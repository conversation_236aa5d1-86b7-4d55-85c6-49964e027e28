{"diagnosis_timestamp": "2025-07-30T01:43:09.633239", "diagnosis_duration_seconds": 1.3829045295715332, "diagnosis_results": {"timestamp_processors": {"gate": {"processor_exists": true, "time_synced": false, "time_offset_ms": 0, "last_sync_time": 0, "sync_age_seconds": -1, "sync_retry_count": 0, "offset_status": "NORMAL", "sync_interval_seconds": 20, "max_offset_threshold": 1000}, "bybit": {"processor_exists": true, "time_synced": false, "time_offset_ms": 0, "last_sync_time": 0, "sync_age_seconds": -1, "sync_retry_count": 0, "offset_status": "NORMAL", "sync_interval_seconds": 20, "max_offset_threshold": 1000}, "okx": {"processor_exists": true, "time_synced": false, "time_offset_ms": 0, "last_sync_time": 0, "sync_age_seconds": -1, "sync_retry_count": 0, "offset_status": "NORMAL", "sync_interval_seconds": 20, "max_offset_threshold": 1000}}, "time_synchronization": {"gate": {"sync_success": true, "time_offset_ms": -391, "sync_age_seconds": 1.3693428039550781, "offset_status": "NORMAL"}, "bybit": {"sync_success": true, "time_offset_ms": -357, "sync_age_seconds": 1.2361202239990234, "offset_status": "NORMAL"}, "okx": {"sync_success": true, "time_offset_ms": -379, "sync_age_seconds": 1.227837324142456, "offset_status": "NORMAL"}}, "websocket_timestamps": {"gate": {"connection_status": "unknown", "client_exists": false}, "bybit": {"connection_status": "unknown", "client_exists": false}, "okx": {"connection_status": "unknown", "client_exists": false}}, "orderbook_sync_validation": {"正常同步": {"time_diff_ms": 100, "is_synced": true, "error_message": "", "expected_result": true}, "边界情况": {"time_diff_ms": 800, "is_synced": true, "error_message": "", "expected_result": true}, "轻微超时": {"time_diff_ms": 1200, "is_synced": false, "error_message": "订单簿数据非同步: 时间差1200.0ms > 1000ms", "expected_result": false}, "严重超时": {"time_diff_ms": 79373, "is_synced": false, "error_message": "订单簿数据非同步: 时间差79373.0ms > 1000ms", "expected_result": false}}, "configuration_parameters": {"TIMESTAMP_TOLERANCE": "未设置", "SYNC_TOLERANCE": "未设置", "ORDERBOOK_TIMEOUT": "未设置", "MAX_TIME_OFFSET_MS": "未设置", "SYNC_INTERVAL_SECONDS": "未设置", "MAX_RETRIES": "未设置", "processor_config": {"sync_interval_seconds": 20, "max_time_offset_ms": 1000, "sync_timeout_seconds": 5, "enable_auto_sync": true, "fallback_to_local": false, "max_retries": 10, "retry_interval_seconds": 1}}}, "root_causes": ["gate时间未同步", "bybit时间未同步", "okx时间未同步", "订单簿时间戳差异过大(79373ms)"], "recommendations": ["强制同步gate时间", "强制同步bybit时间", "强制同步okx时间", "检查WebSocket数据时间戳来源", "验证时间戳格式标准化逻辑"], "severity": "CRITICAL", "summary": {"total_issues": 4, "critical_issues": 1, "config_issues": 0}}