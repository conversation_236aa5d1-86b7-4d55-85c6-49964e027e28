# config/debug_config.py
"""
调试配置模块
控制系统调试模式和日志级别
"""

import os
from enum import Enum

# 🔥 VPS修复：完全禁用调试模式，避免终端刷屏
DEBUG_MODE = False
DEBUG = DEBUG_MODE

# 日志级别
class LogLevel(Enum):
    DEBUG = 10
    INFO = 20
    WARNING = 30
    ERROR = 40
    CRITICAL = 50

# 日志配置
LOG_CONFIG = {
    # 文件日志级别 - 记录所有级别的日志
    "file_level": LogLevel.DEBUG,

    # 🔥 VPS修复：完全禁用终端输出，避免刷屏
    "console_level": 100,  # 设置为极高级别，完全禁用所有终端日志

    # 日志文件目录 - 确保在项目内
    "log_dir": os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "logs"),

    # 模块日志级别配置（可针对不同模块设置不同级别）
    "module_levels": {
        "websocket": LogLevel.DEBUG,  # WebSocket模块记录详细日志
        "exchanges": LogLevel.DEBUG,  # 交易所API详细日志
        "core": LogLevel.INFO,        # 核心业务逻辑
        "trading": LogLevel.INFO,     # 交易执行
        "fund_management": LogLevel.INFO,  # 资金管理
        "monitoring": LogLevel.INFO   # 监控
    },

    # 日志文件大小和备份设置
    "max_file_size": 50 * 1024 * 1024,  # 50MB
    "backup_count": 10,                # 保留10个备份

    # 是否启用彩色终端输出
    "use_rich_console": True,

    # 是否在日志中包含线程信息
    "include_thread_info": True,

    # 是否记录详细的API请求/响应
    "log_api_details": False,  # 设置为True会记录完整的API请求和响应，但可能包含敏感信息

    # WebSocket日志配置
    "websocket": {
        # 是否记录原始WebSocket消息
        "log_raw_messages": False,  # 注意：开启会产生大量日志

        # 是否记录统计信息
        "log_stats": True,

        # 统计信息记录间隔（秒）
        "stats_interval": 60
    }
}

# 异常处理配置
EXCEPTION_CONFIG = {
    # 是否捕获并记录未处理的异常
    "catch_unhandled": True,

    # 是否在异常发生时发送通知
    "notify_on_exception": True,

    # 是否记录详细的堆栈信息
    "detailed_traceback": True
}

# 性能监控配置
PERFORMANCE_CONFIG = {
    # 是否启用性能监控
    "enabled": True,

    # 是否记录函数调用耗时
    "log_function_timing": True,

    # 记录耗时超过多少毫秒的函数调用
    "timing_threshold_ms": 500
}

# 测试辅助函数
def is_debug_mode():
    """返回当前是否为调试模式"""
    return DEBUG_MODE

def get_log_level(module=None):
    """获取指定模块的日志级别"""
    if not module:
        return LOG_CONFIG["file_level"]

    # 查找匹配的模块配置
    for module_prefix, level in LOG_CONFIG["module_levels"].items():
        if module.startswith(module_prefix):
            return level

    # 默认使用文件日志级别
    return LOG_CONFIG["file_level"]
