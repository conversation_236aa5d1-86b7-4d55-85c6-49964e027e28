#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔥 统一余额管理器 - 解决重复余额获取问题
统一管理所有余额查询、缓存和更新，避免重复实现
"""

import logging
import os
import time
import asyncio
from typing import Dict, Any, Optional
from exchanges.exchanges_base import AccountType
# 🔥 6大缓存监控系统
from utils.cache_monitor import log_balance_hit, log_balance_miss, log_balance_update


class UnifiedBalanceManager:
    """🔥 统一余额管理器 - 解决FundManager/ArbitrageEngine重复获取余额的问题"""
    
    _instance = None  # 单例实例
    
    def __new__(cls, *args, **kwargs):
        """单例模式"""
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self, exchanges: Optional[Dict[str, Any]] = None):
        # 避免重复初始化
        if hasattr(self, '_initialized'):
            return
            
        self.logger = logging.getLogger(self.__class__.__name__)
        self.exchanges = exchanges or {}
        
        # 🔥 统一余额缓存
        self.balance_cache: Dict[str, float] = {}
        self.last_update_time = 0
        self.cache_ttl = int(os.getenv("BALANCE_CACHE_TTL", "30"))  # 30秒缓存，支持.env配置
        self.update_lock = asyncio.Lock()
        
        self._initialized = True
        self.logger.info("✅ 统一余额管理器初始化 - 解决重复余额获取问题")
    
    def set_exchanges(self, exchanges: Dict[str, Any]):
        """设置交易所实例"""
        self.exchanges = exchanges
        self.logger.info(f"设置交易所实例: {list(exchanges.keys())}")
    
    async def get_all_balances(self, force_refresh: bool = False) -> Dict[str, float]:
        """
        🔥 统一获取所有余额 - 所有模块统一使用此方法
        
        Args:
            force_refresh: 是否强制刷新缓存
            
        Returns:
            统一格式的余额缓存: {"gate_spot_usdt": 1250.0, "bybit_unified_usdt": 2500.0, ...}
        """
        try:
            current_time = time.time()
            
            # 检查缓存是否有效
            if (not force_refresh and
                self.balance_cache and
                current_time - self.last_update_time < self.cache_ttl):
                self.logger.debug("✅ [缓存命中] 余额缓存: UnifiedBalanceManager | 余额数据从缓存获取，零延迟")

                # 🔥 记录余额缓存命中
                for key, balance in self.balance_cache.items():
                    log_balance_hit("system", key, balance)

                return self.balance_cache
            
            # 需要刷新缓存
            async with self.update_lock:
                # 双重检查，防止并发重复更新
                if (not force_refresh and 
                    current_time - self.last_update_time < self.cache_ttl):
                    return self.balance_cache
                
                self.logger.info("🔍 [API调用] 余额接口: 统一更新 | 缓存过期，真实API查询余额")

                # 🔥 记录余额缓存未命中
                for key in self.balance_cache.keys():
                    log_balance_miss("system", key)

                new_balance_cache = {}
                
                for exchange_name, exchange in self.exchanges.items():
                    try:
                        if hasattr(exchange, 'is_unified_account') and exchange.is_unified_account():
                            # 统一账户
                            balance = await exchange.get_balance(AccountType.UNIFIED)
                            usdt_info = balance.get("USDT", {})
                            
                            if isinstance(usdt_info, dict):
                                available = float(usdt_info.get("available", 0))
                            else:
                                available = float(usdt_info or 0)
                                
                            new_balance_cache[f"{exchange_name}_unified_usdt"] = available
                            self.logger.debug(f"{exchange_name} 统一账户: ${available:.2f}")
                        else:
                            # 分离账户
                            spot_balance = await exchange.get_balance(AccountType.SPOT)
                            futures_balance = await exchange.get_balance(AccountType.FUTURES)

                            spot_usdt_info = spot_balance.get("USDT", {})
                            futures_usdt_info = futures_balance.get("USDT", {})

                            if isinstance(spot_usdt_info, dict):
                                spot_usdt = float(spot_usdt_info.get("available", 0))
                            else:
                                spot_usdt = float(spot_usdt_info or 0)
                                
                            if isinstance(futures_usdt_info, dict):
                                futures_usdt = float(futures_usdt_info.get("available", 0))
                            else:
                                futures_usdt = float(futures_usdt_info or 0)

                            new_balance_cache[f"{exchange_name}_spot_usdt"] = spot_usdt
                            new_balance_cache[f"{exchange_name}_futures_usdt"] = futures_usdt
                            self.logger.debug(f"{exchange_name} 分离账户: 现货${spot_usdt:.2f}, 期货${futures_usdt:.2f}")

                    except Exception as e:
                        self.logger.warning(f"获取{exchange_name}余额失败: {e}")
                        # 保留旧缓存值或设为0
                        if hasattr(exchange, 'is_unified_account') and exchange.is_unified_account():
                            key = f"{exchange_name}_unified_usdt"
                            new_balance_cache[key] = self.balance_cache.get(key, 0.0)
                        else:
                            spot_key = f"{exchange_name}_spot_usdt"
                            futures_key = f"{exchange_name}_futures_usdt"
                            new_balance_cache[spot_key] = self.balance_cache.get(spot_key, 0.0)
                            new_balance_cache[futures_key] = self.balance_cache.get(futures_key, 0.0)
                
                # 🔥 记录余额缓存更新
                for key, new_balance in new_balance_cache.items():
                    old_balance = self.balance_cache.get(key, 0.0)
                    if old_balance != new_balance:
                        log_balance_update("system", key, old_balance, new_balance)

                # 更新缓存
                self.balance_cache = new_balance_cache
                self.last_update_time = current_time

                self.logger.info(f"✅ 余额缓存更新完成: {len(self.balance_cache)}个账户")
                return self.balance_cache
                
        except Exception as e:
            self.logger.error(f"❌ 获取余额失败: {e}")
            return self.balance_cache  # 返回旧缓存
    
    def get_exchange_balance(self, exchange: str, account_type: str = "all") -> float:
        """
        获取特定交易所的余额
        
        Args:
            exchange: 交易所名称
            account_type: 账户类型 ("spot", "futures", "unified", "all")
            
        Returns:
            余额数量
        """
        try:
            if account_type == "all":
                # 返回总余额
                total = 0.0
                for key, balance in self.balance_cache.items():
                    if key.startswith(f"{exchange}_"):
                        total += balance
                return total
            elif account_type == "unified":
                return self.balance_cache.get(f"{exchange}_unified_usdt", 0.0)
            elif account_type == "spot":
                return self.balance_cache.get(f"{exchange}_spot_usdt", 0.0)
            elif account_type == "futures":
                return self.balance_cache.get(f"{exchange}_futures_usdt", 0.0)
            else:
                return 0.0
        except Exception as e:
            self.logger.error(f"获取{exchange}余额失败: {e}")
            return 0.0
    
    def get_total_balance(self) -> float:
        """获取总余额"""
        try:
            return sum(self.balance_cache.values())
        except Exception as e:
            self.logger.error(f"计算总余额失败: {e}")
            return 0.0
    
    def get_balance_summary(self) -> Dict[str, Any]:
        """获取余额汇总"""
        try:
            return {
                "total_usdt": self.get_total_balance(),
                "balances": self.balance_cache.copy(),
                "last_updated": self.last_update_time,
                "cache_age": time.time() - self.last_update_time,
                "source": "UnifiedBalanceManager"
            }
        except Exception as e:
            self.logger.error(f"获取余额汇总失败: {e}")
            return {"error": str(e)}
    
    async def invalidate_cache(self):
        """使缓存失效"""
        self.last_update_time = 0
        self.logger.info("余额缓存已失效")
    
    async def get_balance_unified(self, exchange, account_type: AccountType) -> Dict[str, Any]:
        """
        🔥 统一余额查询接口 - 所有模块使用此方法而非直接调用exchange.get_balance
        
        Args:
            exchange: 交易所实例
            account_type: 账户类型
            
        Returns:
            余额信息
        """
        try:
            return await exchange.get_balance(account_type)
        except Exception as e:
            self.logger.error(f"统一余额查询失败: {e}")
            return {}
    
    async def get_position_unified(self, exchange, symbol: str = None) -> list:
        """
        🔥 统一持仓查询接口 - 所有模块使用此方法而非直接调用exchange.get_position
        
        Args:
            exchange: 交易所实例
            symbol: 交易对符号
            
        Returns:
            持仓信息列表
        """
        try:
            return await exchange.get_position(symbol)
        except Exception as e:
            self.logger.error(f"统一持仓查询失败: {e}")
            return []

    async def cleanup(self):
        """清理资源"""
        self.balance_cache.clear()
        self.logger.info("统一余额管理器清理完成")


# 🔥 全局单例实例
_balance_manager = None

def get_unified_balance_manager(exchanges: Optional[Dict[str, Any]] = None) -> UnifiedBalanceManager:
    """获取统一余额管理器单例"""
    global _balance_manager
    if _balance_manager is None:
        _balance_manager = UnifiedBalanceManager(exchanges)
    elif exchanges:
        _balance_manager.set_exchanges(exchanges)
    return _balance_manager 