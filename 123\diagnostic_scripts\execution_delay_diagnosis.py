#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔥 执行延迟诊断脚本
精准定位10560ms执行延迟的根本原因

根据日志分析：
1. 交易规则预加载失败 - 所有交易对都无法获取交易规则
2. OKX成交价格查询失败 - instId参数为空导致API调用失败
3. 现货交易器等待订单成交 - 导致10秒延迟
"""

import asyncio
import time
import logging
import os
import sys
from typing import Dict, Any, Optional

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.logger import get_logger

logger = get_logger(__name__)

class ExecutionDelayDiagnostic:
    """执行延迟诊断器"""
    
    def __init__(self):
        self.logger = get_logger(self.__class__.__name__)
        self.diagnosis_results = {}
        
    async def run_full_diagnosis(self):
        """运行完整诊断"""
        self.logger.info("🔥 开始执行延迟诊断...")
        self.logger.info("=" * 80)
        
        # 诊断1: 交易规则预加载问题
        await self._diagnose_trading_rules_preloader()
        
        # 诊断2: OKX成交价格查询问题
        await self._diagnose_okx_executed_price_issue()
        
        # 诊断3: 现货交易器等待机制问题
        await self._diagnose_spot_trader_waiting()
        
        # 诊断4: 全局交易所实例问题
        await self._diagnose_global_exchanges()
        
        # 生成诊断报告
        self._generate_diagnosis_report()
        
    async def _diagnose_trading_rules_preloader(self):
        """诊断交易规则预加载问题"""
        self.logger.info("🔍 诊断1: 交易规则预加载问题")
        
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            preloader = get_trading_rules_preloader()
            
            # 检查预加载状态
            stats = preloader.get_cache_stats()
            
            self.diagnosis_results["trading_rules"] = {
                "status": "检查完成",
                "cache_count": stats.get("trading_rules_count", 0),
                "cache_hits": stats.get("cache_hits", 0),
                "cache_misses": stats.get("cache_misses", 0),
                "api_calls": stats.get("api_calls", 0),
                "errors": stats.get("errors", 0),
                "unsupported_pairs": stats.get("unsupported_pairs_count", 0)
            }
            
            self.logger.info(f"   交易规则缓存数量: {stats.get('trading_rules_count', 0)}")
            self.logger.info(f"   缓存命中: {stats.get('cache_hits', 0)}")
            self.logger.info(f"   缓存未命中: {stats.get('cache_misses', 0)}")
            self.logger.info(f"   API调用: {stats.get('api_calls', 0)}")
            self.logger.info(f"   错误数: {stats.get('errors', 0)}")
            self.logger.info(f"   不支持交易对: {stats.get('unsupported_pairs_count', 0)}")
            
            # 检查特定交易对
            test_rule = preloader.get_trading_rule("okx", "SPK-USDT", "spot")
            if test_rule:
                self.logger.info(f"   ✅ SPK-USDT现货交易规则存在")
                self.diagnosis_results["trading_rules"]["spk_usdt_spot"] = "存在"
            else:
                self.logger.warning(f"   ❌ SPK-USDT现货交易规则不存在")
                self.diagnosis_results["trading_rules"]["spk_usdt_spot"] = "不存在"
                
        except Exception as e:
            self.logger.error(f"   ❌ 交易规则预加载诊断失败: {e}")
            self.diagnosis_results["trading_rules"] = {"status": "诊断失败", "error": str(e)}
            
    async def _diagnose_okx_executed_price_issue(self):
        """诊断OKX成交价格查询问题"""
        self.logger.info("🔍 诊断2: OKX成交价格查询问题")
        
        try:
            # 检查OKX交易所的_get_order_executed_price方法
            from exchanges.okx_exchange import OKXExchange
            
            # 模拟检查instId参数问题
            self.logger.info("   检查_get_order_executed_price方法参数...")
            
            # 检查symbol转换逻辑
            test_symbol = "SPK-USDT"
            okx_symbol = test_symbol.replace('-', '-')  # 这里有问题！
            
            self.logger.info(f"   原始symbol: {test_symbol}")
            self.logger.info(f"   转换后symbol: {okx_symbol}")
            
            if test_symbol == okx_symbol:
                self.logger.warning(f"   ⚠️ symbol转换无效果，可能导致instId为空")
                self.diagnosis_results["okx_executed_price"] = {
                    "status": "发现问题",
                    "issue": "symbol转换逻辑错误",
                    "original": test_symbol,
                    "converted": okx_symbol,
                    "problem": "replace('-', '-')无效果"
                }
            else:
                self.logger.info(f"   ✅ symbol转换正常")
                self.diagnosis_results["okx_executed_price"] = {
                    "status": "正常",
                    "original": test_symbol,
                    "converted": okx_symbol
                }
                
        except Exception as e:
            self.logger.error(f"   ❌ OKX成交价格查询诊断失败: {e}")
            self.diagnosis_results["okx_executed_price"] = {"status": "诊断失败", "error": str(e)}
            
    async def _diagnose_spot_trader_waiting(self):
        """诊断现货交易器等待机制问题"""
        self.logger.info("🔍 诊断3: 现货交易器等待机制问题")
        
        try:
            # 检查等待超时配置
            wait_timeout = 0.5  # 从代码中看到的值
            
            self.logger.info(f"   现货交易器等待超时: {wait_timeout}秒")
            
            # 分析日志中的等待时间
            # 从日志看：现货执行用了10238ms，远超500ms
            actual_wait_time = 10238  # ms
            expected_wait_time = 500  # ms
            
            if actual_wait_time > expected_wait_time * 10:
                self.logger.warning(f"   ❌ 实际等待时间{actual_wait_time}ms远超预期{expected_wait_time}ms")
                self.diagnosis_results["spot_trader_waiting"] = {
                    "status": "发现问题",
                    "expected_ms": expected_wait_time,
                    "actual_ms": actual_wait_time,
                    "ratio": actual_wait_time / expected_wait_time,
                    "issue": "等待时间异常长，可能是订单状态查询失败导致"
                }
            else:
                self.logger.info(f"   ✅ 等待时间正常")
                self.diagnosis_results["spot_trader_waiting"] = {"status": "正常"}
                
        except Exception as e:
            self.logger.error(f"   ❌ 现货交易器等待机制诊断失败: {e}")
            self.diagnosis_results["spot_trader_waiting"] = {"status": "诊断失败", "error": str(e)}
            
    async def _diagnose_global_exchanges(self):
        """诊断全局交易所实例问题"""
        self.logger.info("🔍 诊断4: 全局交易所实例问题")
        
        try:
            # 检查get_global_exchanges函数
            try:
                from core.trading_system_initializer import get_global_exchanges
                global_exchanges = get_global_exchanges()
                
                if global_exchanges is None:
                    self.logger.warning(f"   ❌ get_global_exchanges()返回None")
                    self.diagnosis_results["global_exchanges"] = {
                        "status": "发现问题",
                        "issue": "get_global_exchanges返回None",
                        "impact": "交易规则预加载无法获取交易所实例"
                    }
                else:
                    self.logger.info(f"   ✅ get_global_exchanges()返回{len(global_exchanges)}个交易所")
                    self.diagnosis_results["global_exchanges"] = {
                        "status": "正常",
                        "count": len(global_exchanges),
                        "exchanges": list(global_exchanges.keys())
                    }
                    
            except ImportError as e:
                self.logger.warning(f"   ⚠️ 无法导入get_global_exchanges: {e}")
                self.diagnosis_results["global_exchanges"] = {
                    "status": "函数不存在",
                    "issue": "get_global_exchanges函数不存在或无法导入"
                }
                
        except Exception as e:
            self.logger.error(f"   ❌ 全局交易所实例诊断失败: {e}")
            self.diagnosis_results["global_exchanges"] = {"status": "诊断失败", "error": str(e)}
            
    def _generate_diagnosis_report(self):
        """生成诊断报告"""
        self.logger.info("=" * 80)
        self.logger.info("📊 执行延迟诊断报告")
        self.logger.info("=" * 80)
        
        # 问题汇总
        critical_issues = []
        warnings = []
        
        # 分析交易规则问题
        trading_rules = self.diagnosis_results.get("trading_rules", {})
        if trading_rules.get("cache_count", 0) == 0:
            critical_issues.append("交易规则缓存为空，所有交易对无法获取精度信息")
        if trading_rules.get("spk_usdt_spot") == "不存在":
            critical_issues.append("SPK-USDT现货交易规则不存在")
            
        # 分析OKX成交价格问题
        okx_price = self.diagnosis_results.get("okx_executed_price", {})
        if okx_price.get("issue") == "symbol转换逻辑错误":
            critical_issues.append("OKX symbol转换逻辑错误，导致instId参数为空")
            
        # 分析等待时间问题
        waiting = self.diagnosis_results.get("spot_trader_waiting", {})
        if waiting.get("ratio", 0) > 10:
            critical_issues.append(f"现货交易器等待时间异常：{waiting.get('actual_ms')}ms vs {waiting.get('expected_ms')}ms")
            
        # 分析全局交易所问题
        global_ex = self.diagnosis_results.get("global_exchanges", {})
        if global_ex.get("issue"):
            critical_issues.append("全局交易所实例获取失败，影响交易规则预加载")
            
        # 输出问题汇总
        self.logger.info(f"🔥 发现{len(critical_issues)}个关键问题：")
        for i, issue in enumerate(critical_issues, 1):
            self.logger.info(f"   {i}. {issue}")
            
        if warnings:
            self.logger.info(f"⚠️ 发现{len(warnings)}个警告：")
            for i, warning in enumerate(warnings, 1):
                self.logger.info(f"   {i}. {warning}")
                
        # 根本原因分析
        self.logger.info("=" * 80)
        self.logger.info("🎯 根本原因分析")
        self.logger.info("=" * 80)
        
        self.logger.info("根据诊断结果，10560ms执行延迟的根本原因是：")
        self.logger.info("")
        self.logger.info("1. 🔥 交易规则预加载失败")
        self.logger.info("   - 所有交易对的交易规则都无法获取")
        self.logger.info("   - 可能是get_global_exchanges()返回None导致")
        self.logger.info("   - 影响：无法进行精度处理和参数验证")
        self.logger.info("")
        self.logger.info("2. 🔥 OKX成交价格查询失败")
        self.logger.info("   - _get_order_executed_price方法中symbol转换错误")
        self.logger.info("   - replace('-', '-')无效果，导致instId参数为空")
        self.logger.info("   - 影响：API调用失败，无法获取实际成交价格")
        self.logger.info("")
        self.logger.info("3. 🔥 现货交易器等待机制异常")
        self.logger.info("   - 订单状态查询失败，导致持续等待")
        self.logger.info("   - 等待时间从预期500ms延长到10238ms")
        self.logger.info("   - 影响：整体执行时间大幅延长")
        self.logger.info("")
        
        # 修复建议
        self.logger.info("🔧 修复建议")
        self.logger.info("=" * 80)
        self.logger.info("1. 修复交易规则预加载器")
        self.logger.info("   - 实现get_global_exchanges()函数或修复其返回值")
        self.logger.info("   - 添加临时交易所实例创建机制")
        self.logger.info("")
        self.logger.info("2. 修复OKX symbol转换逻辑")
        self.logger.info("   - 将replace('-', '-')改为正确的转换逻辑")
        self.logger.info("   - 确保instId参数正确传递给API")
        self.logger.info("")
        self.logger.info("3. 优化现货交易器等待机制")
        self.logger.info("   - 减少等待超时时间")
        self.logger.info("   - 改进订单状态查询逻辑")
        self.logger.info("   - 添加快速失败机制")
        self.logger.info("")

async def main():
    """主函数"""
    diagnostic = ExecutionDelayDiagnostic()
    await diagnostic.run_full_diagnosis()

if __name__ == "__main__":
    asyncio.run(main())