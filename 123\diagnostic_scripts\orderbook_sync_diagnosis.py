#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔥 订单簿同步验证失败诊断脚本
精准定位79373.0ms时间差问题的根本原因

根据用户报告：
2025-07-29 18:57:53.425 [WARNING] [ExecutionEngine] ⚠️ 订单簿同步验证最终失败: 订单簿数据非同步: 时间差79373.0ms > 1000ms

诊断目标：
1. 检查三个交易所的时间戳同步状态
2. 分析订单簿数据的时间戳来源和格式
3. 验证时间戳处理器的配置和工作状态
4. 检查WebSocket数据流的时间戳一致性
5. 定位79373.0ms时间差的具体来源
"""

import asyncio
import time
import os
import sys
import json
from typing import Dict, List, Any, Optional
from datetime import datetime

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

try:
    from websocket.unified_timestamp_processor import get_timestamp_processor, sync_all_exchanges
    from websocket.orderbook_validator import validate_orderbook_synchronization
    from utils.logger import get_logger
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    print(f"项目根目录: {project_root}")
    print("请确保在项目根目录下运行此脚本")
    sys.exit(1)

logger = get_logger(__name__)


class OrderbookSyncDiagnostic:
    """订单簿同步诊断器"""
    
    def __init__(self):
        self.logger = get_logger(self.__class__.__name__)
        self.exchanges = ["gate", "bybit", "okx"]
        self.diagnosis_results = {}
        
    async def run_comprehensive_diagnosis(self) -> Dict[str, Any]:
        """运行全面诊断"""
        self.logger.info("=" * 80)
        self.logger.info("🔥 开始订单簿同步验证失败诊断")
        self.logger.info("=" * 80)
        
        diagnosis_start = time.time()
        
        # 1. 时间戳处理器状态诊断
        await self._diagnose_timestamp_processors()
        
        # 2. 时间同步状态诊断
        await self._diagnose_time_synchronization()
        
        # 3. WebSocket数据时间戳诊断
        await self._diagnose_websocket_timestamps()
        
        # 4. 订单簿同步验证诊断
        await self._diagnose_orderbook_sync_validation()
        
        # 5. 配置参数诊断
        await self._diagnose_configuration_parameters()
        
        # 6. 生成诊断报告
        diagnosis_duration = time.time() - diagnosis_start
        report = self._generate_diagnosis_report(diagnosis_duration)
        
        self.logger.info("=" * 80)
        self.logger.info("🎯 订单簿同步诊断完成")
        self.logger.info("=" * 80)
        
        return report
    
    async def _diagnose_timestamp_processors(self):
        """诊断时间戳处理器状态"""
        self.logger.info("🔍 步骤1: 诊断时间戳处理器状态")
        
        processor_status = {}
        
        for exchange in self.exchanges:
            try:
                processor = get_timestamp_processor(exchange)
                status = processor.get_sync_status()
                
                processor_status[exchange] = {
                    "processor_exists": True,
                    "time_synced": status["time_synced"],
                    "time_offset_ms": status["time_offset_ms"],
                    "last_sync_time": status["last_sync_time"],
                    "sync_age_seconds": status["sync_age_seconds"],
                    "sync_retry_count": status.get("sync_retry_count", 0),
                    "offset_status": status.get("offset_status", "UNKNOWN"),
                    "sync_interval_seconds": status.get("sync_interval_seconds", 0),
                    "max_offset_threshold": status.get("max_offset_threshold", 0)
                }
                
                self.logger.info(f"✅ {exchange}: 时间戳处理器状态正常")
                self.logger.info(f"   同步状态: {status['time_synced']}")
                self.logger.info(f"   时间偏移: {status['time_offset_ms']}ms")
                self.logger.info(f"   偏移状态: {status.get('offset_status', 'UNKNOWN')}")
                
            except Exception as e:
                processor_status[exchange] = {
                    "processor_exists": False,
                    "error": str(e)
                }
                self.logger.error(f"❌ {exchange}: 时间戳处理器异常 - {e}")
        
        self.diagnosis_results["timestamp_processors"] = processor_status
    
    async def _diagnose_time_synchronization(self):
        """诊断时间同步状态"""
        self.logger.info("🔍 步骤2: 诊断时间同步状态")
        
        sync_results = {}
        
        try:
            # 强制同步所有交易所
            self.logger.info("🔄 强制同步所有交易所时间...")
            sync_results_raw = await sync_all_exchanges(force=True)
            
            for exchange, success in sync_results_raw.items():
                processor = get_timestamp_processor(exchange)
                status = processor.get_sync_status()
                
                sync_results[exchange] = {
                    "sync_success": success,
                    "time_offset_ms": status["time_offset_ms"],
                    "sync_age_seconds": status["sync_age_seconds"],
                    "offset_status": status.get("offset_status", "UNKNOWN")
                }
                
                if success:
                    self.logger.info(f"✅ {exchange}: 时间同步成功，偏移={status['time_offset_ms']}ms")
                else:
                    self.logger.error(f"❌ {exchange}: 时间同步失败")
                    
        except Exception as e:
            self.logger.error(f"❌ 时间同步诊断异常: {e}")
            sync_results["error"] = str(e)
        
        self.diagnosis_results["time_synchronization"] = sync_results
    
    async def _diagnose_websocket_timestamps(self):
        """诊断WebSocket数据时间戳"""
        self.logger.info("🔍 步骤3: 诊断WebSocket数据时间戳")
        
        websocket_diagnosis = {}
        
        try:
            # 尝试获取WebSocket管理器
            from websocket.ws_manager import WebSocketManager
            ws_manager = WebSocketManager()
            
            # 检查WebSocket连接状态
            for exchange in self.exchanges:
                try:
                    # 检查连接状态
                    connection_status = "unknown"
                    if hasattr(ws_manager, 'clients') and exchange in ws_manager.clients:
                        client = ws_manager.clients[exchange]
                        if hasattr(client, 'connected'):
                            connection_status = "connected" if client.connected else "disconnected"
                    
                    websocket_diagnosis[exchange] = {
                        "connection_status": connection_status,
                        "client_exists": hasattr(ws_manager, 'clients') and exchange in ws_manager.clients
                    }
                    
                    self.logger.info(f"📊 {exchange}: WebSocket状态 - {connection_status}")
                    
                except Exception as e:
                    websocket_diagnosis[exchange] = {
                        "error": str(e)
                    }
                    self.logger.error(f"❌ {exchange}: WebSocket诊断异常 - {e}")
            
        except Exception as e:
            self.logger.error(f"❌ WebSocket诊断异常: {e}")
            websocket_diagnosis["error"] = str(e)
        
        self.diagnosis_results["websocket_timestamps"] = websocket_diagnosis
    
    async def _diagnose_orderbook_sync_validation(self):
        """诊断订单簿同步验证逻辑"""
        self.logger.info("🔍 步骤4: 诊断订单簿同步验证逻辑")
        
        validation_diagnosis = {}
        
        try:
            # 模拟订单簿数据，测试同步验证逻辑
            current_time = int(time.time() * 1000)
            
            # 测试不同时间差的订单簿数据
            test_cases = [
                {"name": "正常同步", "time_diff": 100},  # 100ms差异
                {"name": "边界情况", "time_diff": 800},  # 800ms差异（阈值边界）
                {"name": "轻微超时", "time_diff": 1200},  # 1200ms差异
                {"name": "严重超时", "time_diff": 79373},  # 79373ms差异（用户报告的问题）
            ]
            
            for test_case in test_cases:
                spot_orderbook = {
                    "asks": [[100.0, 1.0]],
                    "bids": [[99.0, 1.0]],
                    "timestamp": current_time
                }
                
                futures_orderbook = {
                    "asks": [[100.1, 1.0]],
                    "bids": [[99.1, 1.0]],
                    "timestamp": current_time - test_case["time_diff"]  # 模拟时间差
                }
                
                # 测试同步验证
                is_synced, error_msg = validate_orderbook_synchronization(
                    spot_orderbook, futures_orderbook, max_time_diff_ms=1000
                )
                
                validation_diagnosis[test_case["name"]] = {
                    "time_diff_ms": test_case["time_diff"],
                    "is_synced": is_synced,
                    "error_message": error_msg,
                    "expected_result": test_case["time_diff"] <= 1000
                }
                
                status = "✅ 通过" if is_synced == (test_case["time_diff"] <= 1000) else "❌ 异常"
                self.logger.info(f"{status} {test_case['name']}: 时间差{test_case['time_diff']}ms, 结果={is_synced}")
                
        except Exception as e:
            self.logger.error(f"❌ 订单簿同步验证诊断异常: {e}")
            validation_diagnosis["error"] = str(e)
        
        self.diagnosis_results["orderbook_sync_validation"] = validation_diagnosis
    
    async def _diagnose_configuration_parameters(self):
        """诊断配置参数"""
        self.logger.info("🔍 步骤5: 诊断配置参数")
        
        config_diagnosis = {}
        
        # 检查关键配置参数
        key_configs = [
            "TIMESTAMP_TOLERANCE",
            "SYNC_TOLERANCE", 
            "ORDERBOOK_TIMEOUT",
            "MAX_TIME_OFFSET_MS",
            "SYNC_INTERVAL_SECONDS",
            "MAX_RETRIES"
        ]
        
        for config_key in key_configs:
            value = os.getenv(config_key, "未设置")
            config_diagnosis[config_key] = value
            self.logger.info(f"📋 {config_key}: {value}")
        
        # 检查时间戳处理器配置
        try:
            processor = get_timestamp_processor("gate")  # 使用gate作为示例
            if hasattr(processor, 'config'):
                config_diagnosis["processor_config"] = {
                    "sync_interval_seconds": processor.config.sync_interval_seconds,
                    "max_time_offset_ms": processor.config.max_time_offset_ms,
                    "sync_timeout_seconds": processor.config.sync_timeout_seconds,
                    "enable_auto_sync": processor.config.enable_auto_sync,
                    "fallback_to_local": processor.config.fallback_to_local,
                    "max_retries": processor.config.max_retries,
                    "retry_interval_seconds": processor.config.retry_interval_seconds
                }
                
                self.logger.info("📋 时间戳处理器配置:")
                self.logger.info(f"   同步间隔: {processor.config.sync_interval_seconds}秒")
                self.logger.info(f"   最大偏移: {processor.config.max_time_offset_ms}ms")
                self.logger.info(f"   最大重试: {processor.config.max_retries}次")
                self.logger.info(f"   本地回退: {processor.config.fallback_to_local}")
                
        except Exception as e:
            config_diagnosis["processor_config_error"] = str(e)
            self.logger.error(f"❌ 时间戳处理器配置诊断异常: {e}")
        
        self.diagnosis_results["configuration_parameters"] = config_diagnosis
    
    def _generate_diagnosis_report(self, diagnosis_duration: float) -> Dict[str, Any]:
        """生成诊断报告"""
        self.logger.info("📊 生成诊断报告...")
        
        # 分析问题根源
        root_causes = []
        recommendations = []
        
        # 分析时间戳处理器问题
        if "timestamp_processors" in self.diagnosis_results:
            processors = self.diagnosis_results["timestamp_processors"]
            for exchange, status in processors.items():
                if not status.get("processor_exists", False):
                    root_causes.append(f"{exchange}时间戳处理器不存在")
                    recommendations.append(f"初始化{exchange}时间戳处理器")
                elif not status.get("time_synced", False):
                    root_causes.append(f"{exchange}时间未同步")
                    recommendations.append(f"强制同步{exchange}时间")
                elif abs(status.get("time_offset_ms", 0)) > 1000:
                    root_causes.append(f"{exchange}时间偏移过大: {status.get('time_offset_ms', 0)}ms")
                    recommendations.append(f"检查{exchange}网络连接和API状态")
        
        # 分析订单簿同步验证问题
        if "orderbook_sync_validation" in self.diagnosis_results:
            validation = self.diagnosis_results["orderbook_sync_validation"]
            if "严重超时" in validation:
                severe_case = validation["严重超时"]
                if not severe_case.get("is_synced", True):
                    root_causes.append("订单簿时间戳差异过大(79373ms)")
                    recommendations.append("检查WebSocket数据时间戳来源")
                    recommendations.append("验证时间戳格式标准化逻辑")
        
        # 分析配置问题
        if "configuration_parameters" in self.diagnosis_results:
            config = self.diagnosis_results["configuration_parameters"]
            processor_config = config.get("processor_config", {})
            if processor_config.get("fallback_to_local", True):
                root_causes.append("时间戳处理器配置为回退本地时间")
                recommendations.append("设置fallback_to_local=False，按修复提示词要求")
            if processor_config.get("max_retries", 0) < 10:
                root_causes.append(f"重试次数不足: {processor_config.get('max_retries', 0)}")
                recommendations.append("增加重试次数到10次，按修复提示词要求")
        
        # 生成最终报告
        report = {
            "diagnosis_timestamp": datetime.now().isoformat(),
            "diagnosis_duration_seconds": diagnosis_duration,
            "diagnosis_results": self.diagnosis_results,
            "root_causes": root_causes,
            "recommendations": recommendations,
            "severity": "CRITICAL" if len(root_causes) > 3 else "HIGH" if len(root_causes) > 1 else "MEDIUM",
            "summary": {
                "total_issues": len(root_causes),
                "critical_issues": len([c for c in root_causes if "79373ms" in c or "不存在" in c]),
                "config_issues": len([c for c in root_causes if "配置" in c or "回退" in c or "重试" in c])
            }
        }
        
        # 输出报告摘要
        self.logger.info("📊 诊断报告摘要:")
        self.logger.info(f"   诊断耗时: {diagnosis_duration:.2f}秒")
        self.logger.info(f"   问题严重程度: {report['severity']}")
        self.logger.info(f"   发现问题数量: {len(root_causes)}")
        self.logger.info(f"   关键问题数量: {report['summary']['critical_issues']}")
        
        if root_causes:
            self.logger.info("🔥 主要问题:")
            for i, cause in enumerate(root_causes[:5], 1):
                self.logger.info(f"   {i}. {cause}")
        
        if recommendations:
            self.logger.info("💡 修复建议:")
            for i, rec in enumerate(recommendations[:5], 1):
                self.logger.info(f"   {i}. {rec}")
        
        return report
    
    def save_diagnosis_report(self, report: Dict[str, Any], filename: str = None):
        """保存诊断报告到文件"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"orderbook_sync_diagnosis_{timestamp}.json"
        
        # 确保目录存在
        script_dir = os.path.dirname(os.path.abspath(__file__))
        filepath = os.path.join(script_dir, filename)
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"📄 诊断报告已保存: {filepath}")
            return filepath
            
        except Exception as e:
            self.logger.error(f"❌ 保存诊断报告失败: {e}")
            return None


async def main():
    """主函数"""
    print("🔥 订单簿同步验证失败诊断脚本")
    print("=" * 60)
    
    diagnostic = OrderbookSyncDiagnostic()
    
    try:
        # 运行诊断
        report = await diagnostic.run_comprehensive_diagnosis()
        
        # 保存报告
        report_file = diagnostic.save_diagnosis_report(report)
        
        print("\n" + "=" * 60)
        print("🎯 诊断完成")
        print(f"📊 问题严重程度: {report['severity']}")
        print(f"🔍 发现问题: {len(report['root_causes'])}个")
        if report_file:
            print(f"📄 详细报告: {report_file}")
        print("=" * 60)
        
        return report
        
    except Exception as e:
        print(f"❌ 诊断过程异常: {e}")
        import traceback
        traceback.print_exc()
        return None


if __name__ == "__main__":
    asyncio.run(main())