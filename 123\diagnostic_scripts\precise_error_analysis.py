#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精准错误分析脚本 - 深度分析2025-07-30错误日志
按照修复提示词要求进行深度审查和精准定位
"""

import asyncio
import logging
import sys
import os
import time
import json
from typing import Dict, List, Any, Optional, Set
from collections import defaultdict, Counter

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 🔥 修复：加载.env文件
from dotenv import load_dotenv
load_dotenv("123/.env")

from utils.logger import get_logger

class PreciseErrorAnalysis:
    """精准错误分析器"""
    
    def __init__(self):
        self.logger = get_logger(self.__class__.__name__)
        self.error_patterns = {}
        self.analysis_results = {}
        
    def analyze_error_log_detailed(self, log_file_path: str):
        """深度分析错误日志"""
        self.logger.info("🔍 开始深度分析错误日志...")
        
        try:
            with open(log_file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                
            # 分类错误
            trading_rule_errors = []
            margin_calculator_errors = []
            api_errors = []
            websocket_errors = []
            other_errors = []
            
            for line_num, line in enumerate(lines, 1):
                line = line.strip()
                if not line:
                    continue
                    
                # 分类错误类型
                if "无法获取交易规则" in line:
                    trading_rule_errors.append((line_num, line))
                elif "获取合约信息失败" in line:
                    margin_calculator_errors.append((line_num, line))
                elif "API" in line and ("失败" in line or "错误" in line):
                    api_errors.append((line_num, line))
                elif "WebSocket" in line or "ws" in line.lower():
                    websocket_errors.append((line_num, line))
                elif any(keyword in line for keyword in ["ERROR", "CRITICAL", "❌", "失败"]):
                    other_errors.append((line_num, line))
                    
            # 详细分析每种错误
            self._analyze_trading_rule_errors_detailed(trading_rule_errors)
            self._analyze_margin_calculator_errors_detailed(margin_calculator_errors)
            self._analyze_api_errors_detailed(api_errors)
            
            # 生成分析报告
            self._generate_analysis_report()
            
        except Exception as e:
            self.logger.error(f"❌ 分析错误日志失败: {e}")
            
    def _analyze_trading_rule_errors_detailed(self, errors: List[tuple]):
        """详细分析交易规则错误"""
        self.logger.info("🔍 详细分析交易规则错误...")
        
        # 提取错误的交易对信息
        error_symbols = Counter()
        error_exchanges = Counter()
        error_markets = Counter()
        error_combinations = Counter()
        
        for line_num, error_line in errors:
            # 解析错误信息：SPK-USDT_gate_spot
            if "无法获取交易规则:" in error_line:
                parts = error_line.split("无法获取交易规则:")
                if len(parts) > 1:
                    error_key = parts[1].strip()
                    
                    # 解析 SPK-USDT_gate_spot 格式
                    if "_" in error_key:
                        key_parts = error_key.split("_")
                        if len(key_parts) >= 3:
                            symbol = key_parts[0]
                            exchange = key_parts[1]
                            market = key_parts[2]
                            
                            error_symbols[symbol] += 1
                            error_exchanges[exchange] += 1
                            error_markets[market] += 1
                            error_combinations[f"{exchange}_{market}"] += 1
                            
        # 统计分析
        self.analysis_results["trading_rules"] = {
            "total_errors": len(errors),
            "error_symbols": dict(error_symbols.most_common()),
            "error_exchanges": dict(error_exchanges.most_common()),
            "error_markets": dict(error_markets.most_common()),
            "error_combinations": dict(error_combinations.most_common()),
            "most_problematic_symbol": error_symbols.most_common(1)[0] if error_symbols else None,
            "most_problematic_exchange": error_exchanges.most_common(1)[0] if error_exchanges else None
        }
        
        self.logger.info(f"📊 交易规则错误详细分析:")
        self.logger.info(f"   总错误数: {len(errors)}")
        self.logger.info(f"   涉及交易对: {len(error_symbols)}个")
        self.logger.info(f"   涉及交易所: {len(error_exchanges)}个")
        self.logger.info(f"   最问题交易对: {error_symbols.most_common(3)}")
        self.logger.info(f"   最问题交易所: {error_exchanges.most_common()}")
        
    def _analyze_margin_calculator_errors_detailed(self, errors: List[tuple]):
        """详细分析保证金计算错误"""
        self.logger.info("🔍 详细分析保证金计算错误...")
        
        okx_errors = []
        other_errors = []
        
        for line_num, error_line in errors:
            if "okx_" in error_line.lower():
                # 提取OKX相关错误
                if "获取合约信息失败" in error_line:
                    # 解析: okx_ICNT-USDT | 获取合约信息失败
                    parts = error_line.split("|")
                    if len(parts) > 0:
                        okx_part = parts[0].strip()
                        if "okx_" in okx_part:
                            symbol_part = okx_part.split("okx_")[-1].strip()
                            okx_errors.append(symbol_part)
            else:
                other_errors.append(error_line)
                
        self.analysis_results["margin_calculator"] = {
            "total_errors": len(errors),
            "okx_errors": len(okx_errors),
            "okx_symbols": list(set(okx_errors)),
            "other_errors": len(other_errors)
        }
        
        self.logger.info(f"📊 保证金计算错误详细分析:")
        self.logger.info(f"   总错误数: {len(errors)}")
        self.logger.info(f"   OKX错误数: {len(okx_errors)}")
        self.logger.info(f"   OKX问题交易对: {list(set(okx_errors))}")
        
    def _analyze_api_errors_detailed(self, errors: List[tuple]):
        """详细分析API错误"""
        self.logger.info("🔍 详细分析API错误...")
        
        api_error_types = Counter()
        for line_num, error_line in errors:
            if "timeout" in error_line.lower():
                api_error_types["timeout"] += 1
            elif "rate limit" in error_line.lower() or "频率" in error_line:
                api_error_types["rate_limit"] += 1
            elif "connection" in error_line.lower() or "连接" in error_line:
                api_error_types["connection"] += 1
            elif "authentication" in error_line.lower() or "认证" in error_line:
                api_error_types["auth"] += 1
            else:
                api_error_types["other"] += 1
                
        self.analysis_results["api_errors"] = {
            "total_errors": len(errors),
            "error_types": dict(api_error_types)
        }
        
    def _generate_analysis_report(self):
        """生成分析报告"""
        self.logger.info("📋 生成错误分析报告...")
        
        # 根本原因分析
        root_causes = []
        
        # 1. 交易规则错误分析
        if "trading_rules" in self.analysis_results:
            tr_data = self.analysis_results["trading_rules"]
            
            # 检查是否是特定代币问题
            if tr_data.get("most_problematic_symbol"):
                symbol, count = tr_data["most_problematic_symbol"]
                if count > tr_data["total_errors"] * 0.3:  # 超过30%的错误来自同一代币
                    root_causes.append(f"特定代币问题: {symbol} 占{count}/{tr_data['total_errors']}个错误")
                    
            # 检查是否是特定交易所问题
            if tr_data.get("most_problematic_exchange"):
                exchange, count = tr_data["most_problematic_exchange"]
                if count > tr_data["total_errors"] * 0.4:  # 超过40%的错误来自同一交易所
                    root_causes.append(f"特定交易所问题: {exchange} 占{count}/{tr_data['total_errors']}个错误")
                    
        # 2. 保证金计算错误分析
        if "margin_calculator" in self.analysis_results:
            mc_data = self.analysis_results["margin_calculator"]
            if mc_data["okx_errors"] > 0:
                root_causes.append(f"OKX保证金计算问题: {mc_data['okx_errors']}个错误")
                
        # 3. 综合分析
        if not root_causes:
            root_causes.append("系统性问题: 可能是网络、配置或API密钥问题")
            
        self.analysis_results["root_causes"] = root_causes
        
        self.logger.info("🎯 根本原因分析:")
        for i, cause in enumerate(root_causes, 1):
            self.logger.info(f"   {i}. {cause}")
            
    async def check_system_components(self):
        """检查系统组件状态"""
        self.logger.info("🔍 检查系统组件状态...")
        
        component_status = {}
        
        # 1. 检查环境变量配置
        required_env_vars = [
            "TARGET_SYMBOLS", "TRADING_RULES_TTL", "HEDGE_QUALITY_TTL",
            "PRECISION_CACHE_TTL", "MIN_ORDER_AMOUNT_USD", "MAX_ORDER_AMOUNT_USD"
        ]
        
        missing_vars = []
        for var in required_env_vars:
            if not os.getenv(var):
                missing_vars.append(var)
                
        component_status["env_config"] = {
            "status": "OK" if not missing_vars else "ERROR",
            "missing_vars": missing_vars
        }
        
        # 2. 检查TARGET_SYMBOLS配置
        target_symbols = os.getenv("TARGET_SYMBOLS", "")
        if target_symbols:
            symbols = [s.strip() for s in target_symbols.split(",")]
            component_status["target_symbols"] = {
                "status": "OK",
                "count": len(symbols),
                "symbols": symbols
            }
        else:
            component_status["target_symbols"] = {
                "status": "ERROR",
                "message": "TARGET_SYMBOLS未配置"
            }
            
        # 3. 检查核心模块导入
        try:
            from core.universal_token_system import get_universal_token_system
            token_system = get_universal_token_system()
            component_status["universal_token_system"] = {
                "status": "OK",
                "supported_symbols": len(token_system.get_supported_symbols())
            }
        except Exception as e:
            component_status["universal_token_system"] = {
                "status": "ERROR",
                "error": str(e)
            }
            
        # 4. 检查交易规则预加载器
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            preloader = get_trading_rules_preloader()
            component_status["trading_rules_preloader"] = {
                "status": "OK",
                "cache_stats": preloader.get_cache_stats()
            }
        except Exception as e:
            component_status["trading_rules_preloader"] = {
                "status": "ERROR",
                "error": str(e)
            }
            
        self.analysis_results["component_status"] = component_status
        
        # 输出检查结果
        self.logger.info("📊 系统组件状态:")
        for component, status in component_status.items():
            status_icon = "✅" if status.get("status") == "OK" else "❌"
            self.logger.info(f"   {status_icon} {component}: {status.get('status', 'UNKNOWN')}")
            
    def generate_fix_recommendations(self):
        """生成精准修复建议"""
        self.logger.info("💡 生成精准修复建议...")
        
        recommendations = []
        
        # 基于根本原因分析生成建议
        if "root_causes" in self.analysis_results:
            for cause in self.analysis_results["root_causes"]:
                if "特定代币问题" in cause:
                    recommendations.append({
                        "priority": "HIGH",
                        "category": "代币配置",
                        "action": "检查并移除不支持的代币",
                        "details": "从TARGET_SYMBOLS中移除问题代币，或添加交易所支持性检查"
                    })
                elif "特定交易所问题" in cause:
                    recommendations.append({
                        "priority": "HIGH", 
                        "category": "交易所配置",
                        "action": "检查交易所API配置和网络连接",
                        "details": "验证API密钥、检查网络连接、增加重试机制"
                    })
                elif "OKX保证金计算问题" in cause:
                    recommendations.append({
                        "priority": "MEDIUM",
                        "category": "保证金计算",
                        "action": "修复OKX保证金计算逻辑",
                        "details": "检查OKX合约信息获取API，增加错误处理"
                    })
                    
        # 基于组件状态生成建议
        if "component_status" in self.analysis_results:
            comp_status = self.analysis_results["component_status"]
            
            if comp_status.get("env_config", {}).get("status") == "ERROR":
                recommendations.append({
                    "priority": "CRITICAL",
                    "category": "环境配置",
                    "action": "配置缺失的环境变量",
                    "details": f"添加缺失的环境变量: {comp_status['env_config'].get('missing_vars', [])}"
                })
                
        # 通用建议
        recommendations.extend([
            {
                "priority": "MEDIUM",
                "category": "系统优化",
                "action": "实施交易对支持性预检查",
                "details": "在系统启动时检查所有交易对在各交易所的支持情况"
            },
            {
                "priority": "LOW",
                "category": "监控优化",
                "action": "增强错误监控和告警",
                "details": "添加更详细的错误分类和自动告警机制"
            }
        ])
        
        self.analysis_results["recommendations"] = recommendations
        
        # 输出建议
        self.logger.info("🔧 精准修复建议:")
        for i, rec in enumerate(recommendations, 1):
            self.logger.info(f"   {i}. [{rec['priority']}] {rec['category']}: {rec['action']}")
            self.logger.info(f"      详情: {rec['details']}")
            
    def save_analysis_results(self, output_file: str = "error_analysis_results.json"):
        """保存分析结果到JSON文件"""
        try:
            output_path = os.path.join("123", "diagnostic_results", output_file)
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # 添加时间戳和元数据
            self.analysis_results["metadata"] = {
                "analysis_time": time.time(),
                "analysis_date": time.strftime("%Y-%m-%d %H:%M:%S"),
                "version": "1.0"
            }
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(self.analysis_results, f, indent=2, ensure_ascii=False)
                
            self.logger.info(f"💾 分析结果已保存到: {output_path}")
            return output_path
            
        except Exception as e:
            self.logger.error(f"❌ 保存分析结果失败: {e}")
            return None
            
    async def run_comprehensive_analysis(self, log_file_path: str):
        """运行综合分析"""
        self.logger.info("🚀 开始综合错误分析...")
        
        # 1. 详细分析错误日志
        self.analyze_error_log_detailed(log_file_path)
        
        # 2. 检查系统组件
        await self.check_system_components()
        
        # 3. 生成修复建议
        self.generate_fix_recommendations()
        
        # 4. 保存分析结果
        result_file = self.save_analysis_results()
        
        # 5. 综合评估
        self.logger.info("📊 综合分析完成:")
        
        # 计算问题严重程度
        critical_issues = 0
        high_issues = 0
        medium_issues = 0
        
        if "recommendations" in self.analysis_results:
            for rec in self.analysis_results["recommendations"]:
                if rec["priority"] == "CRITICAL":
                    critical_issues += 1
                elif rec["priority"] == "HIGH":
                    high_issues += 1
                elif rec["priority"] == "MEDIUM":
                    medium_issues += 1
                    
        self.logger.info(f"   严重问题: {critical_issues}个")
        self.logger.info(f"   高优先级问题: {high_issues}个")
        self.logger.info(f"   中优先级问题: {medium_issues}个")
        
        overall_status = "需要立即修复" if critical_issues > 0 else ("需要修复" if high_issues > 0 else "基本正常")
        self.logger.info(f"   整体状态: {overall_status}")
        
        return {
            "status": overall_status,
            "critical_issues": critical_issues,
            "high_issues": high_issues,
            "medium_issues": medium_issues,
            "result_file": result_file
        }


async def main():
    """主函数"""
    analysis = PreciseErrorAnalysis()
    
    # 错误日志文件路径
    log_file_path = "123/logs/error_20250730.log"
    
    if not os.path.exists(log_file_path):
        analysis.logger.error(f"❌ 错误日志文件不存在: {log_file_path}")
        return
        
    # 运行综合分析
    result = await analysis.run_comprehensive_analysis(log_file_path)
    
    if result["critical_issues"] > 0:
        analysis.logger.warning("⚠️ 发现严重问题，需要立即修复")
    elif result["high_issues"] > 0:
        analysis.logger.warning("⚠️ 发现高优先级问题，建议尽快修复")
    else:
        analysis.logger.info("🎉 系统基本正常，可进行优化")


if __name__ == "__main__":
    asyncio.run(main())