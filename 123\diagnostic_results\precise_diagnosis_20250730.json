{"timestamp": 1753863248.0131195, "problems": {}, "root_causes": {"trading_rules": "无法获取全局交易所实例: cannot import name 'get_global_exchanges' from 'main' (C:\\Users\\<USER>\\Desktop\\63A 修复SSL\\123\\main.py)", "closing_logic": "字段名不匹配：代码检查order_id，API返回id"}, "fix_recommendations": {"trading_rules": {"问题": "交易规则预加载器缺少全局交易所实例", "修复方案": "在get_trading_rule方法中添加临时交易所实例创建和异步加载逻辑", "修复文件": "123/core/trading_rules_preloader.py", "具体修复": "使用_create_temporary_exchange_instance_sync创建临时实例进行API调用"}, "closing_logic": {"问题": "平仓成功判断逻辑中字段名不匹配", "修复方案": "修复unified_closing_manager中的字段检查逻辑", "修复文件": "123/core/unified_closing_manager.py", "具体修复": "检查order_id或id字段，兼容不同API返回格式"}}}