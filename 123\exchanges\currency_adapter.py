#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
🎯 超简单币种适配器 - 只负责格式转换！
真正的零配置：用户只需要在.env中添加交易对，无需任何代码修改！

职责分工：
- CurrencyAdapter：负责交易对格式转换（BTC-USDT <-> BTCUSDT <-> BTC_USDT）
- PriceRangeCalculator：负责价格区间计算和参数确定
"""

import os
from typing import Dict, Any, Tuple, List

class CurrencyAdapter:
    """🚀 零配置币种适配器 - 只负责格式转换，其他全部自动化！"""
    
    def __init__(self):
        # 🔥 修复重复配置读取问题：完全依赖UniversalTokenSystem
        self.supported_symbols = []  # 延迟加载，避免循环导入
        self._token_system = None
        
    def _get_token_system(self):
        """延迟加载UniversalTokenSystem，避免循环导入"""
        if self._token_system is None:
            try:
                from core.universal_token_system import get_universal_token_system
                self._token_system = get_universal_token_system()
            except ImportError:
                # 应急情况下的最小实现
                self._token_system = None
        return self._token_system
    
    def extract_base_currency(self, symbol: str) -> str:
        """从交易对中提取基础币种"""
        # 🔥 修复：检查空字符串
        if not symbol or symbol.strip() == "":
            return "UNKNOWN"

        # 支持所有格式: BTC-USDT, BTC_USDT, BTCUSDT
        if "-" in symbol:
            base = symbol.split("-")[0].upper()
            return base if base else "UNKNOWN"
        elif "_" in symbol:
            base = symbol.split("_")[0].upper()
            return base if base else "UNKNOWN"
        else:
            # 智能识别BTCUSDT格式
            common_quotes = ["USDT", "USDC", "BUSD", "USD", "BTC", "ETH"]
            for quote in common_quotes:
                if symbol.upper().endswith(quote):
                    base = symbol[:-len(quote)].upper()
                    return base if base else "UNKNOWN"
            # 默认取前3位
            if len(symbol) >= 3:
                return symbol[:3].upper()
            else:
                return "UNKNOWN"
    
    def get_exchange_symbol(self, symbol: str, exchange: str, market_type: str = "spot") -> str:
        """转换为交易所特定的交易对格式"""
        base_currency = self.extract_base_currency(symbol)
        
        # 智能提取报价货币
        if "-" in symbol:
            quote = symbol.split("-")[1]
        elif "_" in symbol:
            quote = symbol.split("_")[1]
        else:
            # 从BTCUSDT中提取USDT
            common_quotes = ["USDT", "USDC", "BUSD", "USD"]
            quote = "USDT"  # 默认
            for q in common_quotes:
                if symbol.upper().endswith(q):
                    quote = q
                    break
        
        # 按交易所格式化
        if exchange.lower() == "bybit":
            return f"{base_currency}{quote}"  # BTCUSDT
        elif exchange.lower() == "gate":
            return f"{base_currency}_{quote}"  # BTC_USDT
        elif exchange.lower() == "okx":
            if market_type == "futures":
                return f"{base_currency}-{quote}-SWAP"  # BTC-USDT-SWAP
            else:
                return f"{base_currency}-{quote}"  # BTC-USDT
        else:
            return f"{base_currency}-{quote}"  # 默认格式
    
    def normalize_symbol(self, symbol: str) -> str:
        """标准化交易对格式为 BTC-USDT"""
        # 🔥 修复：检查空字符串
        if not symbol or symbol.strip() == "":
            return "UNKNOWN-USDT"

        base = self.extract_base_currency(symbol)

        # 🔥 修复：检查base是否为空
        if not base or base.strip() == "":
            return "UNKNOWN-USDT"

        # 提取报价货币
        if "-" in symbol:
            quote = symbol.split("-")[1]
        elif "_" in symbol:
            quote = symbol.split("_")[1]
        else:
            # 智能提取
            common_quotes = ["USDT", "USDC", "BUSD", "USD"]
            quote = "USDT"
            for q in common_quotes:
                if symbol.upper().endswith(q):
                    quote = q
                    break

        # 🔥 修复：检查quote是否为空
        if not quote or quote.strip() == "":
            quote = "USDT"

        return f"{base}-{quote}"
    
    def get_supported_symbols(self) -> List[str]:
        """🔥 修复重复配置读取：完全依赖UniversalTokenSystem"""
        token_system = self._get_token_system()
        if token_system:
            return [self.normalize_symbol(symbol.strip()) for symbol in token_system.get_supported_symbols()]
        else:
            # 🔥 修复：应急默认值使用.env配置，避免硬编码
            import os
            default_symbols = os.getenv('TARGET_SYMBOLS', 'ADA-USDT,DOGE-USDT,MATIC-USDT')
            return [symbol.strip() for symbol in default_symbols.split(",") if symbol.strip()]

# 🌟 全局实例
currency_adapter = CurrencyAdapter()

def get_exchange_symbol(symbol: str, exchange: str, market_type: str = "spot") -> str:
    """快速转换交易对格式"""
    return currency_adapter.get_exchange_symbol(symbol, exchange, market_type)

def extract_base_currency(symbol: str) -> str:
    """快速提取基础币种"""
    return currency_adapter.extract_base_currency(symbol)

def normalize_symbol(symbol: str) -> str:
    """快速标准化交易对"""
    return currency_adapter.normalize_symbol(symbol)

if __name__ == '__main__':
    print("🧪 测试币种适配器（仅格式转换）")
    
    test_symbols = ["BTC-USDT", "ETH_USDT", "ADAUSDT", "UNKNOWN-USDT"]
    
    for symbol in test_symbols:
        print(f"\n📊 测试交易对: {symbol}")
        print(f"   基础币种: {extract_base_currency(symbol)}")
        print(f"   标准格式: {normalize_symbol(symbol)}")
        
        # 测试所有交易所格式
        for exchange in ["bybit", "gate", "okx"]:
            spot_format = get_exchange_symbol(symbol, exchange, "spot")
            futures_format = get_exchange_symbol(symbol, exchange, "futures")
            print(f"   {exchange}: 现货={spot_format}, 期货={futures_format}") 