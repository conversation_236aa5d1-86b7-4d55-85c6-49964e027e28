#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
🔥 Gate.io交易所接口实现

支持现货和期货交易，使用统一接口规范
"""

import asyncio
import aiohttp
import hmac
import hashlib
import time
import json
from typing import Dict, List, Optional, Any
import logging
import os

from .exchanges_base import BaseExchange, OrderType, OrderSide, OrderStatus, AccountType
from .currency_adapter import get_exchange_symbol
import urllib.parse
# 🔥 删除未使用的导入：统一管理器由系统统一初始化

# 🔥 统一日志系统 - 使用标准logger获取方式
from utils.logger import get_logger
logger = get_logger(__name__)


class GateExchange(BaseExchange):
    """Gate.io交易所实现"""

    def __init__(self, api_key: str, api_secret: str, sandbox: bool = False, **kwargs):
        """
        初始化Gate.io交易所
        :param api_key: API密钥
        :param api_secret: API密钥
        :param sandbox: 是否使用沙盒环境
        """
        super().__init__("gate", api_key, api_secret, **kwargs)
        
        # 🔥 添加logger初始化
        import logging
        self.logger = logging.getLogger(f"gate_exchange")
        
        self.api_key = api_key
        self.api_secret = api_secret
        self.sandbox = sandbox
        self.base_url = "https://api.gateio.ws" if not sandbox else "https://fx-api-testnet.gateio.ws"
        
        # 🔥 根源修复：统一限速配置，确保三交易所一致性
        if not hasattr(self, 'rate_limit') or self.rate_limit > 8:
            self.rate_limit = 8  # 🔥 根源修复：降低到8次/秒，确保30+代币健壮启动
            logger.info(f"🔧 Gate API限制根源修复为{self.rate_limit}次/秒，确保30+代币健壮启动")

        # 初始化统一模块 - 🔥 避免重复导入
        logger.info(f"初始化Gate.io交易所接口，API请求限制: {self.rate_limit}/秒")
        
        # 🔥 统一模块初始化 - 避免重复逻辑
        from core.trading_rules_preloader import get_trading_rules_preloader
        from core.universal_token_system import get_universal_token_system  
        from exchanges.currency_adapter import CurrencyAdapter
        from core.unified_opening_manager import get_opening_manager
        from core.unified_closing_manager import get_closing_manager
        
        self.rules_preloader = get_trading_rules_preloader()
        self.token_system = get_universal_token_system()
        self.currency_adapter = CurrencyAdapter()
        self.opening_manager = get_opening_manager()
        self.closing_manager = get_closing_manager()
        
        logger.info("✅ Gate.io交易所统一初始化完成 - 🔥 零重复逻辑")

        # API endpoints
        self.base_urls = {
            "spot": "https://api.gateio.ws/api/v4",
            "futures": "https://api.gateio.ws/api/v4/futures/usdt"
        }

        # 🔥 统一会话管理 - 使用第19个核心统一模块
        from core.unified_http_session_manager import get_unified_session_manager
        self.session_manager = get_unified_session_manager()
        self.session: Optional[aiohttp.ClientSession] = None

    async def _ensure_session(self):
        """确保会话存在 - 🔥 使用统一会话管理器"""
        if self.session is None or self.session.closed:
            self.session = await self.session_manager.get_session("gate")

    async def close(self):
        """关闭会话 - 🔥 使用统一会话管理器"""
        try:
            await self.session_manager.close_session("gate")
            self.session = None
        except Exception as e:
            self.logger.error(f"关闭Gate.io会话失败: {e}")

    async def _rate_limit(self):
        """🔥 统一限速方法 - 与API调用优化器保持一致"""
        # 使用API调用优化器的统一限速控制
        from core.api_call_optimizer import get_api_optimizer
        optimizer = get_api_optimizer()
        await optimizer._rate_limit_wait("gate")

    async def _rate_limit_with_optimizer(self):
        """🔥 新增：使用API调用优化器的精确限速控制"""
        # 与OKX保持一致的限速方法
        await self._rate_limit()

    async def _generate_signature(self, method: str, url_path: str, query_string: str, payload: str) -> dict:
        """Gate.io官方签名算法 - 完全按照官方SDK实现"""
        # 🔥 关键修复：使用浮点数时间戳，完全对应官方SDK实现
        timestamp = time.time()  # 官方SDK使用浮点数: t = time.time()
        
        # body哈希 - 完全对应官方SDK
        m = hashlib.sha512()
        if payload:
            if not isinstance(payload, str):
                payload = json.dumps(payload)
            m.update(payload.encode('utf-8'))
        else:
            m.update(b"")
        hashed_payload = m.hexdigest()
        
        # 签名字符串 - 完全对应官方SDK格式
        # 官方SDK: s = '%s\n%s\n%s\n%s\n%s' % (method, url, query_string or "", hashed_payload, t)
        query_str = query_string if query_string else ""
        sign_str = f"{method}\n{url_path}\n{query_str}\n{hashed_payload}\n{timestamp}"
        
        # 生成签名 - 完全对应官方SDK
        signature = hmac.new(
            self.api_secret.encode('utf-8'),
            sign_str.encode('utf-8'),
            hashlib.sha512
        ).hexdigest()
        
        # 返回头部 - 完全对应官方SDK格式
        return {
            "KEY": self.api_key,
            "SIGN": signature,
            "Timestamp": str(timestamp),  # 🔥 关键：保持浮点数格式
            "Content-Type": "application/json"
        }

    async def _request(self, method: str, endpoint: str, params: Dict = None,
                      data: Dict = None, market_type: str = "spot", signed: bool = True) -> Dict:
        await self._ensure_session()
        await self._rate_limit()
        try:
            base_url = self.base_urls[market_type]
            # endpoint处理
            if endpoint.startswith("/"):
                endpoint = endpoint[1:]

            # 修正接口路径拼接逻辑，避免重复添加futures/usdt
            if market_type == "spot":
                url_path = f"/api/v4/{endpoint}"
                url = f"{base_url}/{endpoint}"
            else:
                # 期货接口路径修正，防止重复拼接
                url_path = f"/api/v4/futures/usdt/{endpoint}"
                url = f"{base_url}/{endpoint}"

            # 构建query_string
            query_string = ""
            if params:
                query_string = urllib.parse.urlencode(sorted(params.items()))
                # 🔥 关键修复：期货杠杆设置等POST请求也需要query参数
                # 根据Gate.io官方SDK，某些POST请求(如杠杆设置)需要query参数
                if method in ["GET", "DELETE"] or (method == "POST" and market_type == "futures"):
                    url = f"{url}?{query_string}"

            # body
            body = ""
            if data:
                body = json.dumps(data)
            # 修复：GET和DELETE请求都不应该有body
            if method in ["GET", "DELETE"]:
                body = ""

            headers = await self._generate_signature(
                method,
                url_path,
                query_string,
                body
            )

            logger.debug(f"Gate.io请求: {method} {url}, Headers: {headers}, Data: {body}")

            if method == "GET":
                async with self.session.get(url, headers=headers) as response:
                    result = await response.json()
            else:
                async with self.session.request(
                    method, url, headers=headers, data=body if body else None
                ) as response:
                    # 修复：DELETE请求可能返回空响应
                    content_type = response.headers.get('content-type', '')
                    if response.status == 204 or not content_type.startswith('application/json'):
                        result = {}  # DELETE成功通常返回空响应
                    else:
                        result = await response.json()

            # 修复：DELETE请求成功状态码是204
            success_codes = [200, 201] if method != "DELETE" else [200, 201, 204]
            if response.status not in success_codes:
                self.error_count += 1
                logger.error(f"Gate.io API错误 (状态码{response.status}): {result}")
                raise Exception(f"Gate.io API错误: {result}")

            logger.debug(f"Gate.io API响应成功 (状态码{response.status}): {result}")
            return result
        except Exception as e:
            self.error_count += 1
            logger.error(f"Gate.io请求失败: {e}")
            raise

    async def get_balance(self, account_type: AccountType = AccountType.SPOT) -> Dict[str, Any]:
        """获取账户余额"""
        balances = {}

        try:
            if account_type == AccountType.SPOT:
                # 获取现货余额
                response = await self._request("GET", "/spot/accounts", market_type="spot")
                for item in response:
                    currency = item["currency"]
                    available = float(item["available"])
                    balances[currency] = {
                        'available': available,
                        'locked': float(item.get("locked", 0))
                    }

            elif account_type == AccountType.FUTURES:
                # 获取期货余额
                response = await self._request("GET", "/accounts", market_type="futures")
                available = float(response.get("available", 0))
                balances["USDT"] = {
                    'available': available,
                    'locked': float(response.get("in_order", 0))
                }

            return balances

        except Exception as e:
            logger.error(f"获取Gate.io余额失败: {e}")
            raise

    # 🔥 删除RestAPI价格获取方法 - 统一使用WebSocket+缓存
    # async def get_ticker() 方法已删除，改为使用 websocket_price_cache.py
    # 所有价格数据通过WebSocket实时获取，避免RestAPI延迟

    async def get_orderbook(self, symbol: str, market_type: str = "spot", limit: int = 10) -> Dict[str, Any]:
        """
        🚨 严禁REST API - 只能使用WebSocket数据源

        Args:
            symbol: 交易对
            market_type: 市场类型
            limit: 深度限制

        Returns:
            空订单簿（强制使用WebSocket）
        """
        # 🚨 严禁REST API调用！订单簿数据只能来自WebSocket！
        self.logger.warning(f"🚨 get_orderbook已禁用！订单簿数据只能使用WebSocket！")
        self.logger.warning(f"   请使用: OpportunityScanner.market_data['{self.name.lower()}_{market_type}_{symbol}']")

        # 🔥 返回空订单簿，强制使用WebSocket数据流
        return {
            'asks': [],
            'bids': [],
            'timestamp': int(time.time() * 1000),
            'symbol': symbol,
            'exchange': 'gate',
            'error': 'REST_API_DISABLED_USE_WEBSOCKET'
        }

    async def create_spot_order(self, symbol: str, side, order_type,
                               amount: float, price: Optional[float] = None,
                               params: Optional[Dict] = None) -> Dict[str, Any]:
        """
        🔥 关键修复：直接现货下单方法，强制使用市价单
        专门为SpotTrader提供的底层API调用
        """
        try:
            # 处理参数类型
            if hasattr(side, 'value'):
                side_str = side.value
            else:
                side_str = str(side).lower()

            # 🔥 强制使用市价单，禁止限价单
            logger.info(f"🔥 Gate.io强制使用市价单: {symbol} {side_str}")

            # 转换交易对格式
            gate_symbol = self.currency_adapter.get_exchange_symbol(symbol, "gate", "spot")

            # 🔥 关键修复：Gate现货API参数类型错误修复
            # 问题：应该下单21个NEAR，实际下单21 USDT
            # 根因：Gate.io现货买单错误地传递了USDT金额而不是币数量

            # 🔥 修复方案：统一使用币数量，不再区分买卖单
            api_amount = amount  # 直接使用币数量（base currency）
            current_price = 0    # 价格仅用于日志记录

            # 🔥 关键修复：正确使用OpportunityScanner.market_data获取价格
            try:
                from core.arbitrage_engine import get_arbitrage_engine
                engine = get_arbitrage_engine()
                if engine and hasattr(engine, 'opportunity_scanner') and engine.opportunity_scanner:
                    scanner = engine.opportunity_scanner
                    if hasattr(scanner, 'market_data') and scanner.market_data:
                        # 🔥 修复：使用正确的数据key格式
                        data_key = f"gate_spot_{symbol}"  # 格式：gate_spot_NEAR-USDT
                        market_data_obj = scanner.market_data.get(data_key)

                        if market_data_obj and hasattr(market_data_obj, 'price'):
                            current_price = float(market_data_obj.price)
                            if current_price > 0:
                                logger.info(f"🔍 Gate.io获取价格成功: {data_key} = {current_price}")
                            else:
                                logger.warning(f"⚠️ Gate.io价格为0: {data_key}")
                                current_price = 0
                        else:
                            logger.warning(f"⚠️ Gate.io未找到价格数据: {data_key}")
                            # 🔥 调试：列出可用的数据key
                            available_keys = list(scanner.market_data.keys())
                            logger.warning(f"⚠️ 可用数据key总数: {len(available_keys)}")
                            logger.warning(f"⚠️ 包含{symbol}的key: {[k for k in available_keys if symbol in k]}")
                            logger.warning(f"⚠️ 所有gate相关key: {[k for k in available_keys if 'gate' in k]}")
                            logger.warning(f"⚠️ 前10个可用key: {available_keys[:10]}")
                            current_price = 0
                    else:
                        logger.warning(f"⚠️ OpportunityScanner.market_data不可用")
                        current_price = 0
                else:
                    logger.warning(f"⚠️ ArbitrageEngine或OpportunityScanner不可用")
                    current_price = 0
            except Exception as e:
                logger.warning(f"⚠️ Gate.io获取价格失败: {e}")
                current_price = 0

            # 🔥 关键修复：价格为0时抛出异常，避免0 USDT订单
            if current_price <= 0:
                error_msg = f"❌ Gate现货买单价格获取失败: {symbol} price={current_price}, 无法计算USDT金额"
                logger.error(error_msg)
                raise Exception(error_msg)

            # 🔥 关键修复：买单和卖单都使用币数量
            if side_str == "buy":
                logger.info(f"🔧 Gate.io现货买单: {amount} {symbol.split('-')[0]} (修复前错误传递USDT金额)")
            else:
                logger.info(f"🔧 Gate.io现货卖单: {amount} {symbol.split('-')[0]}")

            logger.info(f"✅ Gate.io统一使用币数量: {api_amount} {symbol.split('-')[0]}")

            # 🔥 关键修复：Gate现货API参数正确处理
            # 根据Gate.io官方API文档：
            # - 买单(buy): amount参数是USDT金额
            # - 卖单(sell): amount参数是币数量

            if side_str == "buy":
                # 买单：需要传递USDT金额
                usdt_amount = api_amount * current_price  # 币数量 × 价格 = USDT金额
                order_data = {
                    "currency_pair": gate_symbol,
                    "side": side_str,
                    "type": "market",
                    "amount": str(usdt_amount),  # 🔥 修复：买单传递USDT金额
                    "time_in_force": "ioc"
                }
                logger.info(f"🔥 Gate现货买单: {api_amount} {symbol.split('-')[0]} × {current_price} = {usdt_amount} USDT")
            else:
                # 卖单：传递币数量
                order_data = {
                    "currency_pair": gate_symbol,
                    "side": side_str,
                    "type": "market",
                    "amount": str(api_amount),  # 🔥 卖单传递币数量
                    "time_in_force": "ioc"
                }
                logger.info(f"🔥 Gate现货卖单: {api_amount} {symbol.split('-')[0]}")

            logger.info(f"🔍 Gate.io现货市价单: {order_data}")

            # 直接调用Gate.io API
            response = await self._request(
                "POST", "/spot/orders",
                data=order_data
            )

            if response:
                # 🔥 关键修复：Gate.io现货API参数类型修复后的返回值处理
                # 修复前：传递USDT金额，返回值需要复杂计算
                # 修复后：传递币数量，返回值直接使用

                actual_amount = amount  # 直接使用请求的币数量

                # 🔥 统一处理：买单和卖单都使用币数量逻辑
                filled_total = float(response.get("filled_total", 0))  # 成交金额(USDT)
                left_amount = float(response.get("left", 0))           # 剩余数量

                logger.info(f"🔍 Gate.io现货响应: filled_total={filled_total} USDT, left={left_amount}")

                if side_str == "buy":
                    # 🔥 关键修复：买单传递USDT金额后的正确处理
                    if filled_total > 0:
                        # 买单：filled_total是成交的USDT金额，需要计算实际买到的币数量
                        actual_amount = filled_total / current_price if current_price > 0 else amount

                        logger.info(f"✅ Gate.io买单成交: {filled_total:.2f} USDT ÷ {current_price:.6f} = {actual_amount:.6f} {symbol.split('-')[0]}")

                        # 验证数量合理性
                        expected_amount = amount  # 预期买入的币数量
                        amount_diff_pct = abs(actual_amount - expected_amount) / expected_amount * 100
                        if amount_diff_pct > 5:
                            logger.warning(f"⚠️ Gate.io买单数量差异: 预期{expected_amount:.6f}, 实际{actual_amount:.6f}, 差异{amount_diff_pct:.2f}%")
                        else:
                            logger.info(f"✅ Gate.io买单数量正常: 差异{amount_diff_pct:.2f}%")
                    else:
                        logger.warning(f"⚠️ Gate.io买单未成交: filled_total={filled_total}")
                        actual_amount = 0
                else:
                    # 🔥 卖单：逻辑保持不变，使用币数量
                    actual_amount = amount - left_amount if left_amount < amount else amount
                    logger.info(f"✅ Gate.io卖单成交: {actual_amount} {symbol.split('-')[0]}")

                # 🔥 最终修复：确保actual_amount绝对不会为None或错误值
                # 添加多重保护，防止任何情况下的字段错误
                
                if actual_amount is None or actual_amount <= 0:
                    # 兜底保护：如果actual_amount异常，使用请求数量
                    actual_amount = amount
                    logger.warning(f"⚠️ Gate.io actual_amount异常，使用请求数量: {amount}")
                
                # 🔥 关键验证：确保返回的是币数量而非USDT金额
                if actual_amount > amount * 5:  # 如果数量异常大（可能是USDT金额）
                    logger.error(f"❌ Gate.io返回数量异常: {actual_amount}，疑似USDT金额，强制使用请求数量")
                    actual_amount = amount
                
                # 🔥 关键修复：查询实际成交价格
                order_id = response.get("id", "")
                executed_price = await self._get_order_executed_price(order_id, symbol, "spot")

                # 🔥 修复：如果没有成交价格，使用当前市价作为兜底
                if executed_price <= 0:
                    executed_price = current_price or 0

                # 🔥 统一返回格式：确保amount和actual_amount都是正确的币数量
                result = {
                    "order_id": order_id,
                    "symbol": symbol,
                    "side": side_str,
                    "type": "market",  # 🔥 强制返回市价单
                    "amount": actual_amount,  # 🔥 币数量（SpotTrader备用字段）
                    "actual_amount": actual_amount,  # 🔥 币数量（SpotTrader主字段）
                    "filled": actual_amount,  # 🔥 币数量（标准字段）
                    "executed_quantity": actual_amount,  # 🔥 币数量（标准字段）
                    "price": current_price or 0,  # 🔥 修复：避免None引用错误
                    "executed_price": executed_price,  # 🔥 关键修复：返回查询到的实际成交价格
                    "average": executed_price,  # 🔥 关键修复：使用查询到的价格
                    "status": "open",
                    "timestamp": int(time.time() * 1000)
                }
                
                logger.info(f"✅ Gate.io现货返回: amount={result['amount']}, actual_amount={result['actual_amount']}")
                return result
            else:
                raise Exception(f"Gate.io现货下单失败: {response}")

        except Exception as e:
            logger.error(f"Gate.io现货直接下单异常: {e}")
            raise

    async def create_futures_order(self, symbol: str, side, order_type,
                                  amount: float, price: Optional[float] = None,
                                  params: Optional[Dict] = None) -> Dict[str, Any]:
        """
        🔥 关键修复：直接期货下单方法，强制使用市价单
        专门为FuturesTrader提供的底层API调用
        """
        try:
            # 处理参数类型
            if hasattr(side, 'value'):
                side_str = side.value
            else:
                side_str = str(side).lower()

            # 🔥 强制使用市价单，禁止限价单
            logger.info(f"🔥 Gate.io期货强制使用市价单: {symbol} {side_str}")

            # 转换交易对格式
            contract = get_exchange_symbol(symbol, "gate", "futures")

            # 🔥 Gate.io期货API正确规则（最终修正版）：
            # - size: 正数表示做多，负数表示做空（这是Gate.io的实际API规则！）
            # - 不需要side参数，方向完全由size的正负号决定
            # - 从平仓代码中发现：负数size表示做空是正确的API用法

            # 🔥 关键修复：根据side参数决定size的正负号
            # 🚨 重大修复：不能使用int()截断，会导致数量不匹配！
            # 问题：int(3.31) = 3，导致现货3.311 vs 期货3.0的9.39%差异
            # 解决：使用统一模块的精度处理，与Bybit、OKX保持一致

            # 🔥 使用统一精度处理模块 - 与Bybit保持一致
            # 防御性检查：确保trading_rules_preloader属性存在
            if not hasattr(self, 'trading_rules_preloader') or self.trading_rules_preloader is None:
                logger.warning("⚠️ trading_rules_preloader不存在，重新初始化统一模块")
                from exchanges.unified_exchange_initializer import setup_exchange_unified
                setup_exchange_unified("Gate", self)

            # 🔥 使用统一的合约转换方法 - 删除重复逻辑
            formatted_amount = await self.trading_rules_preloader.format_amount_with_contract_conversion(
                amount, self, symbol, "futures"
            )
            formatted_amount_float = float(formatted_amount)

            # 🚨 关键修复：根据官方SDK文档，Gate.io期货API的size参数必须是整数类型！
            # 官方文档：size | int | Order size. Specify positive number to make a bid, and negative number to ask
            if side_str.lower() == "sell":
                # 卖出 = 做空 = 负整数size
                # 🔥 修复：使用四舍五入而不是截断，保证对冲质量
                actual_size = -round(formatted_amount_float)  # 负整数表示做空（四舍五入）
                logger.info(f"🔥 Gate.io期货做空: {amount:.6f} -> size={actual_size} (四舍五入到负整数)")
            else:
                # 买入 = 做多 = 正整数size
                # 🔥 修复：使用四舍五入而不是截断，保证对冲质量
                actual_size = round(formatted_amount_float)  # 正整数表示做多（四舍五入）
                logger.info(f"🔥 Gate.io期货做多: {amount:.6f} -> size={actual_size} (四舍五入到正整数)")

            # 🔥 关键验证：检查数量差异是否过大
            amount_diff_pct = abs(abs(actual_size) - amount) / amount * 100 if amount > 0 else 0
            if amount_diff_pct > 1.0:  # 超过1%差异需要警告
                logger.warning(f"⚠️ Gate.io期货数量调整: {amount:.6f} -> {abs(actual_size):.6f} (差异{amount_diff_pct:.2f}%)")
            else:
                logger.info(f"✅ Gate.io期货数量处理: {amount:.6f} -> {abs(actual_size):.6f} (差异{amount_diff_pct:.2f}%)")

            # 🔥 Gate.io期货API正确参数（根据官方SDK文档）
            order_data = {
                "contract": contract,
                "size": actual_size,  # 🔥 关键修复：使用整数size，正负号表示方向
                "price": "0",  # 🔥 市价单价格为0
                "tif": "ioc"   # 🔥 市价单使用IOC
                # 🔥 删除side参数：根据官方SDK，方向由size的正负号决定
            }

            # 处理额外参数
            if params:
                # 处理reduce_only参数
                if params.get("reduce_only") or params.get("is_closing"):
                    order_data["reduce_only"] = True

            logger.info(f"🔍 Gate.io期货市价单: {order_data}")

            # 直接调用Gate.io API
            response = await self._request(
                "POST", "orders",
                data=order_data,
                market_type="futures"
            )

            if response and response.get("id"):
                # 🔥 关键修复：正确处理返回的size（可能是负数）
                response_size = float(response.get("size", actual_size))
                filled_amount = abs(response_size)  # 成交数量（绝对值）
                filled_price = float(response.get("price", 0))  # 成交价格
                
                # 🔥 关键：验证期货持仓方向是否正确
                actual_direction = "做空" if response_size < 0 else "做多"
                expected_direction = "做空" if side_str.lower() == "sell" else "做多"
                logger.info(f"🎯 Gate期货订单创建: {side_str} -> size={response_size}")
                logger.info(f"🎯 Gate期货方向验证: 预期{expected_direction}, 实际{actual_direction}")
                
                if actual_direction == expected_direction:
                    logger.info(f"✅ Gate期货方向正确: {expected_direction}")
                else:
                    logger.error(f"❌ Gate期货方向错误: 预期{expected_direction}, 实际{actual_direction}")
                
                # 🔥 重要：记录数量差异（如果有的话）
                if filled_amount != amount:
                    logger.warning(f"⚠️ Gate.io期货数量差异：请求{amount}，实际{filled_amount}")
                
                # 🔥 关键修复：查询实际成交价格
                order_id = str(response.get("id", ""))
                executed_price = await self._get_order_executed_price(order_id, symbol, "futures")

                # 🔥 修复：如果没有成交价格，使用API返回的价格作为兜底
                if executed_price <= 0:
                    executed_price = filled_price or 0

                return {
                    "order_id": order_id,
                    "symbol": symbol,
                    "side": side_str,
                    "type": "market",  # 🔥 强制返回市价单
                    "amount": filled_amount,  # 🔥 返回实际成交数量（整数）
                    "price": filled_price,  # 🔥 返回API价格
                    "executed_price": executed_price,  # 🔥 关键修复：返回查询到的实际成交价格
                    "filled": filled_amount,  # 🔥 成交数量
                    "average": executed_price,  # 🔥 平均成交价格（使用查询到的价格）
                    "status": "filled" if response.get("status") == "finished" else "open",
                    "timestamp": int(time.time() * 1000)
                }
            else:
                raise Exception(f"Gate.io期货下单失败: {response}")

        except Exception as e:
            logger.error(f"Gate.io期货直接下单异常: {e}")
            raise

    async def place_order(
        self,
        symbol: str,
        side: OrderSide,
        order_type: OrderType,
        amount: float,
        price: Optional[float] = None,
        market_type: str = "spot",
        params: Optional[Dict] = None
    ) -> Dict[str, Any]:
        """下单 - 🔥 使用统一开仓管理器（已在__init__中初始化）"""
        try:
            # 🔥 修复：安全处理side参数，避免.value错误
            side_str = side.value if hasattr(side, 'value') else str(side)
            logger.info(f"🔍 Gate.io下单开始: {symbol} {side_str} {amount} {market_type}")

            # 🔥 关键修复：检查是否为期货平仓操作
            is_closing = params and params.get('close', False) if params else False
            reduce_only = params and params.get('reduceOnly', False) if params else False
            is_futures_closing = market_type == "futures" and (is_closing or reduce_only)

            if is_futures_closing:
                logger.info(f"🔥 检测到Gate.io期货平仓操作，跳过合约转换直接下单")
                # 期货平仓：直接使用原生API，跳过统一开仓管理器的合约转换
                return await self._place_futures_order_direct(
                    symbol=symbol,
                    side=side,
                    order_type=order_type,
                    amount=amount,
                    price=price,
                    params=params
                )

            # 🔥 修复：验证统一开仓管理器是否正确初始化
            if not hasattr(self, 'opening_manager') or self.opening_manager is None:
                error_msg = "❌ 统一开仓管理器未初始化"
                logger.error(error_msg)
                raise Exception(error_msg)

            # 🔥 修复：从params中提取orderbook参数
            orderbook = None
            if params and isinstance(params, dict):
                orderbook = params.get('orderbook')
                logger.info(f"🔍 Gate.io从params中获取orderbook: {orderbook is not None}")

            # 🔥 删除重复逻辑，使用已初始化的统一开仓管理器
            # 🔥 修复：安全处理side和order_type参数
            side_value = side.value if hasattr(side, 'value') else str(side)
            order_type_value = order_type.value if hasattr(order_type, 'value') else str(order_type)

            # 准备开仓参数（仅用于开仓，平仓已在上面处理）
            opening_params = await self.opening_manager.prepare_opening_params(
                symbol=symbol,
                side=side_value,
                quantity=amount,
                price=price,
                exchange=self,
                market_type=market_type,
                order_type=order_type_value
            )

            # 🔥 修复：验证开仓参数是否成功准备
            if not opening_params:
                error_msg = f"❌ 开仓参数准备失败: {symbol} {side_value} {amount}"
                logger.error(error_msg)
                raise Exception(error_msg)

            logger.info(f"✅ Gate.io开仓参数准备成功: {symbol}")

            # 🔥 修复：执行开仓订单时传递orderbook参数
            result = await self.opening_manager.execute_opening_order(opening_params, self, orderbook)

            # 🔥 修复：验证执行结果
            if not result:
                error_msg = f"❌ 开仓执行返回空结果: {symbol}"
                logger.error(error_msg)
                raise Exception(error_msg)

            if result.success:
                logger.info(f"✅ Gate.io下单成功: {symbol} {result.order_id}")

                # 🔥 修复：安全处理side和order_type参数
                side_str = side.value if hasattr(side, 'value') else str(side)
                order_type_str = order_type.value if hasattr(order_type, 'value') else str(order_type)

                return {
                    "order_id": result.order_id,
                    "symbol": symbol,
                    "side": side_str,
                    "type": order_type_str,
                    "amount": result.executed_quantity or amount,
                    "price": result.executed_price or price,
                    "status": "open",
                    "timestamp": int(time.time() * 1000)
                }
            else:
                error_msg = f"Gate.io下单失败: {result.error_message}"
                logger.error(error_msg)
                raise Exception(error_msg)

        except Exception as e:
            self.error_count += 1
            error_msg = f"Gate.io下单异常: {str(e)}"
            logger.error(error_msg)
            # 🔥 修复：提供更详细的错误信息
            import traceback
            logger.error(f"Gate.io下单异常堆栈: {traceback.format_exc()}")
            raise Exception(error_msg)

    async def _place_futures_order_direct(
        self,
        symbol: str,
        side: OrderSide,
        order_type: OrderType,
        amount: float,
        price: Optional[float] = None,
        params: Optional[Dict] = None
    ) -> Dict[str, Any]:
        """🔥 期货平仓专用：直接下单，跳过合约转换"""
        try:
            # 🔥 修复：安全处理side参数
            side_str = side.value if hasattr(side, 'value') else str(side)
            logger.info(f"🔥 Gate.io期货平仓直接下单: {symbol} {side_str} {amount}")

            # 转换为Gate.io期货符号格式
            gate_symbol = get_exchange_symbol(symbol, "gate", "futures")

            # 构建期货订单参数
            order_data = {
                "contract": gate_symbol,
                "size": int(amount),  # Gate.io期货使用整数张数
                "price": "0",  # 市价单价格为0
                "tif": "ioc",  # 立即成交或取消
                "reduce_only": True  # 🔥 关键：只减仓，避免开新仓
            }

            # 🔥 修复：根据订单方向设置size的正负
            if side_str.lower() == "sell":
                order_data["size"] = -int(amount)  # 卖出为负数
            else:
                order_data["size"] = int(amount)   # 买入为正数

            logger.info(f"🔧 Gate.io期货平仓订单数据: {order_data}")

            # 直接调用Gate.io期货API (修复路径重复问题)
            response = await self._request(
                "POST", "orders",
                data=order_data,
                market_type="futures"
            )

            if response and response.get('id'):
                order_id = str(response['id'])
                logger.info(f"✅ Gate.io期货平仓成功: {order_id}")

                return {
                    "order_id": order_id,
                    "symbol": symbol,
                    "side": side_str,
                    "type": order_type.value if hasattr(order_type, 'value') else str(order_type),
                    "amount": amount,
                    "price": price,
                    "status": "open",
                    "timestamp": int(time.time() * 1000)
                }
            else:
                error_msg = f"Gate.io期货平仓失败: 无效的订单结果 - {response}"
                logger.error(error_msg)
                raise Exception(error_msg)

        except Exception as e:
            logger.error(f"Gate.io期货平仓直接下单异常: {e}")
            raise

    async def cancel_order(self, order_id: str, symbol: str, market_type: str = "spot") -> bool:
        """取消订单 - 🔥 修复DELETE请求和空响应处理"""
        try:
            symbol = get_exchange_symbol(symbol, "gate", market_type)
            if market_type == "spot":
                # 现货撤单：使用URL参数而非body
                await self._request(
                    "DELETE", f"spot/orders/{order_id}",
                    params={"currency_pair": symbol},
                    market_type="spot"
                )
            else:
                # 期货撤单：只需要订单ID
                await self._request(
                    "DELETE", f"orders/{order_id}",
                    market_type="futures"
                )

            return True

        except Exception as e:
            # 🔥 使用统一错误处理
            if self._handle_common_errors(e, "cancel_order"):
                return True
            else:
                logger.error(f"Gate.io取消订单失败: {e}")
                return False

    async def get_order(self, order_id: str, symbol: str, market_type: str = "spot") -> Dict[str, Any]:
        """查询订单状态"""
        try:
            symbol = get_exchange_symbol(symbol, "gate", market_type)
            if market_type == "spot":
                response = await self._request(
                    "GET", f"/spot/orders/{order_id}",
                    params={"currency_pair": symbol},
                    market_type="spot"
                )

                # 🔥 修复：增强现货订单查询的错误处理，防止字段缺失
                if not isinstance(response, dict):
                    raise Exception(f"Gate.io现货订单查询响应格式异常: {response}")

                return {
                    "order_id": str(response.get("id", order_id)),
                    "symbol": symbol,
                    "side": response.get("side", "buy"),
                    "type": response.get("type", "limit"),
                    "amount": float(response.get("amount", 0)),
                    "price": float(response.get("price", 0)),
                    "filled_amount": float(response.get("filled_total", 0)),
                    "status": self._parse_order_status(response.get("status", "unknown")).value,
                    "timestamp": int(response.get("create_time", time.time())) * 1000
                }
            else:
                # 🔥 修复：Gate.io期货订单查询API响应处理
                response = await self._request(
                    "GET", f"/orders/{order_id}",
                    params={"status": "finished"},  # 添加必需的status参数
                    market_type="futures"
                )

                # 🔥 修复：正确处理响应格式，避免索引错误
                # Gate.io API可能返回列表或字典，需要统一处理
                if isinstance(response, list) and len(response) > 0:
                    order_data = response[0]
                elif isinstance(response, dict):
                    order_data = response
                else:
                    raise Exception(f"Gate.io期货订单查询响应格式异常: {response}")

                # 🔥 修复：正确获取字段值，避免键值错误
                order_size = float(order_data.get("size", 0))
                order_left = float(order_data.get("left", 0))
                
                return {
                    "order_id": str(order_data.get("id", order_id)),
                    "symbol": symbol,
                    "side": "buy" if order_size > 0 else "sell",
                    "type": "limit" if order_data.get("price") else "market",
                    "amount": abs(order_size),
                    "price": float(order_data.get("price", 0)),
                    "filled_amount": abs(order_size) - abs(order_left),
                    "status": self._parse_order_status(order_data.get("status", "unknown")).value,
                    "timestamp": int(order_data.get("create_time", time.time())) * 1000
                }

        except Exception as e:
            logger.error(f"Gate.io查询订单失败: {e}")
            raise

    async def get_position(self, symbol: str = None) -> List[Dict[str, Any]]:
        """获取持仓信息"""
        try:
            symbol = get_exchange_symbol(symbol, "gate", "futures") if symbol else None
            
            if symbol:
                response = await self._request(
                    "GET", "/positions",
                    params={"contract": symbol} if symbol else {},
                    market_type="futures"
                )

                positions = []
                for pos in response:
                    position = {
                        "symbol": pos.get("contract"),
                        "side": "long" if float(pos.get("size", 0)) > 0 else "short",
                        "size": abs(float(pos.get("size", 0))),
                        "entry_price": float(pos.get("entry_price", 0)),
                        "mark_price": float(pos.get("mark_price", 0)),
                        "unrealized_pnl": float(pos.get("unrealised_pnl", 0)),
                        "percentage": float(pos.get("roe", 0)) * 100,
                        "last_update": pos.get("last_update_time")
                    }
                    positions.append(position)

                return positions
            else:
                return []
        except Exception as e:
            logger.error(f"获取Gate.io仓位失败: {e}")
            raise

    async def _get_order_executed_price(self, order_id: str, symbol: str, market_type: str) -> float:
        """
        🔥 关键修复：获取Gate.io订单的实际成交价格
        与Bybit和OKX修复保持一致，补全Gate.io缺失的成交价格查询机制
        """
        try:
            # 转换为Gate.io格式的symbol
            gate_symbol = get_exchange_symbol(symbol, "gate", market_type)

            # 查询订单详情
            if market_type == "spot":
                response = await self._request("GET", f"/spot/orders/{order_id}")
            else:
                response = await self._request("GET", f"/orders/{order_id}",
                                             params={"status": "finished"},
                                             market_type="futures")

            if response:
                # 处理不同的响应格式
                order_info = response
                if isinstance(response, list) and len(response) > 0:
                    order_info = response[0]

                # 尝试获取平均成交价格
                avg_price = None

                # Gate.io现货和期货的字段可能不同
                if market_type == "spot":
                    avg_price = order_info.get("avg_deal_price") or order_info.get("fill_price")
                else:
                    avg_price = order_info.get("fill_price") or order_info.get("avg_price")

                if avg_price and float(avg_price) > 0:
                    executed_price = float(avg_price)
                    logger.info(f"🎯 Gate.io订单{order_id}实际成交价格: {executed_price:.8f}")
                    return executed_price

            # 如果订单详情中没有，查询成交历史
            logger.warning(f"⚠️ 订单详情中未找到成交价格，查询成交历史: {order_id}")
            return await self._get_execution_history_price(order_id, gate_symbol, market_type)

        except Exception as e:
            logger.error(f"❌ 获取Gate.io订单{order_id}成交价格失败: {e}")
            # 返回0，让futures_trader.py使用兜底逻辑
            return 0.0

    async def _get_execution_history_price(self, order_id: str, symbol: str, market_type: str) -> float:
        """
        🔥 从成交历史获取实际成交价格（加权平均）
        """
        try:
            if market_type == "spot":
                # Gate.io现货成交历史查询
                response = await self._request("GET", "/spot/my_trades",
                                             params={"order_id": order_id, "limit": 50})
            else:
                # Gate.io期货成交历史查询
                response = await self._request("GET", "/my_trades",
                                             params={"order": order_id, "limit": 50},
                                             market_type="futures")

            if response and len(response) > 0:
                total_value = 0.0
                total_qty = 0.0

                for trade in response:
                    if market_type == "spot":
                        price = float(trade.get("price", 0))
                        qty = float(trade.get("amount", 0))
                    else:
                        price = float(trade.get("price", 0))
                        qty = abs(float(trade.get("size", 0)))  # 期货size可能为负

                    if price > 0 and qty > 0:
                        total_value += price * qty
                        total_qty += qty

                if total_qty > 0:
                    weighted_avg_price = total_value / total_qty
                    logger.info(f"🎯 Gate.io订单{order_id}加权平均成交价格: {weighted_avg_price:.8f}")
                    return weighted_avg_price

            logger.warning(f"⚠️ Gate.io订单{order_id}成交历史为空")
            return 0.0

        except Exception as e:
            logger.error(f"❌ 获取Gate.io订单{order_id}成交历史失败: {e}")
            return 0.0

    # 🔥 删除重复的便利方法：直接使用基类的统一接口
    # get_orderbook(symbol, market_type, limit) 已经足够
    # get_balance(account_type) 已经足够
    # get_position(symbol) 已经足够

    async def get_total_balance(self) -> Dict[str, float]:
        """获取总余额 - ArbitrageEngine需要的方法"""
        try:
            # 获取现货和期货余额
            spot_balance = await self.get_balance(AccountType.SPOT)
            futures_balance = await self.get_balance(AccountType.FUTURES)

            total_balance = {}

            # 合并现货余额
            for currency, balance_info in spot_balance.items():
                if isinstance(balance_info, dict):
                    available = balance_info.get('available', 0.0)
                    locked = balance_info.get('locked', 0.0)
                    total_balance[currency] = available + locked
                else:
                    total_balance[currency] = float(balance_info) if balance_info else 0.0

            # 合并期货余额
            for currency, balance_info in futures_balance.items():
                if isinstance(balance_info, dict):
                    available = balance_info.get('available', 0.0)
                    locked = balance_info.get('locked', 0.0)
                    futures_amount = available + locked
                else:
                    futures_amount = float(balance_info) if balance_info else 0.0

                # 累加到总余额
                if currency in total_balance:
                    total_balance[currency] += futures_amount
                else:
                    total_balance[currency] = futures_amount

            return total_balance

        except Exception as e:
            logger.error(f"获取Gate.io总余额失败: {e}")
            return {}

    async def get_instrument_info(self, symbol: str, market_type: str) -> Dict[str, Any]:
        """🔥 新增：获取交易对的配置信息 - 确保三交易所接口一致性"""
        try:
            # 转换交易对格式
            gate_symbol = self._convert_symbol(symbol, market_type)

            # 检查缓存
            cache_key = f"{gate_symbol}_{market_type}"
            if hasattr(self, 'instrument_info_cache') and cache_key in self.instrument_info_cache:
                logger.debug(f"Gate使用缓存的交易对信息: {cache_key}")
                return self.instrument_info_cache[cache_key]

            # 初始化缓存
            if not hasattr(self, 'instrument_info_cache'):
                self.instrument_info_cache = {}

            # 从API获取
            logger.info(f"Gate获取交易对信息: {gate_symbol}, {market_type}")

            if market_type == "spot":
                # 现货交易对信息
                response = await self._request("GET", f"/spot/currency_pairs/{gate_symbol}", market_type="spot")

                if response:
                    result = {
                        "min_size": float(response.get("min_base_amount", 0.001)),
                        "size_increment": float(response.get("amount_precision", 0.001)),
                        "lot_size": float(response.get("amount_precision", 0.001)),
                        "tick_size": float(response.get("precision", 0.1)),
                        "min_notional": float(response.get("min_quote_amount", 5.0))
                    }
                else:
                    # 默认值
                    result = {
                        "min_size": 0.001,
                        "size_increment": 0.001,
                        "lot_size": 0.001,
                        "tick_size": 0.1,
                        "min_notional": 5.0
                    }
            else:
                # 期货交易对信息
                response = await self._request("GET", f"/futures/usdt/contracts/{gate_symbol}", market_type="futures")

                if response:
                    result = {
                        "min_size": float(response.get("order_size_min", 0.01)),
                        "size_increment": float(response.get("order_size_min", 0.01)),
                        "lot_size": float(response.get("order_size_min", 0.01)),
                        "tick_size": float(response.get("mark_price_round", 0.1)),
                        "min_notional": 5.0
                    }
                else:
                    # 期货默认值
                    result = {
                        "min_size": 0.01,
                        "size_increment": 0.01,
                        "lot_size": 0.01,
                        "tick_size": 0.1,
                        "min_notional": 5.0
                    }

            # 缓存结果
            self.instrument_info_cache[cache_key] = result
            logger.debug(f"Gate缓存交易对信息: {cache_key} -> {result}")

            return result

        except Exception as e:
            logger.error(f"Gate获取交易对信息失败: {symbol}, {market_type}, 错误: {e}")
            # 返回默认值
            default_result = {
                "min_size": 0.001 if market_type == "spot" else 0.01,
                "size_increment": 0.001 if market_type == "spot" else 0.01,
                "lot_size": 0.001 if market_type == "spot" else 0.01,
                "tick_size": 0.1,
                "min_notional": 5.0
            }
            return default_result

    # 🔥 新增：精度系统需要的API方法
    async def get_currency_pairs(self) -> List[Dict[str, Any]]:
        """
        获取现货交易对信息 - 用于精度配置
        🔥 修复：precision_config.py需要的方法
        """
        try:
            response = await self._request("GET", "/spot/currency_pairs", market_type="spot")
            logger.debug(f"Gate.io现货交易对信息: {len(response) if response else 0}个")
            return response if response else []
        except Exception as e:
            logger.error(f"获取Gate.io现货交易对信息失败: {e}")
            return []

    async def get_futures_contracts(self, settle: str = "usdt") -> List[Dict[str, Any]]:
        """
        获取期货合约信息 - 用于精度配置
        🔥 修复：precision_config.py需要的方法
        """
        try:
            response = await self._request("GET", "contracts",
                                         params={"settle": settle},
                                         market_type="futures")
            logger.debug(f"Gate.io期货合约信息: {len(response) if response else 0}个")
            return response if response else []
        except Exception as e:
            logger.error(f"获取Gate.io期货合约信息失败: {e}")
            return []

    async def get_contract_info(self, symbol: str, settle: str = "usdt") -> Dict[str, Any]:
        """
        🔥 新增：获取单个合约信息 - 用于保证金计算
        支持任意币种的通用合约信息查询

        根据Gate.io API文档：GET /futures/{settle}/contracts/{contract}
        返回Contract对象，包含maintenance_rate等保证金计算必需字段
        """
        try:
            # 转换为Gate.io期货合约格式
            contract_symbol = self.currency_adapter.get_exchange_symbol(symbol, "gate", "futures")

            logger.debug(f"Gate.io获取合约信息: {contract_symbol}")

            # 调用Gate.io官方API: GET /futures/{settle}/contracts/{contract}
            response = await self._request("GET", f"contracts/{contract_symbol}",
                                         params={"settle": settle},
                                         market_type="futures")

            if response:
                # 根据Gate.io Contract模型提取关键保证金计算字段
                contract_info = {
                    "name": response.get("name", contract_symbol),
                    "maintenance_rate": float(response.get("maintenance_rate", "0.005")),  # 维持保证金率
                    "leverage_min": float(response.get("leverage_min", "1")),
                    "leverage_max": float(response.get("leverage_max", "100")),
                    "order_size_min": int(response.get("order_size_min", 1)),
                    "order_size_max": int(response.get("order_size_max", 1000000)),
                    "quanto_multiplier": float(response.get("quanto_multiplier", "0.0001")),  # 合约乘数
                    "maker_fee_rate": float(response.get("maker_fee_rate", "0.0002")),
                    "taker_fee_rate": float(response.get("taker_fee_rate", "0.0005")),
                    "mark_price": float(response.get("mark_price", "0")),
                    "index_price": float(response.get("index_price", "0")),
                    "last_price": float(response.get("last_price", "0")),
                    "funding_rate": float(response.get("funding_rate", "0")),
                    "type": response.get("type", "inverse")
                }

                logger.info(f"✅ Gate.io合约信息获取成功: {symbol} -> 维持保证金率={contract_info['maintenance_rate']*100:.3f}%")
                return contract_info
            else:
                logger.warning(f"⚠️ Gate.io合约信息为空: {symbol}")
                return {}

        except Exception as e:
            logger.error(f"❌ Gate.io获取合约信息失败 {symbol}: {e}")
            # 🔥 关键修复：不返回默认值，让调用方知道获取失败
            # 这样ExecutionEngine可以决定是否继续交易
            return {}

    async def close_position(self, symbol: str, amount: Optional[float] = None) -> Dict[str, Any]:
        """平仓（期货）- 🔥 使用原生Gate.io期货API，避免递归调用"""
        try:
            # 🔥 修复：直接使用Gate.io原生期货平仓API
            # 1. 获取期货持仓
            positions = await self.get_position(symbol)
            
            if not positions:
                return {"success": False, "message": f"无{symbol}期货持仓需要平仓"}
            
            # 2. 找到有效持仓
            valid_position = None
            for pos in positions:
                position_size = float(pos.get('size', 0))
                if position_size > 0:
                    valid_position = pos
                    break
            
            if not valid_position:
                return {"success": False, "message": f"无{symbol}有效期货持仓"}
            
            position_size = float(valid_position.get('size', 0))
            position_side = valid_position.get('side', 'unknown')
            
            # 3. 确定平仓方向
            close_side = "buy" if position_side.lower() in ["short", "sell"] else "sell"
            
            # 4. 使用原生API创建平仓订单
            gate_symbol = self._convert_symbol(symbol, "futures")
            
            # Gate.io期货平仓订单参数
            order_data = {
                "contract": gate_symbol,
                "size": -position_size if close_side == "buy" else position_size,  # 负数表示平仓
                "price": "0",  # 市价单
                "tif": "ioc",  # 立即成交或取消
                "text": "api_close"  # 标记为API平仓
            }
            
            # 调用Gate.io期货下单API
            response = await self._request("POST", "/orders", data=order_data, market_type="futures")
            
            if response and response.get("id"):
                order_id = str(response["id"])
                
                # 查询订单状态确认平仓
                try:
                    await asyncio.sleep(0.1)  # 等待订单处理
                    order_status = await self._request("GET", f"/orders/{order_id}", 
                                                     params={"settle": "usdt"}, market_type="futures")
                    
                    executed_quantity = abs(float(order_status.get("size", position_size)))
                    executed_price = float(order_status.get("fill_price", 0))
                    
                    return {
                        "success": True,
                        "order_id": order_id,
                        "quantity": executed_quantity,
                        "price": executed_price,
                        "message": "平仓成功"
                    }
                    
                except Exception as e:
                    logger.warning(f"查询Gate期货平仓订单状态失败: {e}")
                    return {
                        "success": True,
                        "order_id": order_id,
                        "quantity": position_size,
                        "price": 0,
                        "message": "平仓订单已提交，状态查询失败"
                    }
            else:
                error_msg = "未知错误"
                if response and response.get("message"):
                    error_msg = response["message"]
                
                logger.error(f"❌ Gate期货平仓订单创建失败: {error_msg}")
                return {"success": False, "message": f"平仓失败: {error_msg}"}

        except Exception as e:
            logger.error(f"Gate期货平仓异常: {e}")
            return {"success": False, "message": str(e)}

    async def transfer_funds(self, currency: str, amount: float,
                           from_account: AccountType, to_account: AccountType) -> bool:
        """
        Gate.io资金划转（分离账户模式）
        POST /api/v4/wallet/transfers
        """
        try:
            # 🔥 Gate.io是分离账户，需要执行实际的资金划转
            # 正确映射账户类型到Gate.io API格式
            account_type_map = {
                AccountType.SPOT: "spot",
                AccountType.FUTURES: "futures",
                AccountType.UNIFIED: "unified"
            }

            from_account_str = account_type_map.get(from_account)
            to_account_str = account_type_map.get(to_account)

            if not from_account_str or not to_account_str:
                logger.error(f"不支持的账户类型: {from_account} -> {to_account}")
                return False

            # 检查余额是否足够
            current_balance = await self.get_balance(from_account)
            # 🔥 修复：正确处理余额字典格式
            available_amount = 0.0
            if isinstance(current_balance, dict) and currency in current_balance:
                currency_balance = current_balance[currency]
                if isinstance(currency_balance, dict):
                    available_amount = currency_balance.get('available', 0.0)
                elif isinstance(currency_balance, (int, float)):
                    available_amount = float(currency_balance)
            
            if available_amount < amount:
                logger.error(f"余额不足: 需要{amount} {currency}, 可用{available_amount}")
                return False

            # 修复：使用正确的请求体格式（根据官方API文档）
            # Gate.io要求金额最多8位小数
            amount_str = f"{amount:.8f}".rstrip('0').rstrip('.')
            if '.' not in amount_str:
                amount_str += ".0"

            data = {
                "currency": currency,
                "from": from_account_str,
                "to": to_account_str,
                "amount": amount_str  # 使用格式化后的金额字符串
            }

            # 如果是期货账户，需要添加settle参数
            if from_account == AccountType.FUTURES or to_account == AccountType.FUTURES:
                data["settle"] = "usdt"

            logger.info(f"Gate.io划转请求: {data}")

            # 修复：使用正确的API端点（transfers，不是transfer）
            url_path = "/api/v4/wallet/transfers"
            headers = await self._generate_signature(
                "POST", url_path, "", json.dumps(data)
            )
            headers['Content-Type'] = 'application/json'

            url = "https://api.gateio.ws" + url_path
            await self._ensure_session()

            async with self.session.post(url, headers=headers, data=json.dumps(data)) as response:
                response_text = await response.text()
                logger.info(f"Gate.io划转响应状态: {response.status}")
                logger.info(f"Gate.io划转响应内容: {response_text}")

                if response.status == 200:
                    try:
                        result = json.loads(response_text) if response_text else {}

                        # 检查响应格式是否正确（应该返回TransactionID）
                        if "tx_id" in result or not response_text.strip():
                            # 有交易ID或空响应都表示成功
                            tx_id = result.get("tx_id", "unknown")
                            logger.info(f"Gate.io转账成功: {amount} {currency} {from_account_str} -> {to_account_str}, 交易ID: {tx_id}")
                            return True
                        else:
                            logger.error(f"Gate.io划转响应格式异常: {result}")
                            return False

                    except json.JSONDecodeError:
                        # 空响应体也算成功
                        if not response_text.strip():
                            logger.info(f"Gate.io转账成功(空响应): {amount} {currency} {from_account_str} -> {to_account_str}")
                            return True
                        else:
                            logger.error(f"Gate.io划转响应解析失败: {response_text}")
                            return False
                else:
                    logger.error(f"Gate.io资金划转失败 (状态码: {response.status}): {response_text}")
                    return False

        except Exception as e:
            logger.error(f"Gate.io转账异常: {e}")
            return False

    def is_unified_account(self) -> bool:
        """Gate.io不是统一账户，使用分离账户模式"""
        return False

    def _convert_symbol(self, symbol: str, market_type: str) -> str:
        """转换交易对格式 - 使用统一适配器"""
        return self.currency_adapter.get_exchange_symbol(symbol, "gate", market_type)

    def _parse_order_status(self, status: str) -> OrderStatus:
        """解析订单状态"""
        status_map = {
            "open": OrderStatus.OPEN,
            "closed": OrderStatus.FILLED,
            "filled": OrderStatus.FILLED,  # 修复：添加filled状态映射
            "cancelled": OrderStatus.CANCELLED,
            "finished": OrderStatus.FILLED
        }
        return status_map.get(status.lower(), OrderStatus.PENDING)

    # 🔥 删除重复方法：使用统一精度处理系统
    # 价格格式化现在统一使用预加载器的format_amount_unified方法

    # 🔥 已删除重复方法：_format_amount
    # 现在统一使用预加载器的格式化方法

    # 🔥 杠杆设置已移到统一杠杆管理器
    # 请使用: from core.unified_leverage_manager import set_leverage_unified
    async def set_leverage(self, symbol: str, leverage: int, market_type: str = "futures") -> bool:
        """
        设置杠杆倍数 - 🔥 重定向到统一杠杆管理器
        此方法保留是为了兼容性，实际功能已迁移到统一杠杆管理器
        """
        from core.unified_leverage_manager import set_leverage_unified
        logger.info(f"🔄 Gate.io杠杆设置重定向到统一杠杆管理器: {symbol}")
        return await set_leverage_unified(self, symbol, leverage)

    async def get_server_time(self) -> int:
        """获取Gate.io服务器时间 - 🔥 修复：添加超时机制防止阻塞"""
        try:
            # 🔥 修复404错误：使用正确的API端点和请求方式
            # Gate.io public时间端点不需要market_type，直接调用基础URL
            await self._ensure_session()
            url = "https://api.gateio.ws/api/v4/spot/time"  # 使用完整URL，避免路径拼接问题

            # 🔥 修复：正确的异步超时写法 - 先await协程，再使用响应对象
            async with await asyncio.wait_for(self.session.get(url), timeout=5.0) as response:
                if response.status == 200:
                    result = await response.json()
                    # Gate.io返回格式: {"server_time": 1234567890}
                    if isinstance(result, dict) and "server_time" in result:
                        return int(result["server_time"]) * 1000
                    else:
                        logger.warning("Gate.io服务器时间响应格式异常，使用本地时间")
                        return int(time.time() * 1000)
                else:
                    logger.warning(f"Gate.io时间API返回状态码{response.status}，使用本地时间")
                    return int(time.time() * 1000)

        except asyncio.TimeoutError:
            logger.warning("获取Gate.io服务器时间超时(5秒)，使用本地时间")
            return int(time.time() * 1000)
        except Exception as e:
            logger.warning(f"获取Gate.io服务器时间失败: {e}，使用本地时间")
            return int(time.time() * 1000)

    # 🔥 删除重复方法：使用基类的get_current_price方法

    async def initialize(self) -> bool:
        """
        初始化Gate.io交易所
        🔥 标准化接口：所有交易所都应该有initialize方法
        """
        try:
            logger.info("🚀 初始化Gate.io交易所（分离账户模式）...")
            
            # 1. 测试连接
            server_time = await self.get_server_time()
            logger.info(f"✅ Gate.io连接成功，服务器时间: {server_time}")
            
            # 2. 验证分离账户模式
            if not self.is_unified_account():
                logger.info("✅ Gate.io使用分离账户模式")
            else:
                logger.warning("⚠️ Gate.io账户模式配置异常")
            
            # 3. 获取初始余额状态
            try:
                spot_balance = await self.get_balance(AccountType.SPOT)
                futures_balance = await self.get_balance(AccountType.FUTURES)
                
                spot_usdt = spot_balance.get("USDT", {})
                futures_usdt = futures_balance.get("USDT", {})
                
                # 处理余额格式
                if isinstance(spot_usdt, dict):
                    spot_amount = spot_usdt.get("available", 0)
                else:
                    spot_amount = float(spot_usdt) if spot_usdt else 0
                    
                if isinstance(futures_usdt, dict):
                    futures_amount = futures_usdt.get("available", 0)
                else:
                    futures_amount = float(futures_usdt) if futures_usdt else 0
                
                total_amount = spot_amount + futures_amount
                logger.info(f"✅ Gate.io初始余额: 现货{spot_amount:.2f} + 期货{futures_amount:.2f} = 总计{total_amount:.2f} USDT")
                
            except Exception as e:
                logger.warning(f"⚠️ Gate.io余额查询异常: {e}")
            
            # 4. 检查分离账户状态（无需特殊设置）
            logger.info("✅ Gate.io分离账户模式已确认")
            
            logger.info("✅ Gate.io交易所初始化完成")
            return True

        except Exception as e:
            logger.error(f"❌ Gate.io交易所初始化失败: {e}")
            return False

    async def get_order_status(self, order_id: str, symbol: str, market_type: str = "spot") -> str:
        """
        🔥 新增：获取订单状态
        :param order_id: 订单ID
        :param symbol: 交易对
        :param market_type: 市场类型
        :return: 订单状态
        """
        try:
            order_info = await self.get_order(order_id, symbol, market_type)
            return order_info.get("status", "unknown")
        except Exception as e:
            self.logger.error(f"获取订单状态失败: {e}")
            return "error"

    async def get_trading_rules(self, symbol: str = None) -> Dict[str, Any]:
        """
        🔥 新增：获取交易规则
        :param symbol: 交易对，None表示所有
        :return: 交易规则
        """
        try:
            # 使用统一交易规则预加载器
            if hasattr(self, 'trading_rules_preloader') and self.trading_rules_preloader:
                if symbol:
                    # 获取特定交易对的规则
                    rules = await self.trading_rules_preloader.get_trading_rules(symbol, "gate")
                    return {symbol: rules} if rules else {}
                else:
                    # 获取所有交易对的规则
                    return await self.trading_rules_preloader.get_all_trading_rules("gate")
            else:
                self.logger.warning("交易规则预加载器不可用")
                return {}
        except Exception as e:
            self.logger.error(f"获取交易规则失败: {e}")
            return {}


# 🔥 添加独立运行入口 - 符合方案要求
if __name__ == "__main__":
    import os
    import asyncio
    # 🔥 删除重复的load_dotenv调用 - 仅在独立测试时需要
    from dotenv import load_dotenv
    load_dotenv()  # 仅用于独立测试

    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    async def test_gate_exchange():
        """Gate交易所独立测试"""
        # 从环境变量获取API密钥
        api_key = os.getenv("GATE_API_KEY", "")
        api_secret = os.getenv("GATE_API_SECRET", "")

        if not api_key or not api_secret:
            print("❌ 请设置GATE_API_KEY和GATE_API_SECRET环境变量")
            return

        exchange = GateExchange(api_key, api_secret)

        try:
            print("🚀 Gate.io交易所独立测试开始")
            
            # 测试连接
            print("\n1. 测试连接...")
            connected = await exchange.test_connection()
            print(f"   连接状态: {'✅ 成功' if connected else '❌ 失败'}")

            if connected:
                # 获取余额
                print("\n2. 获取账户余额...")
                balance = await exchange.get_balance(AccountType.SPOT)
                print(f"   现货余额: {balance}")

                # 🔥 删除WebSocket价格缓存测试 - 统一使用OpportunityScanner.market_data
                print("\n3. 价格数据已统一使用OpportunityScanner.market_data管理")
                print("   WebSocket价格缓存已删除，使用统一数据源")

                # 获取订单簿
                print("\n4. 获取BTC-USDT现货深度...")
                orderbook = await exchange.get_orderbook("BTC-USDT", "spot", 5)
                print(f"   买1: ${orderbook['bids'][0][0]:.2f} 量: {orderbook['bids'][0][1]:.6f}")
                print(f"   卖1: ${orderbook['asks'][0][0]:.2f} 量: {orderbook['asks'][0][1]:.6f}")

                # 测试下单（仅在启用时）
                if os.environ.get("TEST_PLACE_ORDER", "false").lower() == "true":
                    print("\n5. 测试下市价单...")
                    test_amount = float(os.environ.get("TEST_AMOUNT", "0.001"))
                    # 🔥 市价单系统：删除价格参数，使用市价下单

                    try:
                        order = await exchange.place_order(
                            "BTC-USDT",
                            OrderSide.BUY,
                            OrderType.MARKET,
                            test_amount,
                            None,  # 市价单无需价格
                            "spot"
                        )
                        print(f"   下单结果: 订单ID {order.get('order_id', 'N/A')}")

                        if order.get("order_id"):
                            # 查询订单
                            order_info = await exchange.get_order(order["order_id"], "BTC-USDT", "spot")
                            print(f"   订单状态: {order_info.get('status', 'unknown')}")

                            # 取消订单
                            cancel_result = await exchange.cancel_order(order["order_id"], "BTC-USDT", "spot")
                            print(f"   取消结果: {'✅ 成功' if cancel_result else '❌ 失败'}")
                    except Exception as e:
                        print(f"   ❌ 下单测试失败: {e}")

            print("\n✅ Gate.io交易所独立测试完成")

        except Exception as e:
            print(f"❌ Gate.io交易所测试异常: {e}")
        finally:
            await exchange.close()

    # 运行测试
    asyncio.run(test_gate_exchange())