"""
OKX交易所接口实现
支持统一账户模式
"""

import asyncio
import aiohttp
import hmac
import hashlib
import base64
import time
import json
from typing import Dict, List, Optional, Any
from datetime import datetime, timezone
import logging
import os

from .exchanges_base import BaseExchange, OrderType, OrderSide, OrderStatus, AccountType
from utils.logger import get_logger
# 🔥 删除未使用的导入：统一管理器由系统统一初始化

logger = logging.getLogger(__name__)


class OKXExchange(BaseExchange):
    """OKX交易所实现"""

    def __init__(self, api_key: str, api_secret: str, passphrase: str, **kwargs):
        """初始化OKX交易所接口"""
        super().__init__("OKX", api_key, api_secret, **kwargs)

        # 🔥 统一日志系统 - 使用标准logger获取方式，确保详细日志记录
        self.logger = get_logger(self.__class__.__name__)

        # 🔥 确保OKX日志记录到专用文件
        okx_logger = logging.getLogger('OKXExchange')
        okx_logger.setLevel(logging.DEBUG)

        # 创建OKX专用日志文件处理器
        log_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "logs")
        os.makedirs(log_dir, exist_ok=True)
        okx_log_file = os.path.join(log_dir, "OKXExchange.log")

        # 检查是否已经有文件处理器
        has_file_handler = any(isinstance(h, logging.FileHandler) and h.baseFilename.endswith('OKXExchange.log')
                              for h in okx_logger.handlers)

        if not has_file_handler:
            file_handler = logging.FileHandler(okx_log_file, encoding='utf-8')
            file_handler.setLevel(logging.DEBUG)
            formatter = logging.Formatter(
                '%(asctime)s [%(levelname)s] [%(name)s] %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
            file_handler.setFormatter(formatter)
            okx_logger.addHandler(file_handler)
            okx_logger.propagate = False  # 避免重复记录到根日志器

        # 使用OKX专用日志器
        self.logger = okx_logger

        # API endpoint
        self.base_url = "https://www.okx.com"

        # API访问凭据
        self.api_key = api_key
        self.api_secret = api_secret
        self.passphrase = passphrase

        # 🔥 最简化统一初始化 - 一行代码完成所有模块和配置设置
        from exchanges.unified_exchange_initializer import setup_exchange_unified
        setup_exchange_unified("OKX", self)

        # 🔥 统一会话管理 - 使用第19个核心统一模块
        from core.unified_http_session_manager import get_unified_session_manager
        self.session_manager = get_unified_session_manager()
        self.session = None

        # 使用统一账户
        self.unified_account = True

        # 交易对信息缓存
        self.instrument_info_cache = {}

        # 使用服务器时间模式
        self.use_server_time = True
        self.server_time_offset = 0
        self.last_server_time_check = 0

        # 🔥 新增：账户初始化标志
        self.account_initialized = False

        # 🔥 新增：统一模式支持标记，避免重复尝试设置
        self._unified_mode_supported = None  # None=未检查, True=支持, False=不支持

        # 🔥 根源修复：大幅降低OKX API限制，确保30+代币健壮启动
        # 从3次/秒降低到2次/秒，配合5秒冷却时间，确保绝不触发限速
        if not hasattr(self, 'rate_limit') or self.rate_limit > 2:
            self.rate_limit = 2  # 🔥 根源修复：降低到2次/秒，配合5秒冷却
            logger.info(f"🔧 OKX API限制根源修复为{self.rate_limit}次/秒，确保30+代币健壮启动")

        logger.info(f"OKX交易所初始化完成: 统一账户模式, 默认杠杆={self.default_leverage}倍, API限制={self.rate_limit}次/秒")

        # 🔥 新增：初始化API调用优化器引用
        self._api_optimizer = None

    def _convert_symbol(self, symbol: str, market_type: str = "spot") -> str:
        """转换交易对格式 - 🔥 使用统一货币适配器"""
        # 🔥 修复：使用统一的货币适配器，避免绕过统一模块
        from exchanges.currency_adapter import get_exchange_symbol
        return get_exchange_symbol(symbol, "okx", market_type)



    async def _ensure_session(self):
        """确保会话存在 - 🔥 使用统一会话管理器"""
        if self.session is None or self.session.closed:
            self.session = await self.session_manager.get_session("okx")

    async def close(self):
        """关闭会话 - 🔥 使用统一会话管理器"""
        try:
            await self.session_manager.close_session("okx")
            self.session = None
        except Exception as e:
            self.logger.error(f"关闭OKX会话失败: {e}")

    async def _ensure_account_ready_for_futures(self):
        """🔥 确保账户已准备好进行期货交易 - 按照老版本模式"""
        try:
            if self.account_initialized:
                return True

            logger.info("🔧 OKX初始化期货账户配置...")

            # 1. 检查账户配置
            config = await self.get_account_config()
            if not config:
                logger.warning("⚠️ 无法获取OKX账户配置，使用默认设置")
                return True

            # 2. 检查账户等级（统一账户模式）
            acct_lv = config.get("acctLv", "1")
            if acct_lv != "3":
                logger.warning(f"⚠️ OKX账户等级不是统一账户模式: {acct_lv}")
                # 不强制升级，使用当前模式

            # 3. 检查持仓模式
            pos_mode = config.get("posMode", "net_mode")
            logger.info(f"✅ OKX持仓模式: {pos_mode}")

            self.account_initialized = True
            logger.info("✅ OKX期货账户配置检查完成")
            return True

        except Exception as e:
            logger.warning(f"⚠️ OKX期货账户配置检查失败: {e}")
            # 不阻止交易，继续使用默认配置
            return True

    async def get_position_mode(self) -> str:
        """获取持仓模式"""
        try:
            config = await self.get_account_config()
            return config.get("posMode", "net_mode")
        except Exception as e:
            logger.warning(f"获取OKX持仓模式失败: {e}")
            return "net_mode"

    # 🔥 删除重复的方法 - 已在第119行定义，避免重复造轮子

    def _get_timestamp(self) -> str:
        """获取时间戳 - 使用ISO格式（参考官方SDK）"""
        if self.use_server_time:
            # 使用服务器时间
            current_time = time.time()
            if current_time - self.last_server_time_check > 300:  # 5分钟更新一次偏移
                # 需要更新服务器时间偏移，但这里先用本地时间 + 偏移
                pass

            # 使用UTC时间 + 偏移
            utc_time = datetime.fromtimestamp(current_time + self.server_time_offset, tz=timezone.utc)
        else:
            # 使用本地UTC时间
            utc_time = datetime.now(timezone.utc)

        # 🔥 修复：格式化为正确的ISO字符串（毫秒精度），移除时区信息
        # OKX要求格式：2020-12-08T09:08:57.715Z
        timestamp = utc_time.strftime("%Y-%m-%dT%H:%M:%S.%f")[:-3] + "Z"
        return timestamp

    async def _rate_limit_with_optimizer(self):
        """🔥 新增：使用API调用优化器的精确限速控制"""
        try:
            # 懒加载API调用优化器
            if self._api_optimizer is None:
                from core.api_call_optimizer import get_api_optimizer
                self._api_optimizer = get_api_optimizer()

            # 使用API调用优化器的精确限速控制
            await self._api_optimizer._rate_limit_wait("okx")

        except Exception as e:
            # 如果API调用优化器不可用，回退到基类的限速方法
            logger.warning(f"⚠️ API调用优化器不可用，使用基类限速: {e}")
            await super()._rate_limit()

    async def _get_server_time(self) -> Optional[str]:
        """获取服务器时间并计算偏移 - 🔥 使用统一会话管理器"""
        try:
            url = f"{self.base_url}/api/v5/public/time"

            # 🔥 修复：使用统一会话管理器，避免直接创建会话
            await self._ensure_session()

            local_before = time.time()
            async with self.session.get(url, timeout=aiohttp.ClientTimeout(total=5)) as response:
                local_after = time.time()
                response_text = await response.text()
                result = json.loads(response_text)

                if result.get("code") == "0" and result.get("data"):
                    # 🔥 修复：处理不同的数据结构
                    data = result["data"]
                    if isinstance(data, list) and len(data) > 0:
                        server_timestamp_ms = int(data[0]["ts"])
                    elif isinstance(data, dict) and "ts" in data:
                        server_timestamp_ms = int(data["ts"])
                    else:
                        logger.warning(f"OKX服务器时间响应格式异常: {data}")
                        return self._get_timestamp()

                    server_time = server_timestamp_ms / 1000

                    # 计算网络延迟
                    network_delay = (local_after - local_before) / 2

                    # 计算时间偏移
                    self.server_time_offset = server_time - local_after + network_delay
                    self.last_server_time_check = time.time()

                    logger.debug(f"OKX服务器时间同步: 偏移={self.server_time_offset:.3f}秒")
                    return self._get_timestamp()

        except Exception as e:
            logger.warning(f"获取OKX服务器时间失败: {e}")
            # 回退到本地时间
            self.use_server_time = False

        return self._get_timestamp()

    def _generate_signature(self, timestamp: str, method: str, path: str, body: str = "") -> str:
        """生成签名（参考官方SDK实现）"""
        # 构建待签名字符串
        message = timestamp + method.upper() + path + body

        # 生成HMAC-SHA256签名
        signature = base64.b64encode(
            hmac.new(
                self.api_secret.encode('utf-8'),
                message.encode('utf-8'),
                hashlib.sha256
            ).digest()
        ).decode()

        return signature

    def _get_headers(self, timestamp: str, method: str, path: str, body: str = "") -> Dict[str, str]:
        """获取请求头（用于测试）"""
        signature = self._generate_signature(timestamp, method, path, body)
        return {
            "OK-ACCESS-KEY": self.api_key,
            "OK-ACCESS-SIGN": signature,
            "OK-ACCESS-TIMESTAMP": timestamp,
            "OK-ACCESS-PASSPHRASE": self.passphrase,
            "Content-Type": "application/json"
        }

    async def _request(self, method: str, endpoint: str, params: Dict = None,
                      data: Dict = None, signed: bool = True) -> Dict:
        """发送请求"""
        await self._ensure_session()
        await self._rate_limit_with_optimizer()

        try:
            url = f"{self.base_url}{endpoint}"

            # 获取时间戳
            timestamp = self._get_timestamp()

            # 如果需要更新服务器时间，先同步
            if (self.use_server_time and
                time.time() - self.last_server_time_check > 300):
                timestamp = await self._get_server_time() or timestamp

            # 构建请求路径
            if method == "GET" and params:
                query_string = "&".join([f"{k}={v}" for k, v in params.items()])
                path = f"{endpoint}?{query_string}"
            else:
                path = endpoint

            # 构建请求体
            body = json.dumps(data) if data else ""

            # 生成签名
            signature = self._generate_signature(timestamp, method, path, body)

            # 设置请求头 - 🔥 修复Cloudflare拦截问题
            headers = {
                "OK-ACCESS-KEY": self.api_key,
                "OK-ACCESS-SIGN": signature,
                "OK-ACCESS-TIMESTAMP": timestamp,
                "OK-ACCESS-PASSPHRASE": self.passphrase,
                "Content-Type": "application/json",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
                "Accept": "application/json",
                "Accept-Language": "en-US,en;q=0.9",
                "Accept-Encoding": "gzip, deflate, br",
                "Connection": "keep-alive",
                "Upgrade-Insecure-Requests": "1"
            }

            # 详细记录请求信息用于调试
            logger.debug(f"OKX请求: {method} {url}")
            logger.debug(f"OKX时间戳: {timestamp}")
            if method == "GET":
                logger.debug(f"OKX请求参数: {params}")
            else:
                logger.debug(f"OKX请求数据: {body}")

            # 发送请求
            if method == "GET":
                async with self.session.get(url, headers=headers, params=params) as response:
                    response_text = await response.text()
                    try:
                        result = json.loads(response_text)
                    except Exception as e:
                        logger.error(f"OKX响应解析失败: {e}, 原始响应: {response_text}")
                        raise Exception(f"OKX响应解析失败: {response_text}")
            else:
                async with self.session.request(
                    method, url, headers=headers, data=body
                ) as response:
                    response_text = await response.text()
                    try:
                        result = json.loads(response_text)
                    except Exception as e:
                        logger.error(f"OKX响应解析失败: {e}, 原始响应: {response_text}")
                        raise Exception(f"OKX响应解析失败: {response_text}")

            # 记录响应信息
            logger.debug(f"OKX响应: {result}")

            # 🔥 修复：OKX API错误处理 - 检查code是否为"0"
            if result.get("code") != "0":
                self.error_count += 1
                error_code = result.get('code', 'unknown')
                error_msg = result.get('msg', 'Unknown error')

                # 🔥 根据错误类型选择合适的日志级别
                if error_code == "51001":
                    # 🚀 通用期货溢价套利系统：交易对不存在是正常情况，使用DEBUG级别
                    logger.debug(f"OKX交易对不存在 - 通用系统正常情况:")
                    logger.debug(f"  - 错误代码: {error_code}")
                    logger.debug(f"  - 错误信息: {error_msg}")
                    logger.debug(f"  - 请求端点: {endpoint}")
                    logger.debug("这是通用系统的正常情况，返回空数据让上层优雅处理")
                    # 🔥 关键修复：51001错误直接返回空数据，不抛出异常
                    return result.get("data", [])
                else:
                    # 其他错误使用ERROR级别详细记录
                    logger.error(f"OKX API错误详情:")
                    logger.error(f"  - 错误代码: {error_code}")
                    logger.error(f"  - 错误信息: {error_msg}")
                    logger.error(f"  - 完整响应: {result}")
                    logger.error(f"  - 请求端点: {endpoint}")
                    logger.error(f"  - 请求方法: {method}")

                # 🔥 新增：检查data数组中的sCode，特别处理51400错误
                data_array = result.get("data", [])
                if isinstance(data_array, list) and len(data_array) > 0:
                    for data_item in data_array:
                        if isinstance(data_item, dict):
                            s_code = data_item.get("sCode", "")
                            s_msg = data_item.get("sMsg", "")

                            # 🚨 特殊处理51400错误：订单取消失败-订单已处理
                            if s_code == "51400":
                                logger.info(f"OKX检测到51400错误：{s_msg}")
                                raise Exception(f"OKX_ORDER_ALREADY_PROCESSED_51400: {s_msg}")

                            # 记录其他错误代码
                            if s_code:
                                logger.error(f"  - 子错误代码: {s_code}")
                                logger.error(f"  - 子错误信息: {s_msg}")

                # 🚨 特定错误代码的处理策略
                if error_code == "1":
                    # 代码1通常表示系统错误或参数错误
                    logger.error("OKX错误代码1：系统错误或参数错误，可能的原因：")
                    logger.error("  1. 账户模式设置错误（需要检查tdMode）")
                    logger.error("  2. 交易对格式错误")
                    logger.error("  3. 请求参数格式错误")
                    logger.error("  4. 账户权限不足")
                elif error_code in ["51008", "51003"]:
                    # 这些是已知的账户限制错误
                    logger.warning(f"OKX账户限制错误（{error_code}）: {error_msg}")
                elif error_code == "51070":
                    # 账户模式升级要求错误 - 不应该阻止交易
                    logger.warning(f"OKX账户模式限制（{error_code}）: {error_msg}")
                    logger.warning("账户可能不支持统一账户模式，将使用当前账户模式继续交易")
                    # 对于51070错误，我们不抛出异常，而是继续使用当前账户模式
                    return result.get("data", [])


                full_error_msg = f"{error_msg} (代码: {error_code})"
                raise Exception(f"OKX API错误: {full_error_msg}")

            return result.get("data", [])

        except Exception as e:
            self.error_count += 1
            # 特殊处理51008错误（账户限制：保证金不足）
            error_str = str(e)

            # 🔥 新增：特殊处理51400错误（订单取消失败-订单已处理）
            # 这种情况在cancel_order中应该视为成功
            if "51400" in error_str or "Order cancellation failed as the order has been filled, canceled or does not exist" in error_str:
                # 为cancel_order方法创建特殊标记
                raise Exception(f"OKX_ORDER_ALREADY_PROCESSED_51400: {error_str}")

            # 🔥 关键修复：详细分析51008错误并提供解决方案
            if "51008" in error_str or "Insufficient USDT margin" in error_str or "available margin (in USD) is too low for borrowing" in error_str:
                logger.error(f"🚨 OKX API错误51008: 保证金不足")
                logger.error(f"💡 错误原因分析:")
                logger.error(f"   1. USDT余额不足以支持当前交易量")
                logger.error(f"   2. 账户模式可能未设置为统一账户（acctLv=3）")
                logger.error(f"   3. 杠杆设置可能过高，需要更多保证金")
                logger.error(f"   4. 交易量过大，超出账户保证金能力")
                logger.error(f"💊 解决方案:")
                logger.error(f"   1. 增加USDT余额到账户")
                logger.error(f"   2. 降低交易量（当前系统已自动使用极小量）")
                logger.error(f"   3. 设置更低的杠杆（当前使用2倍）")
                logger.error(f"   4. 确保账户为统一账户模式")

                raise Exception(f"OKX_MARGIN_INSUFFICIENT_51008: 保证金不足，请检查USDT余额或降低交易量")
            else:
                logger.error(f"OKX API调用失败: {e}")
                # 添加更多详细错误信息
                if hasattr(e, 'args') and e.args:
                    logger.error(f"错误详情: {e.args}")
                raise

    async def get_balance(self, account_type: AccountType = AccountType.SPOT) -> Dict[str, Any]:
        """获取账户余额（优先使用统一账户API）"""
        try:
            # 🔥 修复：根据方案文档，OKX应该是统一账户，优先使用统一账户API
            # 如果失败，再尝试资金账户API作为备用
            response = None

            try:
                # 首先尝试统一账户API
                response = await self._request("GET", "/api/v5/account/balance")
                logger.debug("OKX使用统一账户API获取余额成功")
            except Exception as e:
                logger.warning(f"OKX统一账户API失败，尝试资金账户API: {e}")
                try:
                    # 备用：资金账户API
                    response = await self._request("GET", "/api/v5/asset/balances")
                    logger.debug("OKX使用资金账户API获取余额成功")
                except Exception as e2:
                    logger.error(f"OKX所有余额API都失败: 统一账户={e}, 资金账户={e2}")
                    raise e2

            balances = {}

            # 🔥 修复：确保返回正确的字典格式 - 期现套利系统要求
            # 默认包含USDT账户
            balances["USDT"] = {
                'available': 0.0,
                'locked': 0.0
            }

            # 🔥 修复：智能解析不同API的响应格式
            if response and isinstance(response, list):
                # 检测响应格式并相应解析
                if len(response) > 0 and isinstance(response[0], dict):
                    first_item = response[0]

                    if "details" in first_item:
                        # 统一账户API格式：包含details字段
                        logger.debug("OKX检测到统一账户API响应格式")
                        for account in response:
                            if isinstance(account, dict) and "details" in account:
                                for detail in account.get("details", []):
                                    try:
                                        currency = detail.get("ccy", "")
                                        if not currency:
                                            continue

                                        # 可用余额 - OKX使用availBal字段
                                        available = float(detail.get("availBal", 0))
                                        # 冻结余额 - OKX使用frozenBal字段
                                        locked = float(detail.get("frozenBal", 0))

                                        balances[currency] = {
                                            'available': available,
                                            'locked': locked
                                        }

                                        logger.debug(f"OKX统一账户 {currency} 余额: 可用={available}, 冻结={locked}")

                                    except Exception as detail_err:
                                        logger.error(f"OKX解析统一账户余额失败: {detail_err}, 详情: {detail}")
                                        continue
                    else:
                        # 资金账户API格式：直接是币种列表
                        logger.debug("OKX检测到资金账户API响应格式")
                        for item in response:
                            try:
                                currency = item.get("ccy", "")
                                if not currency:
                                    continue

                                # 资金账户余额字段
                                available = float(item.get("availBal", 0))
                                locked = float(item.get("frozenBal", 0))

                                balances[currency] = {
                                    'available': available,
                                    'locked': locked
                                }

                                logger.debug(f"OKX资金账户 {currency} 余额: 可用={available}, 冻结={locked}")

                            except Exception as item_err:
                                logger.error(f"OKX解析资金账户余额失败: {item_err}, 详情: {item}")
                                continue
            else:
                logger.warning(f"OKX余额响应格式异常: {response}")

            logger.debug(f"OKX余额解析完成: {balances}")
            return balances

        except Exception as e:
            logger.error(f"获取OKX余额失败: {e}")
            # 返回默认格式，避免测试失败
            return {"USDT": {'available': 0.0, 'locked': 0.0}}

    # 🔥 删除RestAPI价格获取方法 - 统一使用WebSocket+缓存
    # async def get_ticker() 方法已删除，改为使用 websocket_price_cache.py
    # 所有价格数据通过WebSocket实时获取，避免RestAPI延迟

    async def get_orderbook(self, symbol: str, market_type: str = "spot", limit: int = 10) -> Dict[str, Any]:
        """
        🚨 严禁REST API - 只能使用WebSocket数据源

        Args:
            symbol: 交易对
            market_type: 市场类型
            limit: 深度限制

        Returns:
            空订单簿（强制使用WebSocket）
        """
        # 🚨 严禁REST API调用！订单簿数据只能来自WebSocket！
        self.logger.warning(f"🚨 get_orderbook已禁用！订单簿数据只能使用WebSocket！")
        self.logger.warning(f"   请使用: OpportunityScanner.market_data['{self.name.lower()}_{market_type}_{symbol}']")

        # 🔥 返回空订单簿，强制使用WebSocket数据流
        return {
            'asks': [],
            'bids': [],
            'timestamp': int(time.time() * 1000),
            'symbol': symbol,
            'exchange': 'okx',
            'error': 'REST_API_DISABLED_USE_WEBSOCKET'
        }

    async def get_instrument_info(self, symbol: str, market_type: str) -> Dict[str, Any]:
        """获取交易对的配置信息"""
        try:
            # 转换交易对格式以确保与API一致
            instId = self._convert_symbol(symbol, market_type)
            instType = "SPOT" if market_type == "spot" else "SWAP"

            # 检查缓存
            cache_key = f"{instId}_{instType}"
            if cache_key in self.instrument_info_cache:
                logger.debug(f"OKX使用缓存的交易对信息: {cache_key}")
                return self.instrument_info_cache[cache_key]

            # 从API获取
            logger.info(f"OKX获取交易对信息: {instId}, {instType}")
            response = await self._request(
                "GET", "/api/v5/public/instruments",
                params={"instType": instType, "instId": instId}
            )

            # 如果没有找到，返回默认值
            if not response or len(response) == 0:
                logger.warning(f"OKX未找到交易对信息: {instId}, {instType}，使用默认值")
                default_info = {
                    "min_size": 0.001,
                    "size_increment": 0.001,
                    "lot_size": 0.001 if market_type == "spot" else 0.01,  # 期货默认批量单位为0.01
                    "tick_size": 0.1,
                    "min_notional": 5.0
                }
                # 记录默认值添加到缓存
                self.instrument_info_cache[cache_key] = default_info
                return default_info

            # OKX交易对信息
            info = response[0]

            # 解析关键参数
            result = {
                "min_size": float(info.get("minSz", 0.001)),  # 最小下单数量
                "size_increment": float(info.get("lotSz", 0.001)),  # 数量精度
                "lot_size": float(info.get("lotSz", 0.001)),  # 合约单位
                "tick_size": float(info.get("tickSz", 0.1)),  # 价格精度
                "min_notional": 5.0  # 最小名义价值
            }

            # 🔥 关键修复：OKX期货数量放大10倍问题（21.6→216）
            # 问题根因：错误地将OKX期货lot_size强制调整为0.01，导致数量被错误放大
            # 解决方案：移除强制调整逻辑，使用API返回的真实lot_size，确保与Gate.io、Bybit一致
            #
            # 🚨 删除错误的强制调整逻辑：
            # if market_type == "futures" and result["lot_size"] < 0.01:
            #     result["lot_size"] = 0.01  # ❌ 这是错误的！会导致数量放大
            #
            # ✅ 正确做法：直接使用API返回的lot_size

            logger.info(f"✅ OKX期货使用API真实lot_size: {result['lot_size']} (修复数量放大问题)")

            # 🔥 验证修复效果：记录关键参数
            logger.info(f"🔍 OKX {symbol} {market_type} 精度参数:")
            logger.info(f"   lot_size: {result['lot_size']} (步长)")
            logger.info(f"   min_size: {result['min_size']} (最小数量)")
            logger.info(f"   size_increment: {result['size_increment']} (数量精度)")

            logger.info(f"✅ OKX期货使用协调后lot_size: {result['lot_size']} (解决21.7→217问题)")

            # 🔥 验证修复效果：记录关键参数
            logger.info(f"🔍 OKX {symbol} {market_type} 精度参数:")
            logger.info(f"   lot_size: {result['lot_size']} (步长)")
            logger.info(f"   min_size: {result['min_size']} (最小数量)")
            logger.info(f"   size_increment: {result['size_increment']} (数量精度)")

            # 🔥 三交易所一致性检查：确保OKX与Gate.io、Bybit处理逻辑一致
            if market_type == "futures":
                logger.info(f"🔧 OKX期货数量处理修复: 使用真实API步长 {result['lot_size']}，确保与Gate.io、Bybit一致")

            # 存入缓存
            self.instrument_info_cache[cache_key] = result

            logger.debug(f"OKX交易对信息: {symbol}, {market_type}, {result}")
            return result

        except Exception as e:
            logger.error(f"获取OKX交易对信息失败: {e}")
            # 🔥 修复：删除错误的强制调整逻辑，使用真实API数据
            # 原因：0.01强制调整导致19.4变成194的数量放大问题
            default_info = {
                "min_size": 0.001,
                "size_increment": 0.001,
                "lot_size": 0.001,  # 🔥 修复：统一使用0.001，不再强制调整
                "tick_size": 0.1,
                "min_notional": 5.0
            }
            logger.warning(f"🔧 OKX使用默认配置（已修复强制调整问题）: {symbol} {market_type}")
            return default_info

    # 🔥 删除重复方法：_format_amount
    # 现在统一使用预加载器的格式化方法

    async def create_spot_order(self, symbol: str, side, order_type,
                               amount: float, price: Optional[float] = None,
                               params: Optional[Dict] = None) -> Dict[str, Any]:
        """
        🔥 关键修复：直接现货下单方法，强制使用市价单
        专门为SpotTrader提供的底层API调用
        """
        try:
            # 处理参数类型
            if hasattr(side, 'value'):
                side_str = side.value
            else:
                side_str = str(side).lower()

            # 🔥 强制使用市价单，禁止限价单
            order_type_str = "market"
            logger.info(f"🔥 OKX现货强制使用市价单: {symbol} {side_str}")

            # 转换交易对格式
            inst_id = self._convert_symbol(symbol, "spot")

            # 🔥 使用统一模块处理精度，删除重复实现
            from core.trading_rules_preloader import get_trading_rules_preloader
            rules_preloader = get_trading_rules_preloader()
            formatted_amount = rules_preloader.truncate_to_step_size(amount, "okx", symbol, "spot")

            # 🔥 强制市价单参数
            order_data = {
                "instId": inst_id,
                "tdMode": "cash",  # 现货模式
                "side": side_str,
                "ordType": "market",  # 🔥 强制市价单
                "sz": str(formatted_amount)  # 🔥 使用统一精度处理
            }

            logger.info(f"🔧 OKX现货统一精度处理: {amount} -> {formatted_amount}")
            
            # 🔥 关键修复：OKX现货市价买单需要指定tgtCcy
            # tgtCcy="base_ccy": sz表示基础币种数量（如8.76 DOT）
            # tgtCcy="quote_ccy": sz表示报价币种金额（如8.76 USDT）
            if side_str.lower() == "buy":
                order_data["tgtCcy"] = "base_ccy"  # 确保sz是基础币种数量
                logger.info(f"🔧 OKX现货市价买单设置tgtCcy=base_ccy: {amount} {inst_id.split('-')[0]}")

            logger.info(f"🔍 OKX现货市价单: {order_data}")

            # 直接调用OKX API
            response = await self._request(
                "POST", "/api/v5/trade/order",
                data=order_data
            )

            if response and len(response) > 0:
                result = response[0]

                # 🔥 关键修复：查询实际成交价格（与期货保持一致）
                order_id = result.get("ordId", "")
                executed_price = await self._get_order_executed_price(order_id, symbol, "SPOT")

                # 🔥 统一修复：确保OKX现货返回标准字段，与Gate.io和Bybit保持一致
                result_data = {
                    "order_id": order_id,
                    "symbol": symbol,
                    "side": side_str,
                    "type": "market",  # 🔥 强制返回市价单
                    "amount": amount,  # 🔥 币数量（SpotTrader备用字段）
                    "actual_amount": amount,  # 🔥 币数量（与Gate.io一致）
                    "filled": amount,  # 🔥 币数量（标准字段）
                    "executed_quantity": amount,  # 🔥 币数量（标准字段）
                    "price": 0,  # 🔥 市价单价格为0
                    "executed_price": executed_price,  # 🔥 关键修复：返回实际成交价格
                    "average": executed_price,  # 🔥 平均成交价格（与Gate.io和Bybit保持一致）
                    "status": "open",
                    "timestamp": int(time.time() * 1000)
                }

                logger.info(f"✅ OKX现货返回: amount={result_data['amount']}, executed_price={executed_price:.8f}")
                return result_data
            else:
                raise Exception(f"OKX现货下单失败: {response}")

        except Exception as e:
            logger.error(f"OKX现货直接下单异常: {e}")
            raise

    async def create_futures_order(self, symbol: str, side, order_type,
                                  amount: float, price: Optional[float] = None,
                                  params: Optional[Dict] = None, skip_contract_conversion: bool = False) -> Dict[str, Any]:
        """
        🔥 关键修复：直接期货下单方法，强制使用市价单
        专门为FuturesTrader提供的底层API调用
        """
        try:
            # 处理参数类型
            if hasattr(side, 'value'):
                side_str = side.value
            else:
                side_str = str(side).lower()

            # 🔥 强制使用市价单，禁止限价单
            order_type_str = "market"
            logger.info(f"🔥 OKX期货强制使用市价单: {symbol} {side_str}")

            # 转换交易对格式
            inst_id = self._convert_symbol(symbol, "futures")

            # 🔥 关键修复：确保账户模式和持仓模式正确设置
            await self._ensure_account_ready_for_futures()

            # 🔥 修复：获取正确的持仓模式和账户配置
            account_config = await self.get_account_config()
            pos_mode = account_config.get("posMode", "net_mode")
            acct_lv = account_config.get("acctLv", "2")

            logger.info(f"🔧 OKX账户配置: 账户等级={acct_lv}, 持仓模式={pos_mode}")

            # 🔥 关键修复：平仓时跳过合约转换，开仓时进行合约转换
            if skip_contract_conversion:
                # 平仓模式：直接使用持仓数量，不进行合约转换
                formatted_amount = str(amount)
                logger.info(f"🔧 OKX期货平仓模式: 跳过合约转换，直接使用 {amount}")
            else:
                # 开仓模式：进行合约转换
                # 防御性检查：确保trading_rules_preloader属性存在
                if not hasattr(self, 'trading_rules_preloader') or self.trading_rules_preloader is None:
                    logger.warning("⚠️ trading_rules_preloader不存在，重新初始化统一模块")
                    from exchanges.unified_exchange_initializer import setup_exchange_unified
                    setup_exchange_unified("OKX", self)

                # 🔥 使用统一的合约转换方法 - 删除重复逻辑
                formatted_amount = await self.trading_rules_preloader.format_amount_with_contract_conversion(
                    amount, self, symbol, "futures"
                )
                logger.info(f"🔧 OKX期货开仓模式: 合约转换 {amount} -> {formatted_amount}")

            # 🔥 强制市价单参数 - 根据账户模式调整参数
            order_data = {
                "instId": inst_id,
                "side": side_str,
                "ordType": "market",  # 🔥 强制市价单
                "sz": str(formatted_amount)  # 🔥 使用统一精度处理
            }

            logger.info(f"🔧 OKX期货统一精度处理: {amount} -> {formatted_amount}")

            # 🔥 关键修复：根据老版本成功实现恢复tdMode参数
            # 老版本验证：单币种保证金模式必须使用isolated，不能用cross
            if acct_lv == "3":
                # 统一账户模式（Portfolio margin）
                order_data["tdMode"] = "cross"
                logger.info(f"🔧 OKX统一账户模式，设置tdMode=cross")
            elif acct_lv == "2":
                # 单币种保证金模式（Single-currency margin）
                order_data["tdMode"] = "isolated"  # 🔥 恢复老版本：单币种保证金必须用isolated
                logger.info(f"🔧 OKX单币种保证金模式，设置tdMode=isolated")
            else:
                # 简单交易模式（Cash）
                order_data["tdMode"] = "cash"
                logger.info(f"🔧 OKX简单交易模式，设置tdMode=cash")

            # 🔥 关键修复：根据持仓模式和传入的position_side设置posSide参数
            position_side_from_params = params.get("position_side") if params else None

            if pos_mode == "long_short_mode":
                # 开平仓模式：需要明确指定posSide
                if position_side_from_params:
                    # 使用期货交易器传递的position_side
                    if position_side_from_params.lower() == "long":
                        order_data["posSide"] = "long"
                    elif position_side_from_params.lower() == "short":
                        order_data["posSide"] = "short"
                    else:
                        order_data["posSide"] = "net"
                    logger.info(f"🔧 OKX开平仓模式，使用传入的position_side: {order_data['posSide']}")
                else:
                    # 根据订单方向推断
                    if side_str.lower() == "buy":
                        order_data["posSide"] = "long"
                    else:
                        order_data["posSide"] = "short"
                    logger.info(f"🔧 OKX开平仓模式，根据订单方向推断posSide: {order_data['posSide']}")
            else:
                # 买卖模式：posSide为net
                order_data["posSide"] = "net"
                logger.info(f"🔧 OKX买卖模式，设置posSide: net")

            # 处理额外参数
            if params:
                # 处理reduce_only参数
                if params.get("reduce_only") or params.get("is_closing"):
                    order_data["reduceOnly"] = True
                    logger.info(f"🔧 OKX设置reduceOnly=True（平仓单）")

            logger.info(f"🔍 OKX期货市价单（修复后）: {order_data}")

            # 🔥 关键调试：详细记录数量处理链路
            logger.info(f"🚀 OKX期货下单请求详细信息:")
            logger.info(f"   原始数量: {amount}")
            logger.info(f"   格式化数量: {formatted_amount}")
            logger.info(f"   API sz参数: '{order_data['sz']}'")
            logger.info(f"   完整请求: {order_data}")

            response = await self._request(
                "POST", "/api/v5/trade/order",
                data=order_data
            )

            logger.info(f"🔍 OKX期货下单响应: {response}")

            # 🔥 关键调试：验证API实际执行的数量
            if response and len(response) > 0:
                result = response[0]
                api_order_id = result.get("ordId", "")
                logger.info(f"🎯 OKX期货API执行验证:")
                logger.info(f"   订单ID: {api_order_id}")
                logger.info(f"   请求sz: '{order_data['sz']}'")
                logger.info(f"   响应数据: {result}")

                # 如果用户报告数量放大，这里会显示真实情况
                if float(order_data['sz']) != amount:
                    logger.error(f"❌ 检测到数量不匹配!")
                    logger.error(f"   原始数量: {amount}")
                    logger.error(f"   API数量: {order_data['sz']}")
                    logger.error(f"   放大倍数: {float(order_data['sz']) / amount:.2f}x")
                else:
                    logger.info(f"✅ 数量匹配正确: {amount} = {order_data['sz']}")

            if response and len(response) > 0:
                result = response[0]

                # 🔥 修复：根据官方SDK检查订单结果中的sCode
                s_code = result.get("sCode", "0")
                if s_code != "0":
                    s_msg = result.get("sMsg", "Unknown error")
                    logger.error(f"❌ OKX期货下单失败: sCode={s_code}, sMsg={s_msg}")
                    logger.error(f"❌ 下单参数: {order_data}")

                    # 🔥 增强错误信息，帮助调试
                    error_details = f"OKX API错误: {s_msg} (代码: {s_code})"
                    if s_code == "1":
                        error_details += " - 可能原因: 参数错误、账户模式不匹配、保证金不足"
                    elif s_code == "51008":
                        error_details += " - 可能原因: 保证金不足"
                    elif s_code == "51004":
                        error_details += " - 可能原因: 账户模式错误"

                    raise Exception(error_details)

                # 🔥 关键修复：查询实际成交价格（与Bybit保持一致）
                order_id = result.get("ordId", "")
                executed_price = await self._get_order_executed_price(order_id, symbol, "SWAP")

                # 🔥 统一修复：确保OKX期货返回标准字段，与Bybit和Gate.io保持一致
                result_data = {
                    "order_id": order_id,
                    "symbol": symbol,
                    "side": side_str,
                    "type": "market",  # 🔥 强制返回市价单
                    "amount": amount,  # 🔥 原始币数量（备用字段）
                    "actual_amount": float(formatted_amount),  # 🔥 实际下单数量（合约张数）
                    "filled": float(formatted_amount),  # 🔥 实际下单数量（合约张数）
                    "executed_quantity": float(formatted_amount),  # 🔥 关键修复：返回实际的合约张数
                    "price": 0,  # 🔥 市价单价格为0
                    "executed_price": executed_price,  # 🔥 关键修复：返回实际成交价格
                    "average": executed_price,  # 🔥 平均成交价格（与Bybit和Gate.io保持一致）
                    "status": "open",
                    "timestamp": int(time.time() * 1000)
                }
                
                logger.info(f"✅ OKX期货返回: amount={result_data['amount']}, executed_price={executed_price:.8f}")
                return result_data
            else:
                raise Exception(f"OKX期货下单失败: {response}")

        except Exception as e:
            logger.error(f"OKX期货直接下单异常: {e}")
            raise

    async def _get_order_executed_price(self, order_id: str, symbol: str, inst_type: str) -> float:
        """
        🔥 关键修复：获取OKX订单的实际成交价格
        解决OKX期货API不返回executed_price的问题，与Bybit修复保持一致
        """
        try:
            # 🔥 修复：正确转换为OKX格式的symbol
            # 将SPK-USDT转换为SPK-USDT（OKX使用相同格式）
            okx_symbol = symbol  # OKX实际使用相同的格式

            # 🔥 关键修复：查询订单详情时必须包含instId参数
            params = {
                "instType": inst_type,
                "instId": okx_symbol,  # 🔥 修复：添加必需的instId参数
                "ordId": order_id
            }

            response = await self._request("GET", "/api/v5/trade/order", params=params, signed=True)

            if response and "data" in response and response["data"]:
                order_info = response["data"][0]

                # 尝试获取平均成交价格
                avg_px = order_info.get("avgPx")
                if avg_px and float(avg_px) > 0:
                    executed_price = float(avg_px)
                    logger.info(f"🎯 OKX订单{order_id}实际成交价格: {executed_price:.8f}")
                    return executed_price

            # 如果订单详情中没有，查询成交历史
            logger.warning(f"⚠️ 订单详情中未找到成交价格，查询成交历史: {order_id}")
            return await self._get_execution_history_price(order_id, okx_symbol, inst_type)

        except Exception as e:
            logger.error(f"❌ 获取OKX订单{order_id}成交价格失败: {e}")
            # 返回0，让futures_trader.py使用兜底逻辑
            return 0.0

    async def _get_execution_history_price(self, order_id: str, symbol: str, inst_type: str) -> float:
        """
        🔥 从成交历史获取实际成交价格（加权平均）
        """
        try:
            params = {
                "instType": inst_type,
                "instId": symbol,
                "ordId": order_id,
                "limit": "50"
            }

            response = await self._request("GET", "/api/v5/trade/fills", params=params, signed=True)

            if response and "data" in response and response["data"]:
                fills = response["data"]

                if fills:
                    total_value = 0.0
                    total_qty = 0.0

                    for fill in fills:
                        price = float(fill.get("fillPx", 0))
                        qty = float(fill.get("fillSz", 0))

                        if price > 0 and qty > 0:
                            total_value += price * qty
                            total_qty += qty

                    if total_qty > 0:
                        weighted_avg_price = total_value / total_qty
                        logger.info(f"🎯 OKX订单{order_id}加权平均成交价格: {weighted_avg_price:.8f}")
                        return weighted_avg_price

            logger.warning(f"⚠️ 未找到订单{order_id}的成交历史")
            return 0.0

        except Exception as e:
            logger.error(f"❌ 查询OKX订单{order_id}成交历史失败: {e}")
            return 0.0

    async def place_order(
        self,
        symbol: str,
        side: OrderSide,
        order_type: OrderType,
        amount: float,
        price: Optional[float] = None,
        market_type: str = "spot",
        params: Optional[Dict] = None
    ) -> Dict[str, Any]:
        """下单 - 🔥 使用统一开仓管理器（已在__init__中初始化）"""
        try:
            # 🔥 修复：从params中提取orderbook参数
            orderbook = None
            if params and isinstance(params, dict):
                orderbook = params.get('orderbook')
                logger.info(f"🔍 OKX从params中获取orderbook: {orderbook is not None}")

            # 🔥 删除重复逻辑，使用已初始化的统一开仓管理器
            # 准备开仓参数
            opening_params = await self.opening_manager.prepare_opening_params(
                symbol=symbol,
                side=side.value,
                quantity=amount,
                price=price,
                exchange=self,
                market_type=market_type,
                order_type=order_type.value
            )

            # 🔥 修复：执行开仓订单时传递orderbook参数
            result = await self.opening_manager.execute_opening_order(opening_params, self, orderbook)

            if result.success:
                return {
                    "order_id": result.order_id,
                    "symbol": symbol,
                    "side": side.value,
                    "type": order_type.value,
                    "amount": result.executed_quantity or amount,
                    "price": result.executed_price or price,
                    "status": "open",
                    "timestamp": int(time.time() * 1000)
                }
            else:
                raise Exception(f"OKX下单失败: {result.error_message}")

        except Exception as e:
            self.error_count += 1
            logger.error(f"OKX下单异常: {e}")
            raise

    async def cancel_order(self, order_id: str, symbol: str, market_type: str = "spot") -> bool:
        """取消订单"""
        try:
            inst_id = self._convert_symbol(symbol, market_type)

            # 🔥 修复：OKX取消订单需要确保有ordId或clOrdId
            # 如果order_id为空，说明下单失败，直接返回True（取消成功）
            if not order_id or order_id.strip() == "":
                logger.warning("OKX订单ID为空，视为取消成功")
                return True

            # 根据订单ID格式判断是服务器订单ID还是客户端订单ID
            cancel_data = {"instId": inst_id}

            if order_id.startswith("arb"):
                # 客户端订单ID格式
                cancel_data["clOrdId"] = order_id
                logger.debug(f"OKX取消订单使用客户端ID: {order_id}")
            else:
                # 服务器订单ID格式
                cancel_data["ordId"] = order_id
                logger.debug(f"OKX取消订单使用服务器ID: {order_id}")

            await self._request(
                "POST", "/api/v5/trade/cancel-order",
                data=cancel_data
            )

            logger.info(f"OKX订单取消成功: {order_id}")
            return True

        except Exception as e:
            # 🔥 使用统一错误处理
            if self._handle_common_errors(e, "cancel_order"):
                return True
            else:
                logger.error(f"OKX取消订单失败: {e}")
                return False

    async def get_order(self, order_id: str, symbol: str, market_type: str = "spot") -> Dict[str, Any]:
        """查询订单"""
        try:
            # 检查订单ID有效性
            if not order_id or order_id.strip() == "":
                logger.warning("OKX订单ID为空，无法查询订单")
                return {
                    "order_id": "",
                    "symbol": symbol,
                    "side": "",
                    "type": "",
                    "amount": 0.0,
                    "price": 0.0,
                    "filled_amount": 0.0,
                    "status": "failed",
                    "timestamp": int(time.time() * 1000)
                }

            inst_id = self._convert_symbol(symbol, market_type)

            response = await self._request(
                "GET", "/api/v5/trade/order",
                params={
                    "instId": inst_id,
                    "ordId": order_id
                }
            )

            order = response[0] if response else {}

            # 安全地将数值字段转换为浮点数
            def safe_float(value, default=0.0):
                if value is None or value == '':
                    return default
                try:
                    return float(value)
                except (ValueError, TypeError):
                    logger.warning(f"无法将值转换为浮点数: {value}")
                    return default

            return {
                "order_id": order.get("ordId", ""),
                "symbol": inst_id,
                "side": order.get("side", ""),
                "type": "limit" if order.get("ordType") == "limit" else "market",
                "amount": safe_float(order.get("sz", 0)),
                "price": safe_float(order.get("px", 0)),
                "filled_amount": safe_float(order.get("fillSz", 0)),
                "status": self._parse_order_status(order.get("state", "")).value,
                "timestamp": int(order.get("cTime", 0) or time.time() * 1000)
            }

        except Exception as e:
            logger.error(f"OKX查询订单失败: {e}")
            raise

    async def get_position(self, symbol: str = None) -> List[Dict[str, Any]]:
        """获取持仓（期货）"""
        try:
            params = {"instType": "SWAP"}
            if symbol:
                params["instId"] = self._convert_symbol(symbol, "futures")

            response = await self._request(
                "GET", "/api/v5/account/positions",
                params=params
            )

            positions = []
            for pos in response:
                try:
                    # 安全地提取并转换数值字段
                    def safe_float(value, default=0):
                        if value is None or value == '' or value == 'null':
                            return default
                        try:
                            return float(value)
                        except (ValueError, TypeError):
                            logger.warning(f"无法将值转换为浮点数: {value}")
                            return default

                    # 获取持仓数量并确保安全转换
                    pos_size = safe_float(pos.get("pos"), 0)

                    if pos_size != 0:
                        positions.append({
                            "symbol": pos["instId"],
                            "size": abs(pos_size),
                            "side": "long" if pos_size > 0 else "short",
                            "entry_price": safe_float(pos.get("avgPx"), 0),
                            "mark_price": safe_float(pos.get("markPx"), 0),
                            "pnl": safe_float(pos.get("upl"), 0),
                            "margin": safe_float(pos.get("margin"), 0)
                        })
                except Exception as e:
                    logger.error(f"处理持仓数据时出错: {e}, 持仓数据: {pos}")
                    continue

            return positions

        except Exception as e:
            logger.error(f"OKX获取持仓失败: {e}")
            return []

    async def close_position(self, symbol: str, amount: Optional[float] = None) -> Dict[str, Any]:
        """平仓（期货）- 🔥 使用统一平仓管理器（已在__init__中初始化）"""
        try:
            # 🔥 修复：close_position_unified不接受amount参数
            result = await self.closing_manager.close_position_unified(
                symbol=symbol,
                exchange=self,
                market_type="futures"
            )

            # 🔥 修复：统一返回格式，确保与其他交易所一致
            if hasattr(result, 'success'):
                return {
                    "success": result.success,
                    "order_id": getattr(result, 'order_id', ''),
                    "quantity": getattr(result, 'executed_quantity', 0),
                    "price": getattr(result, 'executed_price', 0),
                    "message": getattr(result, 'error_message', '') if not result.success else "平仓成功"
                }
            else:
                return result

        except Exception as e:
            logger.error(f"OKX平仓失败: {e}")
            return {"success": False, "message": str(e)}

    async def transfer_funds(self, currency: str, amount: float,
                           from_account: AccountType = None, to_account: AccountType = None) -> bool:
        """内部转账（统一账户不需要）"""
        # OKX统一账户不需要内部转账
        logger.info(f"OKX统一账户不需要转账: {currency} {amount}")
        return True

    def is_unified_account(self) -> bool:
        """OKX是统一账户"""
        return True

    def _parse_order_status(self, status: str) -> OrderStatus:
        """解析订单状态"""
        status_map = {
            "live": OrderStatus.OPEN,
            "partially_filled": OrderStatus.PARTIAL,
            "filled": OrderStatus.FILLED,
            "canceled": OrderStatus.CANCELLED,
            "cancelled": OrderStatus.CANCELLED,
            "failed": OrderStatus.FAILED
        }
        return status_map.get(status.lower(), OrderStatus.PENDING)

    async def set_leverage(self, symbol: str, leverage: int = None,
                         margin_mode: str = "cross") -> bool:
        """设置期货杠杆"""
        try:
            # 使用环境变量配置的杠杆值，严格限制在2倍以内
            if leverage is None:
                leverage = self.default_leverage
            leverage = min(leverage, int(self.max_leverage))

            inst_id = self._convert_symbol(symbol, "futures")
            logger.info(f"OKX设置杠杆: {inst_id} {leverage}倍，保证金模式: {margin_mode}")

            # 🔥 修复：根据持仓模式正确设置posSide
            try:
                position_mode = await self.get_position_mode()
                if position_mode == "long_short_mode":
                    # 开平仓模式：需要分别设置多空杠杆
                    for pos_side in ["long", "short"]:
                        await self._request(
                            "POST", "/api/v5/account/set-leverage",
                            data={
                                "instId": inst_id,
                                "lever": str(leverage),
                                "mgnMode": margin_mode,
                                "posSide": pos_side
                            }
                        )
                        logger.info(f"OKX设置{pos_side}杠杆成功: {leverage}倍")
                else:
                    # 买卖模式：posSide为net
                    await self._request(
                        "POST", "/api/v5/account/set-leverage",
                        data={
                            "instId": inst_id,
                            "lever": str(leverage),
                            "mgnMode": margin_mode,
                            "posSide": "net"
                        }
                    )
                    logger.info(f"OKX设置net杠杆成功: {leverage}倍")
            except Exception as e:
                # 兜底处理：使用net模式
                logger.warning(f"OKX获取持仓模式失败，使用net模式设置杠杆: {e}")
                await self._request(
                    "POST", "/api/v5/account/set-leverage",
                    data={
                        "instId": inst_id,
                        "lever": str(leverage),
                        "mgnMode": margin_mode,
                        "posSide": "net"
                    }
                )

            logger.info(f"OKX设置杠杆成功: {leverage}倍")
            return True
        except Exception as e:
            logger.error(f"OKX设置杠杆失败: {e}")
            return False

    # 🔥 删除重复的便利方法：直接使用基类的统一接口
    # get_orderbook(symbol, market_type, limit) 已经足够

    # 🔥 删除重复的便利方法：直接使用基类的统一接口
    # get_balance(account_type) 已经足够
    # get_position(symbol) 已经足够

    # 🔥 新增：精度系统需要的API方法
    async def get_instruments(self, instType: str, instId: str = None) -> Dict[str, Any]:
        """
        获取交易对信息 - 用于精度配置
        🔥 修复：precision_config.py需要的方法（复数形式）
        🚀 通用期货溢价套利系统：优雅处理不存在的交易对
        """
        try:
            params = {"instType": instType}
            if instId:
                params["instId"] = instId

            logger.debug(f"OKX获取交易对信息: instType={instType}, instId={instId}")

            response = await self._request(
                "GET", "/api/v5/public/instruments",
                params=params
            )

            logger.debug(f"OKX交易对信息响应: {response}")
            return {"data": response} if response else {"data": []}

        except Exception as e:
            # 🔥 修复：优雅处理交易对不存在的情况
            error_str = str(e).lower()
            if any(keyword in error_str for keyword in ["51001", "不存在", "invalid", "not found", "does not exist"]):
                logger.debug(f"🔍 OKX交易对不存在: {instId} (instType={instType}) - 通用系统正常情况")
                return {"data": []}  # 返回空数据，表示交易对不存在
            else:
                # 其他真正的错误才记录为ERROR
                logger.error(f"获取OKX交易对信息失败: {e}")
                return {"data": []}

    async def get_contract_info(self, symbol: str) -> Dict[str, Any]:
        """
        🔥 新增：获取单个合约信息 - 用于保证金计算
        支持任意币种的通用合约信息查询

        根据OKX API文档：GET /api/v5/public/instruments?instType=SWAP&instId={instId}
        返回instruments数据，包含杠杆等保证金计算相关字段
        """
        try:
            # 转换为OKX期货合约格式
            instId = self._convert_symbol(symbol, "futures")

            logger.debug(f"OKX获取合约信息: {instId}")

            # 调用OKX官方API: GET /api/v5/public/instruments
            response = await self._request(
                "GET", "/api/v5/public/instruments",
                params={"instType": "SWAP", "instId": instId}
            )

            if response and len(response) > 0:
                contract_data = response[0]

                # 根据OKX API响应提取关键保证金计算字段
                contract_info = {
                    "name": contract_data.get("instId", instId),
                    "maintenance_rate": 0.004,  # OKX永续合约一般维持保证金率0.4%
                    "leverage_min": 1,
                    "leverage_max": int(float(contract_data.get("lever", "100"))),
                    "order_size_min": float(contract_data.get("minSz", "1")),
                    "order_size_max": float(contract_data.get("maxMktSz", "1000000")),
                    "tick_size": float(contract_data.get("tickSz", "0.1")),
                    "lot_size": float(contract_data.get("lotSz", "1")),
                    "contract_val": float(contract_data.get("ctVal") or "1"),  # 🔥 修复：安全转换ctVal
                    "contract_mult": float(contract_data.get("ctMult") or "1"),   # 🔥 修复：安全转换ctMult
                    "state": contract_data.get("state", "live"),
                    "inst_type": contract_data.get("instType", "SWAP"),
                    "uly": contract_data.get("uly", ""),  # 标的指数
                    "base_ccy": contract_data.get("baseCcy", ""),
                    "quote_ccy": contract_data.get("quoteCcy", "USDT"),
                    "settle_ccy": contract_data.get("settleCcy", "USDT")
                }

                logger.info(f"✅ OKX合约信息获取成功: {symbol} -> 最大杠杆={contract_info['leverage_max']}x")
                return contract_info
            else:
                logger.warning(f"⚠️ OKX合约信息为空: {symbol}")
                return {}

        except Exception as e:
            logger.error(f"❌ OKX获取合约信息失败 {symbol}: {e}")
            # 🔥 关键修复：不返回默认值，让调用方知道获取失败
            return {}

    async def get_account_config(self) -> Dict[str, Any]:
        """获取账户配置信息"""
        try:
            response = await self._request("GET", "/api/v5/account/config")
            if response and len(response) > 0:
                config = response[0]
                logger.debug(f"OKX账户配置: acctLv={config.get('acctLv')}, posMode={config.get('posMode')}")
                return config
            return {}
        except Exception as e:
            logger.error(f"OKX获取账户配置失败: {e}")
            return {}

    async def set_account_mode(self, account_level: str = "3") -> bool:
        """设置账户模式 - 3为统一账户模式"""
        try:
            await self._request("POST", "/api/v5/account/set-account-level",
                               data={"acctLv": account_level})
            logger.info(f"OKX账户模式设置成功: 等级{account_level}")
            return True
        except Exception as e:
            logger.error(f"OKX设置账户模式失败: {e}")
            return False

    # 🔥 删除重复方法：使用基类的get_server_time方法，或重写以获取真实服务器时间
    async def get_server_time(self) -> int:
        """获取OKX服务器时间 - 重写基类方法以获取真实服务器时间"""
        try:
            endpoint = "/api/v5/public/time"
            response = await self._request("GET", endpoint, signed=False)

            # OKX返回的时间格式
            if isinstance(response, dict) and "data" in response:
                data = response["data"]
                if data and len(data) > 0 and "ts" in data[0]:
                    # OKX的ts是毫秒级时间戳
                    return int(data[0]["ts"])

            # 如果API响应格式不符合预期，返回当前时间
            logger.warning("OKX服务器时间响应格式异常，使用本地时间")
            return int(time.time() * 1000)

        except Exception as e:
            logger.warning(f"获取OKX服务器时间失败: {e}，使用本地时间")
            return int(time.time() * 1000)

    async def initialize(self):
        """初始化OKX账户，设置必要的配置"""
        try:
            if self.account_initialized:
                return True
                
            self.logger.info("🔥 开始初始化OKX账户配置...")
            
            # 🔥 修复1：确保账户模式正确设置
            try:
                config_result = await self.get_account_config()
                if config_result:
                    current_mode = config_result.get('acctLv', 0)
                    self.logger.info(f"OKX当前账户模式: {current_mode}")
                    
                    # 账户模式说明：
                    # 1: 简单交易模式
                    # 2: 单币种保证金模式  
                    # 3: 跨币种保证金模式（推荐）
                    # 4: 组合保证金模式
                    if current_mode != "3":
                        self.logger.warning(f"OKX账户不是跨币种保证金模式，当前模式: {current_mode}")
                        # 注意：账户模式通常需要在网页端设置，API无法直接修改
                        
            except Exception as e:
                self.logger.warning(f"OKX获取账户配置失败: {e}")
            
            # 🔥 修复2：设置持仓模式为单向持仓（简化保证金计算）
            try:
                await self._request(
                    "POST", "/api/v5/account/set-position-mode",
                    data={"posMode": "net_mode"}  # net_mode=单向持仓，long_short_mode=双向持仓
                )
                self.logger.info("OKX设置为单向持仓模式")
            except Exception as e:
                self.logger.warning(f"OKX设置持仓模式失败（可能已经是正确模式）: {e}")
            
            # 🔥 修复3：预设置杠杆 - 使用.env配置的交易对，避免硬编码
            try:
                from core.universal_token_system import get_universal_token_system
                token_system = get_universal_token_system()
                configured_symbols = token_system.get_supported_symbols()[:4]  # 取前4个进行预设置

                for symbol in configured_symbols:
                    try:
                        await self.set_leverage(symbol, 3, "cross")
                        self.logger.debug(f"OKX预设置杠杆成功: {symbol}")
                    except Exception as e:
                        self.logger.debug(f"OKX预设置杠杆失败: {symbol}, {e}")
            except Exception as e:
                self.logger.warning(f"OKX预设置杠杆过程失败: {e}")
            
            self.account_initialized = True
            self.logger.info("✅ OKX账户初始化完成")
            return True
            
        except Exception as e:
            self.logger.error(f"OKX账户初始化失败: {e}")
            return False

    async def get_account_mode(self) -> Dict[str, Any]:
        """获取账户模式配置"""
        try:
            response = await self._request("GET", "/api/v5/account/config")
            if response and len(response) > 0:
                config = response[0]
                logger.info(f"OKX账户模式: {config}")
                return config
            return {}
        except Exception as e:
            logger.error(f"OKX获取账户模式失败: {e}")
            return {}

    async def is_portfolio_margin_mode(self) -> bool:
        """检查是否为统一账户模式"""
        try:
            config = await self.get_account_mode()
            # 检查账户类型
            acct_lv = config.get("acctLv", "1")  # 账户等级：1-简单交易，2-单币种保证金，3-跨币种保证金，4-组合保证金
            return acct_lv in ["3", "4"]  # 3为跨币种保证金，4为组合保证金（统一账户）
        except Exception as e:
            logger.error(f"检查OKX账户模式失败: {e}")
            return False

    async def get_position_mode(self) -> str:
        """获取持仓模式"""
        try:
            config = await self.get_account_mode()
            # posMode: 持仓方式，long_short_mode：双向持仓，net_mode：单向持仓
            pos_mode = config.get("posMode", "net_mode")
            logger.info(f"OKX持仓模式: {pos_mode}")
            return pos_mode
        except Exception as e:
            logger.error(f"获取OKX持仓模式失败: {e}")
            return "net_mode"  # 默认单向持仓

    async def get_order_status(self, order_id: str, symbol: str, market_type: str = "spot") -> str:
        """
        🔥 新增：获取订单状态
        :param order_id: 订单ID
        :param symbol: 交易对
        :param market_type: 市场类型
        :return: 订单状态
        """
        try:
            order_info = await self.get_order(order_id, symbol, market_type)
            return order_info.get("status", "unknown")
        except Exception as e:
            self.logger.error(f"获取订单状态失败: {e}")
            return "error"

    async def get_trading_rules(self, symbol: str = None) -> Dict[str, Any]:
        """
        🔥 新增：获取交易规则
        :param symbol: 交易对，None表示所有
        :return: 交易规则
        """
        try:
            # 使用统一交易规则预加载器
            if hasattr(self, 'trading_rules_preloader') and self.trading_rules_preloader:
                if symbol:
                    # 获取特定交易对的规则
                    rules = await self.trading_rules_preloader.get_trading_rules(symbol, "okx")
                    return {symbol: rules} if rules else {}
                else:
                    # 获取所有交易对的规则
                    return await self.trading_rules_preloader.get_all_trading_rules("okx")
            else:
                self.logger.warning("交易规则预加载器不可用")
                return {}
        except Exception as e:
            self.logger.error(f"获取交易规则失败: {e}")
            return {}


# 🔥 添加独立运行入口 - 符合方案要求
if __name__ == "__main__":
    import os
    import asyncio
    from dotenv import load_dotenv

    load_dotenv()

    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    async def test_okx_exchange():
        """OKX交易所独立测试"""
        # 从环境变量获取API密钥
        api_key = os.getenv("OKX_API_KEY", "")
        api_secret = os.getenv("OKX_API_SECRET", "")
        passphrase = os.getenv("OKX_API_PASSPHRASE", "")

        if not api_key or not api_secret or not passphrase:
            print("❌ 请设置OKX_API_KEY、OKX_API_SECRET和OKX_API_PASSPHRASE环境变量")
            return

        exchange = OKXExchange(api_key, api_secret, passphrase)

        try:
            print("🚀 OKX交易所独立测试开始")
            
            # 测试连接
            print("\n1. 测试连接...")
            connected = await exchange.test_connection()
            print(f"   连接状态: {'✅ 成功' if connected else '❌ 失败'}")

            if connected:
                # 检查账户配置
                print("\n2. 检查账户配置...")
                try:
                    config = await exchange.get_account_config()
                    account_level = config.get("acctLv", "未知")
                    print(f"   账户等级: {account_level} ({'统一账户' if account_level in ['3', '4'] else '普通账户'})")
                except Exception as e:
                    print(f"   ⚠️ 账户配置检查失败: {e}")

                # 获取余额
                print("\n3. 获取账户余额...")
                balance = await exchange.get_balance(AccountType.UNIFIED)
                print(f"   账户余额: {balance}")

                # 🔥 修复：删除WebSocket价格缓存，统一使用OpportunityScanner.market_data
                print("\n4. 价格数据统一使用OpportunityScanner.market_data管理")

                # 获取订单簿
                print("\n5. 获取BTC-USDT现货深度...")
                orderbook = await exchange.get_orderbook("BTC-USDT", "spot", 5)
                print(f"   买1: ${orderbook['bids'][0][0]:.2f} 量: {orderbook['bids'][0][1]:.6f}")
                print(f"   卖1: ${orderbook['asks'][0][0]:.2f} 量: {orderbook['asks'][0][1]:.6f}")

                # 测试获取合约信息
                print("\n6. 获取合约信息...")
                try:
                    instrument_info = await exchange.get_instrument_info("BTC-USDT", "spot")
                    print(f"   最小交易量: {instrument_info.get('min_size', 'N/A')}")
                    print(f"   最小增量: {instrument_info.get('lot_size', 'N/A')}")
                except Exception as e:
                    print(f"   ⚠️ 合约信息获取失败: {e}")

                # 测试下单（仅在启用时）
                if os.environ.get("TEST_PLACE_ORDER", "false").lower() == "true":
                    print("\n7. 测试下市价单...")
                    test_amount = float(os.environ.get("TEST_AMOUNT", "0.001"))
                    # 🔥 市价单系统：删除价格参数，使用市价下单

                    try:
                        order = await exchange.place_order(
                            "BTC-USDT",
                            OrderSide.BUY,
                            OrderType.MARKET,
                            test_amount,
                            None,  # 市价单无需价格
                            "spot"
                        )
                        print(f"   下单结果: 订单ID {order.get('order_id', 'N/A')}")

                        if order.get("order_id"):
                            # 查询订单
                            order_info = await exchange.get_order(order["order_id"], "BTC-USDT", "spot")
                            print(f"   订单状态: {order_info.get('status', 'unknown')}")

                            # 取消订单
                            cancel_result = await exchange.cancel_order(order["order_id"], "BTC-USDT", "spot")
                            print(f"   取消结果: {'✅ 成功' if cancel_result else '❌ 失败'}")
                    except Exception as e:
                        print(f"   ❌ 下单测试失败: {e}")

            print("\n✅ OKX交易所独立测试完成")

        except Exception as e:
            print(f"❌ OKX交易所测试异常: {e}")
        finally:
            await exchange.close()

    # 运行测试
    asyncio.run(test_okx_exchange())