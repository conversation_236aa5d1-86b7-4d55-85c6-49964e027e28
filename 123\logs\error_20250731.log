2025-07-31 10:05:03.010 [ERROR] [utils.margin_calculator] ❌ [API调用] 保证金接口: okx_ICNT-USDT | 获取合约信息失败，所有重试都失败
2025-07-31 10:05:09.530 [ERROR] [utils.margin_calculator] ❌ [API调用] 保证金接口: okx_CAKE-USDT | 获取合约信息失败，所有重试都失败
2025-07-31 10:06:19.698 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-07-31 10:06:19.698 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-07-31 10:06:19.698 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-07-31 10:06:19.699 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-07-31 10:06:19.699 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-07-31 10:06:19.699 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-07-31 10:06:19.700 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-31 10:06:19.700 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-07-31 10:06:19.700 [ERROR] [exchanges.okx_exchange] OKX获取账户模式失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-31 10:06:19.707 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-07-31 10:06:19.708 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-07-31 10:06:19.708 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-07-31 10:06:19.708 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-07-31 10:06:19.708 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-07-31 10:06:19.708 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-07-31 10:06:19.709 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-31 10:06:19.709 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-07-31 10:06:19.709 [ERROR] [exchanges.okx_exchange] OKX获取账户模式失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-31 10:06:19.712 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-07-31 10:06:19.712 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-07-31 10:06:19.712 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-07-31 10:06:19.712 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-07-31 10:06:19.712 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-07-31 10:06:19.712 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-07-31 10:06:19.712 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-31 10:06:19.713 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-07-31 10:06:19.713 [ERROR] [exchanges.okx_exchange] OKX获取账户模式失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-31 10:06:19.717 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-07-31 10:06:19.717 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-07-31 10:06:19.718 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-07-31 10:06:19.718 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-07-31 10:06:19.718 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-07-31 10:06:19.718 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-07-31 10:06:19.718 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-31 10:06:19.718 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-07-31 10:06:19.718 [ERROR] [exchanges.okx_exchange] OKX获取账户模式失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-31 10:06:19.798 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-07-31 10:06:19.799 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-07-31 10:06:19.799 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-07-31 10:06:19.799 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-07-31 10:06:19.799 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-07-31 10:06:19.799 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-07-31 10:06:19.800 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-31 10:06:19.800 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-07-31 10:06:19.800 [ERROR] [exchanges.okx_exchange] OKX获取账户模式失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-31 10:06:20.527 [ERROR] [exchanges.bybit_exchange] Bybit API错误: 110074: closed symbol error: This MATICUSDT contract is not live
2025-07-31 10:06:20.527 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: Bybit API错误: 110074: closed symbol error: This MATICUSDT contract is not live
2025-07-31 10:06:20.527 [ERROR] [exchanges.bybit_exchange] ❌ Bybit设置杠杆异常: Bybit API错误: 110074: closed symbol error: This MATICUSDT contract is not live
2025-07-31 10:06:35.357 [ERROR] [utils.margin_calculator] ❌ [API调用] 保证金接口: okx_ICNT-USDT | 获取合约信息失败，所有重试都失败
2025-07-31 10:06:36.999 [ERROR] [utils.margin_calculator] ❌ [API调用] 保证金接口: okx_CAKE-USDT | 获取合约信息失败，所有重试都失败
2025-07-31 10:06:46.285 [ERROR] [utils.margin_calculator] ❌ [API调用] 保证金接口: okx_ICNT-USDT | 获取合约信息失败，所有重试都失败
2025-07-31 10:06:47.984 [ERROR] [utils.margin_calculator] ❌ [API调用] 保证金接口: okx_CAKE-USDT | 获取合约信息失败，所有重试都失败
2025-07-31 10:15:42.124 [ERROR] [exchanges.bybit_exchange] Bybit API错误: 170137: Order quantity has too many decimals.
2025-07-31 10:15:42.124 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: Bybit API错误: 170137: Order quantity has too many decimals.
2025-07-31 10:15:42.125 [ERROR] [exchanges.bybit_exchange] Bybit现货直接下单异常: Bybit API错误: 170137: Order quantity has too many decimals.
2025-07-31 10:15:42.125 [ERROR] [SpotTrader.bybit] Error executing spot order: Bybit API错误: 170137: Order quantity has too many decimals.
2025-07-31 10:15:42.125 [ERROR] [SpotTrader.bybit] Order parameters: symbol=RESOLV-USDT, side=buy, quantity=173.01, price=0.2022
2025-07-31 10:15:42.125 [ERROR] [core.unified_opening_manager] ❌ 缺少订单ID或结果为空: order_result={'id': '', 'status': 'failed', 'error': 'Bybit API错误: 170137: Order quantity has too many decimals.'}
2025-07-31 10:15:42.125 [ERROR] [core.unified_opening_manager] 开仓失败: 无效的订单结果 - {'id': '', 'status': 'failed', 'error': 'Bybit API错误: 170137: Order quantity has too many decimals.'}
2025-07-31 10:15:42.125 [ERROR] [ExecutionEngine] ❌ 现货执行失败: OpeningResult(success=False, order_id=None, executed_quantity=0.0, executed_price=0.0, error_message="开仓失败: 无效的订单结果 - {'id': '', 'status': 'failed', 'error': 'Bybit API错误: 170137: Order quantity has too many decimals.'}", execution_time_ms=856.5785884857178, params_used=OpeningOrderParams(symbol='RESOLV-USDT', side='buy', order_type='market', quantity='173.01', price=None, market_type='spot', original_quantity=173.01, original_price=None, step_size='0.001', price_step='0.01', exchange_name='bybit'))
2025-07-31 10:15:48.475 [ERROR] [ExecutionEngine] 🚨 期货成功但现货失败，执行紧急平仓期货仓位
2025-07-31 10:15:50.411 [ERROR] [ExecutionEngine] 🚨 开始紧急平仓期货仓位: RESOLV-USDT
2025-07-31 10:15:51.434 [ERROR] [FuturesTrader.gate] ❌ 缺少WebSocket订单簿数据，无法获取最优卖价
2025-07-31 10:15:51.434 [ERROR] [ExecutionEngine] ❌ 紧急平仓期货失败: 期货平仓失败: {'id': '', 'status': 'failed', 'error': 'Exchange does not support get_orderbook'}
2025-07-31 10:15:51.435 [ERROR] [ExecutionEngine] ❌ 执行步骤C.1失败: 并行执行失败，结束套利: RESOLV-USDT
2025-07-31 10:15:52.479 [ERROR] [ArbitrageEngine] Error in executing: Lock is not acquired.
2025-07-31 10:15:52.611 [ERROR] [ArbitrageEngine] 🔧 处理错误状态: session ARB_1753949749
2025-07-31 10:16:16.937 [ERROR] [exchanges.bybit_exchange] Bybit API错误: 170137: Order quantity has too many decimals.
2025-07-31 10:16:16.937 [ERROR] [exchanges.bybit_exchange] Bybit请求失败: Bybit API错误: 170137: Order quantity has too many decimals.
2025-07-31 10:16:16.937 [ERROR] [exchanges.bybit_exchange] Bybit现货直接下单异常: Bybit API错误: 170137: Order quantity has too many decimals.
2025-07-31 10:16:16.937 [ERROR] [SpotTrader.bybit] Error executing spot order: Bybit API错误: 170137: Order quantity has too many decimals.
2025-07-31 10:16:16.937 [ERROR] [SpotTrader.bybit] Order parameters: symbol=RESOLV-USDT, side=buy, quantity=172.583, price=0.2028
2025-07-31 10:16:16.938 [ERROR] [core.unified_opening_manager] ❌ 缺少订单ID或结果为空: order_result={'id': '', 'status': 'failed', 'error': 'Bybit API错误: 170137: Order quantity has too many decimals.'}
2025-07-31 10:16:16.938 [ERROR] [core.unified_opening_manager] 开仓失败: 无效的订单结果 - {'id': '', 'status': 'failed', 'error': 'Bybit API错误: 170137: Order quantity has too many decimals.'}
2025-07-31 10:16:16.938 [ERROR] [ExecutionEngine] ❌ 现货执行失败: OpeningResult(success=False, order_id=None, executed_quantity=0.0, executed_price=0.0, error_message="开仓失败: 无效的订单结果 - {'id': '', 'status': 'failed', 'error': 'Bybit API错误: 170137: Order quantity has too many decimals.'}", execution_time_ms=250.22077560424805, params_used=OpeningOrderParams(symbol='RESOLV-USDT', side='buy', order_type='market', quantity='172.583', price=None, market_type='spot', original_quantity=172.583, original_price=None, step_size='0.001', price_step='0.01', exchange_name='bybit'))
2025-07-31 10:16:18.973 [ERROR] [exchanges.okx_exchange] ❌ 检测到数量不匹配!
2025-07-31 10:16:18.973 [ERROR] [exchanges.okx_exchange]    原始数量: 172.58382
2025-07-31 10:16:18.973 [ERROR] [exchanges.okx_exchange]    API数量: 17.0
2025-07-31 10:16:18.974 [ERROR] [exchanges.okx_exchange]    放大倍数: 0.10x
2025-07-31 10:16:19.559 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-07-31 10:16:19.560 [ERROR] [exchanges.okx_exchange]   - 错误代码: 51603
2025-07-31 10:16:19.560 [ERROR] [exchanges.okx_exchange]   - 错误信息: Order does not exist
2025-07-31 10:16:19.560 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'code': '51603', 'data': [], 'msg': 'Order does not exist'}
2025-07-31 10:16:19.560 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/trade/order
2025-07-31 10:16:19.560 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-07-31 10:16:19.560 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Order does not exist (代码: 51603)
2025-07-31 10:16:19.560 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Order does not exist (代码: 51603)',)
2025-07-31 10:16:19.560 [ERROR] [exchanges.okx_exchange] ❌ 获取OKX订单2732920537643540480成交价格失败: OKX API错误: Order does not exist (代码: 51603)
2025-07-31 10:16:19.615 [ERROR] [ExecutionEngine] 🚨 期货成功但现货失败，执行紧急平仓期货仓位
2025-07-31 10:16:21.196 [ERROR] [ExecutionEngine] 🚨 开始紧急平仓期货仓位: RESOLV-USDT
2025-07-31 10:16:22.939 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-07-31 10:16:22.939 [ERROR] [exchanges.okx_exchange]   - 错误代码: 51603
2025-07-31 10:16:22.939 [ERROR] [exchanges.okx_exchange]   - 错误信息: Order does not exist
2025-07-31 10:16:22.939 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'code': '51603', 'data': [], 'msg': 'Order does not exist'}
2025-07-31 10:16:22.939 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/trade/order
2025-07-31 10:16:22.940 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-07-31 10:16:22.940 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Order does not exist (代码: 51603)
2025-07-31 10:16:22.940 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Order does not exist (代码: 51603)',)
2025-07-31 10:16:22.940 [ERROR] [exchanges.okx_exchange] ❌ 获取OKX订单2732920652097708032成交价格失败: OKX API错误: Order does not exist (代码: 51603)
2025-07-31 10:16:22.941 [ERROR] [ExecutionEngine] ❌ 执行步骤C.1失败: 并行执行失败，结束套利: RESOLV-USDT
2025-07-31 10:16:23.976 [ERROR] [ArbitrageEngine] Error in executing: Lock is not acquired.
2025-07-31 10:16:24.141 [ERROR] [ArbitrageEngine] 🔧 处理错误状态: session ARB_1753949776
