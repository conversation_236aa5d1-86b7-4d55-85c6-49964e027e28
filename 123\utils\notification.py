#!/usr/bin/env python
"""
通知服务模块 - 仅支持Telegram通知
简化版本，专注于Telegram通知功能
"""

import os
import json
import asyncio
import aiohttp
from enum import Enum
from typing import Dict, List, Optional, Any
from datetime import datetime

# 🔥 新增：确保环境变量加载
from dotenv import load_dotenv
load_dotenv()

try:
    from utils.logger import get_logger
except ImportError:
    import logging
    def get_logger(name):
        return logging.getLogger(name)

logger = get_logger(__name__)


class NotificationLevel(Enum):
    """通知级别枚举"""
    DEBUG = "debug"
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class NotificationChannel(Enum):
    """通知渠道枚举"""
    TELEGRAM = "telegram"
    CONSOLE = "console"


class NotificationTemplate:
    """通知模板类"""
    
    # 系统异常通知模板
    SYSTEM_ERROR = {
        "title": "🚨 系统异常警告",
        "format": "⚠️ 系统发生异常:\n📍 位置: {location}\n⏰ 时间: {timestamp}\n❌ 错误: {error}\n📝 详情: {details}"
    }
    
    # 交易异常通知模板
    TRADING_ERROR = {
        "title": "📈 交易异常警告",
        "format": "⚠️ 交易异常:\n🏢 交易所: {exchange}\n💱 交易对: {symbol}\n⏰ 时间: {timestamp}\n❌ 错误: {error}\n💰 影响金额: {amount}"
    }
    
    # 资金异常通知模板
    FUND_ERROR = {
        "title": "💰 资金异常警告",
        "format": "⚠️ 资金异常:\n🏢 交易所: {exchange}\n💰 余额: {balance}\n⚖️ 阈值: {threshold}\n⏰ 时间: {timestamp}\n📝 详情: {details}"
    }
    
    # 连接异常通知模板
    CONNECTION_ERROR = {
        "title": "🌐 连接异常警告",
        "format": "⚠️ 连接异常:\n🏢 服务: {service}\n🔗 连接: {connection}\n⏰ 时间: {timestamp}\n❌ 错误: {error}\n🔄 重试次数: {retry_count}"
    }
    
    # 套利成功通知模板
    ARBITRAGE_SUCCESS = {
        "title": "✅ 套利成功",
        "format": "🎉 套利执行成功:\n💱 交易对: {symbol}\n💰 收益: {profit} USDT\n📊 价差: {spread}%\n⏱️ 执行时间: {execution_time}ms\n⏰ 时间: {timestamp}"
    }


class BaseNotifier:
    """基础通知器类"""
    
    def __init__(self, enabled: bool = True):
        self.enabled = enabled
        self.logger = get_logger(self.__class__.__name__)
    
    async def send(self, message: str, level: NotificationLevel = NotificationLevel.INFO, **kwargs) -> bool:
        """发送通知"""
        if not self.enabled:
            self.logger.debug("通知器未启用，跳过发送")
            return True
        
        try:
            return await self._send_message(message, level, **kwargs)
        except Exception as e:
            self.logger.error(f"发送通知失败: {e}")
            return False
    
    async def _send_message(self, message: str, level: NotificationLevel, **kwargs) -> bool:
        """子类需要实现的发送方法"""
        raise NotImplementedError


class TelegramNotifier(BaseNotifier):
    """Telegram通知器"""
    
    def __init__(self, bot_token: str = "", chat_id: str = "", enabled: bool = True):
        super().__init__(enabled)
        self.bot_token = bot_token or os.getenv('TELEGRAM_BOT_TOKEN', '')
        self.chat_id = chat_id or os.getenv('TELEGRAM_CHAT_ID', '')
        self.base_url = f"https://api.telegram.org/bot{self.bot_token}/sendMessage" if self.bot_token else None
        
        # 🔥 增强调试日志
        self.logger.info(f"TelegramNotifier初始化:")
        self.logger.info(f"  Bot Token: {'已设置' if self.bot_token else '未设置'}")
        self.logger.info(f"  Chat ID: {'已设置' if self.chat_id else '未设置'}")
        self.logger.info(f"  Base URL: {self.base_url if self.base_url else '未设置'}")
        
        if not self.bot_token or not self.chat_id:
            self.enabled = False
            self.logger.warning("Telegram配置不完整，通知功能已禁用")
            self.logger.warning(f"  缺少配置: bot_token={'✓' if self.bot_token else '✗'}, chat_id={'✓' if self.chat_id else '✗'}")
        else:
            self.logger.info("✅ Telegram配置完整，通知功能已启用")
    
    async def _send_message(self, message: str, level: NotificationLevel, **kwargs) -> bool:
        """发送Telegram消息"""
        if not self.base_url:
            self.logger.error("Telegram base_url未设置")
            return False
        
        # 添加级别emoji
        level_emoji = {
            NotificationLevel.DEBUG: "🔍",
            NotificationLevel.INFO: "ℹ️",
            NotificationLevel.WARNING: "⚠️",
            NotificationLevel.ERROR: "❌",
            NotificationLevel.CRITICAL: "🚨"
        }
        
        formatted_message = f"{level_emoji.get(level, 'ℹ️')} {message}"
        
        payload = {
            "chat_id": self.chat_id,
            "text": formatted_message
        }
        
        # 🔥 修复：只有当parse_mode明确指定时才添加
        if kwargs.get('parse_mode') == 'html':
            payload["parse_mode"] = "HTML"
        
        # 🔥 增强调试日志
        self.logger.info(f"🚀 发送Telegram消息:")
        self.logger.info(f"  URL: {self.base_url}")
        self.logger.info(f"  Chat ID: {self.chat_id}")
        self.logger.info(f"  消息长度: {len(formatted_message)}")
        self.logger.debug(f"  消息内容: {formatted_message[:100]}...")
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(self.base_url, json=payload, timeout=10) as response:
                    response_text = await response.text()
                    
                    if response.status == 200:
                        self.logger.info("✅ Telegram消息发送成功")
                        self.logger.debug(f"响应: {response_text}")
                        return True
                    else:
                        self.logger.error(f"❌ Telegram消息发送失败: HTTP {response.status}")
                        self.logger.error(f"Telegram响应: {response_text}")
                        
                        # 特殊处理常见错误
                        if response.status == 400:
                            self.logger.error("⚠️ HTTP 400 错误通常表示:")
                            self.logger.error("  1. Bot Token 无效")
                            self.logger.error("  2. Chat ID 无效") 
                            self.logger.error("  3. 消息格式错误")
                        elif response.status == 401:
                            self.logger.error("⚠️ HTTP 401 错误: Bot Token 无效")
                        elif response.status == 403:
                            self.logger.error("⚠️ HTTP 403 错误: Bot无权限发送消息到该Chat ID")
                        
                        return False
        except asyncio.TimeoutError:
            self.logger.error("Telegram消息发送超时")
            return False
        except Exception as e:
            self.logger.error(f"Telegram消息发送异常: {e}")
            import traceback
            self.logger.error(f"异常详情: {traceback.format_exc()}")
            return False


class ConsoleNotifier(BaseNotifier):
    """控制台通知器"""
    
    async def _send_message(self, message: str, level: NotificationLevel, **kwargs) -> bool:
        """发送控制台通知"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        level_colors = {
            NotificationLevel.DEBUG: "\033[36m",    # 青色
            NotificationLevel.INFO: "\033[32m",     # 绿色
            NotificationLevel.WARNING: "\033[33m",  # 黄色
            NotificationLevel.ERROR: "\033[31m",    # 红色
            NotificationLevel.CRITICAL: "\033[35m"  # 紫色
        }
        reset_color = "\033[0m"
        
        color = level_colors.get(level, "")
        print(f"{timestamp} {color}[{level.value.upper()}]{reset_color} {message}")
        return True


class NotificationManager:
    """通知管理器 - 仅支持Telegram和控制台"""
    
    def __init__(self):
        self.notifiers: Dict[NotificationChannel, BaseNotifier] = {}
        self.logger = get_logger(__name__)
        self._initialize_notifiers()
    
    def _initialize_notifiers(self):
        """初始化通知器"""
        # Telegram通知器
        self.notifiers[NotificationChannel.TELEGRAM] = TelegramNotifier()
        
        # 控制台通知器
        self.notifiers[NotificationChannel.CONSOLE] = ConsoleNotifier()
        
        # 记录启用的通知器
        enabled_notifiers = [
            channel.value for channel, notifier in self.notifiers.items() 
            if notifier.enabled
        ]
        self.logger.info(f"已启用的通知器: {enabled_notifiers}")
    
    async def send(self, message: str, level: NotificationLevel = NotificationLevel.INFO,
                   channels: List[NotificationChannel] = None, **kwargs) -> Dict[NotificationChannel, bool]:
        """发送通知到指定渠道"""
        if channels is None:
            # 默认发送到所有启用的渠道
            channels = [channel for channel, notifier in self.notifiers.items() if notifier.enabled]
        
        results = {}
        
        for channel in channels:
            notifier = self.notifiers.get(channel)
            if notifier and notifier.enabled:
                result = await notifier.send(message, level, **kwargs)
                results[channel] = result
            else:
                results[channel] = False
        
        return results
    
    async def send_template(self, template: Dict[str, str], data: Dict[str, Any],
                           level: NotificationLevel = NotificationLevel.INFO,
                           channels: List[NotificationChannel] = None) -> Dict[NotificationChannel, bool]:
        """使用模板发送通知"""
        try:
            message = template["format"].format(**data)
            return await self.send(message, level, channels)
        except KeyError as e:
            self.logger.error(f"模板数据缺少必需字段: {e}")
            return {}
        except Exception as e:
            self.logger.error(f"模板格式化失败: {e}")
            return {}
    
    async def send_system_error(self, location: str, error: str, details: str = "", 
                               channels: List[NotificationChannel] = None) -> Dict[NotificationChannel, bool]:
        """发送系统异常通知"""
        data = {
            "location": location,
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "error": error,
            "details": details or "无其他详情"
        }
        return await self.send_template(NotificationTemplate.SYSTEM_ERROR, data, NotificationLevel.ERROR, channels)
    
    async def send_trading_error(self, exchange: str, symbol: str, error: str, amount: str = "0",
                                channels: List[NotificationChannel] = None) -> Dict[NotificationChannel, bool]:
        """发送交易异常通知"""
        data = {
            "exchange": exchange,
            "symbol": symbol,
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "error": error,
            "amount": amount
        }
        return await self.send_template(NotificationTemplate.TRADING_ERROR, data, NotificationLevel.ERROR, channels)
    
    async def send_fund_error(self, exchange: str, balance: str, threshold: str, details: str = "",
                             channels: List[NotificationChannel] = None) -> Dict[NotificationChannel, bool]:
        """发送资金异常通知"""
        data = {
            "exchange": exchange,
            "balance": balance,
            "threshold": threshold,
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "details": details or "余额不足"
        }
        return await self.send_template(NotificationTemplate.FUND_ERROR, data, NotificationLevel.WARNING, channels)
    
    async def send_connection_error(self, service: str, connection: str, error: str, retry_count: int = 0,
                                   channels: List[NotificationChannel] = None) -> Dict[NotificationChannel, bool]:
        """发送连接异常通知"""
        data = {
            "service": service,
            "connection": connection,
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "error": error,
            "retry_count": retry_count
        }
        return await self.send_template(NotificationTemplate.CONNECTION_ERROR, data, NotificationLevel.WARNING, channels)
    
    def get_enabled_channels(self) -> List[NotificationChannel]:
        """获取启用的通知渠道"""
        return [channel for channel, notifier in self.notifiers.items() if notifier.enabled]


# 全局通知管理器实例
notification_manager = NotificationManager()


# 便捷函数
async def send_notification(message: str, level: NotificationLevel = NotificationLevel.INFO,
                           channels: List[NotificationChannel] = None) -> Dict[NotificationChannel, bool]:
    """发送通知的便捷函数"""
    return await notification_manager.send(message, level, channels)


async def send_system_error(location: str, error: str, details: str = "") -> Dict[NotificationChannel, bool]:
    """发送系统异常通知的便捷函数"""
    return await notification_manager.send_system_error(location, error, details)


async def send_trading_error(exchange: str, symbol: str, error: str, amount: str = "0") -> Dict[NotificationChannel, bool]:
    """发送交易异常通知的便捷函数"""
    return await notification_manager.send_trading_error(exchange, symbol, error, amount)


# 测试函数
async def test_notifications():
    """测试Telegram通知功能"""
    logger.info("开始测试Telegram通知功能...")
    
    # 测试基础通知
    await send_notification("🧪 Telegram测试消息", NotificationLevel.INFO)
    await send_notification("⚠️ 测试警告", NotificationLevel.WARNING)
    await send_notification("❌ 测试错误", NotificationLevel.ERROR)
    
    # 测试模板通知
    await send_system_error("测试模块", "测试异常", "这是一个Telegram测试异常")
    await send_trading_error("Gate", "BTC-USDT", "测试交易错误", "100 USDT")
    
    logger.info("Telegram通知功能测试完成")


if __name__ == '__main__':
    # 运行测试
    asyncio.run(test_notifications()) 