"""
连接池配置模块
统一管理连接池相关配置参数
"""

import os
from typing import Dict, Any


class ConnectionPoolConfig:
    """连接池配置类"""
    
    # 连接池基础配置 - 🔥 按照22阈值正确调整.md优化
    MAX_CONNECTIONS_PER_EXCHANGE = int(os.getenv("MAX_CONNECTIONS_PER_EXCHANGE", "6"))  # 🔥 修复：每交易所6连接
    CONNECTION_POOL_SIZE = int(os.getenv("CONNECTION_POOL_SIZE", "18"))  # 🔥 修复：3交易所×6连接=18
    CONNECTION_MONITOR_INTERVAL = float(os.getenv("CONNECTION_MONITOR_INTERVAL", "3.0"))  # 🔥 修复：3秒监控（更及时发现断连）
    
    # 重连配置
    BASE_RECONNECT_DELAY = float(os.getenv("BASE_RECONNECT_DELAY", "1.0"))
    MAX_RECONNECT_DELAY = float(os.getenv("MAX_RECONNECT_DELAY", "120.0"))  # 最大2分钟
    RECONNECT_JITTER = float(os.getenv("RECONNECT_JITTER", "0.1"))  # 10%抖动
    MAX_RECONNECT_ATTEMPTS = int(os.getenv("MAX_RECONNECT_ATTEMPTS", "10"))
    
    # 定期重启配置
    CONNECTION_RESTART_INTERVAL = float(os.getenv("CONNECTION_RESTART_INTERVAL", "24.0"))  # 24小时
    RESTART_WINDOW_START = int(os.getenv("RESTART_WINDOW_START", "2"))  # 凌晨2点开始
    RESTART_WINDOW_END = int(os.getenv("RESTART_WINDOW_END", "6"))    # 凌晨6点结束
    
    # 连接质量配置
    QUALITY_CHECK_INTERVAL = float(os.getenv("QUALITY_CHECK_INTERVAL", "30.0"))  # 30秒
    POOR_QUALITY_THRESHOLD = float(os.getenv("POOR_QUALITY_THRESHOLD", "0.05"))  # 5%错误率
    EXCELLENT_LATENCY_THRESHOLD = float(os.getenv("EXCELLENT_LATENCY_THRESHOLD", "50.0"))  # 50ms
    GOOD_LATENCY_THRESHOLD = float(os.getenv("GOOD_LATENCY_THRESHOLD", "100.0"))  # 100ms
    FAIR_LATENCY_THRESHOLD = float(os.getenv("FAIR_LATENCY_THRESHOLD", "200.0"))  # 200ms
    
    # 数据缓冲配置
    DATA_BUFFER_SIZE = int(os.getenv("DATA_BUFFER_SIZE", "1000"))  # 最多缓存1000条消息
    BUFFER_RETENTION_SECONDS = float(os.getenv("BUFFER_RETENTION_SECONDS", "300.0"))  # 5分钟
    
    # 故障切换配置
    FAILOVER_ENABLED = os.getenv("FAILOVER_ENABLED", "true").lower() == "true"
    FAILOVER_THRESHOLD_ERRORS = int(os.getenv("FAILOVER_THRESHOLD_ERRORS", "5"))  # 5次错误后切换
    FAILOVER_THRESHOLD_LATENCY = float(os.getenv("FAILOVER_THRESHOLD_LATENCY", "500.0"))  # 500ms延迟阈值
    
    # 心跳配置
    HEARTBEAT_INTERVAL = float(os.getenv("HEARTBEAT_INTERVAL", "30.0"))  # 30秒心跳
    HEARTBEAT_TIMEOUT = float(os.getenv("HEARTBEAT_TIMEOUT", "10.0"))  # 10秒超时
    
    @classmethod
    def get_all_config(cls) -> Dict[str, Any]:
        """获取所有配置"""
        return {
            # 连接池基础配置
            "max_connections_per_exchange": cls.MAX_CONNECTIONS_PER_EXCHANGE,
            "connection_pool_size": cls.CONNECTION_POOL_SIZE,
            "connection_monitor_interval": cls.CONNECTION_MONITOR_INTERVAL,
            
            # 重连配置
            "base_reconnect_delay": cls.BASE_RECONNECT_DELAY,
            "max_reconnect_delay": cls.MAX_RECONNECT_DELAY,
            "reconnect_jitter": cls.RECONNECT_JITTER,
            "max_reconnect_attempts": cls.MAX_RECONNECT_ATTEMPTS,
            
            # 定期重启配置
            "connection_restart_interval": cls.CONNECTION_RESTART_INTERVAL,
            "restart_window_start": cls.RESTART_WINDOW_START,
            "restart_window_end": cls.RESTART_WINDOW_END,
            
            # 连接质量配置
            "quality_check_interval": cls.QUALITY_CHECK_INTERVAL,
            "poor_quality_threshold": cls.POOR_QUALITY_THRESHOLD,
            "excellent_latency_threshold": cls.EXCELLENT_LATENCY_THRESHOLD,
            "good_latency_threshold": cls.GOOD_LATENCY_THRESHOLD,
            "fair_latency_threshold": cls.FAIR_LATENCY_THRESHOLD,
            
            # 数据缓冲配置
            "data_buffer_size": cls.DATA_BUFFER_SIZE,
            "buffer_retention_seconds": cls.BUFFER_RETENTION_SECONDS,
            
            # 故障切换配置
            "failover_enabled": cls.FAILOVER_ENABLED,
            "failover_threshold_errors": cls.FAILOVER_THRESHOLD_ERRORS,
            "failover_threshold_latency": cls.FAILOVER_THRESHOLD_LATENCY,
            
            # 心跳配置
            "heartbeat_interval": cls.HEARTBEAT_INTERVAL,
            "heartbeat_timeout": cls.HEARTBEAT_TIMEOUT,
        }
    
    @classmethod
    def validate_config(cls) -> bool:
        """验证配置有效性"""
        try:
            # 检查基础配置
            assert cls.MAX_CONNECTIONS_PER_EXCHANGE > 0, "每个交易所最大连接数必须大于0"
            assert cls.CONNECTION_POOL_SIZE > 0, "连接池大小必须大于0"
            assert cls.CONNECTION_MONITOR_INTERVAL > 0, "监控间隔必须大于0"
            
            # 检查重连配置
            assert cls.BASE_RECONNECT_DELAY > 0, "基础重连延迟必须大于0"
            assert cls.MAX_RECONNECT_DELAY >= cls.BASE_RECONNECT_DELAY, "最大重连延迟必须大于等于基础延迟"
            assert 0 <= cls.RECONNECT_JITTER <= 1, "重连抖动必须在0-1之间"
            assert cls.MAX_RECONNECT_ATTEMPTS > 0, "最大重连次数必须大于0"
            
            # 检查重启配置
            assert cls.CONNECTION_RESTART_INTERVAL > 0, "重启间隔必须大于0"
            assert 0 <= cls.RESTART_WINDOW_START <= 23, "重启窗口开始时间必须在0-23之间"
            assert 0 <= cls.RESTART_WINDOW_END <= 23, "重启窗口结束时间必须在0-23之间"
            
            # 检查质量配置
            assert cls.QUALITY_CHECK_INTERVAL > 0, "质量检查间隔必须大于0"
            assert 0 <= cls.POOR_QUALITY_THRESHOLD <= 1, "质量阈值必须在0-1之间"
            
            # 检查缓冲配置
            assert cls.DATA_BUFFER_SIZE > 0, "数据缓冲大小必须大于0"
            assert cls.BUFFER_RETENTION_SECONDS > 0, "缓冲保留时间必须大于0"
            
            # 检查故障切换配置
            assert cls.FAILOVER_THRESHOLD_ERRORS > 0, "故障切换错误阈值必须大于0"
            assert cls.FAILOVER_THRESHOLD_LATENCY > 0, "故障切换延迟阈值必须大于0"
            
            # 检查心跳配置
            assert cls.HEARTBEAT_INTERVAL > 0, "心跳间隔必须大于0"
            assert cls.HEARTBEAT_TIMEOUT > 0, "心跳超时必须大于0"
            
            return True
            
        except AssertionError as e:
            print(f"❌ 配置验证失败: {e}")
            return False
    
    @classmethod
    def print_config(cls):
        """打印配置信息"""
        print("🔧 连接池配置信息:")
        print(f"   📊 连接池大小: {cls.CONNECTION_POOL_SIZE}")
        print(f"   📊 每个交易所最大连接数: {cls.MAX_CONNECTIONS_PER_EXCHANGE}")
        print(f"   📊 监控间隔: {cls.CONNECTION_MONITOR_INTERVAL}秒")
        print(f"   🔄 重连基础延迟: {cls.BASE_RECONNECT_DELAY}秒")
        print(f"   🔄 重连最大延迟: {cls.MAX_RECONNECT_DELAY}秒")
        print(f"   🔄 最大重连次数: {cls.MAX_RECONNECT_ATTEMPTS}")
        print(f"   🔄 重连抖动: {cls.RECONNECT_JITTER * 100}%")
        print(f"   🔄 定期重启间隔: {cls.CONNECTION_RESTART_INTERVAL}小时")
        print(f"   🔄 重启窗口: {cls.RESTART_WINDOW_START}:00-{cls.RESTART_WINDOW_END}:00")
        print(f"   📈 质量检查间隔: {cls.QUALITY_CHECK_INTERVAL}秒")
        print(f"   📈 质量阈值: {cls.POOR_QUALITY_THRESHOLD * 100}%")
        print(f"   💾 数据缓冲大小: {cls.DATA_BUFFER_SIZE}")
        print(f"   💾 缓冲保留时间: {cls.BUFFER_RETENTION_SECONDS}秒")
        print(f"   🔀 故障切换: {'启用' if cls.FAILOVER_ENABLED else '禁用'}")
        print(f"   💓 心跳间隔: {cls.HEARTBEAT_INTERVAL}秒")


# 配置验证
if __name__ == "__main__":
    if ConnectionPoolConfig.validate_config():
        print("✅ 连接池配置验证通过")
        ConnectionPoolConfig.print_config()
    else:
        print("❌ 连接池配置验证失败")
