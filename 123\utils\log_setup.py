"""
日志系统初始化模块
为整个系统提供统一的日志配置
"""

import os
import sys
import logging
from logging.handlers import RotatingFileHandler
from datetime import datetime

# 尝试导入调试配置
try:
    from config.debug_config import LOG_CONFIG, LogLevel
    HAS_CONFIG = True
except ImportError:
    HAS_CONFIG = False
    # 🔥 修复：使用默认日志设置，无需调试输出

# 尝试导入Rich库用于美化终端输出
try:
    from rich.logging import RichHandler
    from rich.console import Console
    HAS_RICH = True
except ImportError:
    HAS_RICH = False

# 默认日志配置
DEFAULT_LOG_CONFIG = {
    "file_level": logging.DEBUG,  # 文件记录所有级别的详细日志
    "console_level": logging.CRITICAL + 10,  # 终端完全禁用日志输出（按用户要求）
    "log_dir": os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "logs"),  # 项目内logs目录
    "max_file_size": 50 * 1024 * 1024,  # 50MB
    "backup_count": 10,
    "use_rich_console": HAS_RICH,
    "include_thread_info": True
}


def get_log_config():
    """获取日志配置"""
    if HAS_CONFIG:
        # 处理LogLevel枚举和普通整数值
        file_level = LOG_CONFIG.get("file_level", LogLevel.DEBUG)
        console_level = LOG_CONFIG.get("console_level", 60)  # 默认禁用终端输出

        # 如果是枚举类型，获取其值
        if hasattr(file_level, 'value'):
            file_level = file_level.value
        if hasattr(console_level, 'value'):
            console_level = console_level.value

        config = {
            "file_level": file_level,
            "console_level": console_level,
            "log_dir": LOG_CONFIG.get("log_dir", DEFAULT_LOG_CONFIG["log_dir"]),
            "max_file_size": LOG_CONFIG.get("max_file_size", DEFAULT_LOG_CONFIG["max_file_size"]),
            "backup_count": LOG_CONFIG.get("backup_count", DEFAULT_LOG_CONFIG["backup_count"]),
            "use_rich_console": LOG_CONFIG.get("use_rich_console", DEFAULT_LOG_CONFIG["use_rich_console"]),
            "include_thread_info": LOG_CONFIG.get("include_thread_info", DEFAULT_LOG_CONFIG["include_thread_info"])
        }
    else:
        config = DEFAULT_LOG_CONFIG

    return config


def setup_logging():
    """设置统一日志系统 - 只保留用户指定的8个日志文件"""
    config = get_log_config()

    # 创建日志目录
    os.makedirs(config["log_dir"], exist_ok=True)

    # 配置根日志器
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.DEBUG)  # 设置为最低级别，让处理器决定哪些级别的日志显示

    # 清除现有处理器
    if root_logger.handlers:
        root_logger.handlers.clear()

    # 确保所有子日志器都继承根日志器的处理器
    logging.getLogger().propagate = True

    # 日期格式
    date_str = datetime.now().strftime('%Y%m%d')

    # 🔥 用户要求：创建文件日志格式 - 支持毫秒显示(HH:MM:SS.mmm)
    if config["include_thread_info"]:
        file_formatter = logging.Formatter(
            '%(asctime)s.%(msecs)03d [%(levelname)s] [%(name)s] %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
    else:
        file_formatter = logging.Formatter(
            '%(asctime)s.%(msecs)03d [%(levelname)s] [%(name)s] %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )

    # 🔥 用户指定的8个日志文件配置 - 全部在项目内logs目录
    log_files_config = [
        # 1. 错误日志 - 111 乱套了/logs/error_20250627.log
        {
            'name': 'error',
            'path': os.path.join(config["log_dir"], f'error_{date_str}.log'),
            'level': logging.ERROR,
            'filter_loggers': None  # 所有错误
        },
        # 2. 缓存监控日志 - 111 乱套了/logs/cache_monitor.log
        {
            'name': 'cache_monitor',
            'path': os.path.join(config["log_dir"], 'cache_monitor.log'),
            'level': logging.DEBUG,
            'filter_loggers': ['TradingRulesPreloader', 'MarginCalculator', 'UnifiedBalanceManager', 'cache', 'CacheMonitor']
        },
        # 3. Bybit交易所日志 - 111 乱套了/logs/bybit_exchange.log
        {
            'name': 'bybit_exchange',
            'path': os.path.join(config["log_dir"], 'bybit_exchange.log'),
            'level': logging.INFO,  # 🔥 修复：减少调试日志，保持交易所日志一致性
            'filter_loggers': ['exchanges.bybit_exchange', 'BybitExchange', 'bybit']
        },
        # 4. 执行引擎日志 - 111 乱套了/logs/execution_engine.log
        {
            'name': 'execution_engine',
            'path': os.path.join(config["log_dir"], 'execution_engine.log'),
            'level': logging.DEBUG,
            'filter_loggers': ['ExecutionEngine', 'execution']
        },
        # 5. Gate交易所日志 - 111 乱套了/logs/gate_exchange.log
        {
            'name': 'gate_exchange',
            'path': os.path.join(config["log_dir"], 'gate_exchange.log'),
            'level': logging.INFO,  # 🔥 修复：减少调试日志，只记录INFO及以上级别
            'filter_loggers': ['exchanges.gate_exchange', 'GateExchange', 'gate']
        },
        # 6. OKX交易所日志 - 111 乱套了/logs/OKXExchange.log
        {
            'name': 'okx_exchange',
            'path': os.path.join(config["log_dir"], 'OKXExchange.log'),
            'level': logging.INFO,  # 🔥 修复：减少调试日志，保持交易所日志一致性
            'filter_loggers': ['exchanges.okx_exchange', 'OKXExchange', 'okx']
        },
        # 7. WebSocket订单簿日志 - /root/myproject/logs/websocket_orderbook_20250627.log
        # 🔥 VPS修复：减少订单簿DEBUG日志，只记录重要信息
        {
            'name': 'websocket_orderbook',
            'path': os.path.join(config["log_dir"], f'websocket_orderbook_{date_str}.log'),
            'level': logging.INFO,  # 🔥 修复：只记录INFO级别，减少DEBUG刷屏
            'filter_loggers': ['websocket_orderbook']  # 🔥 修复：只记录专门的订单簿日志器
        },
        # 8. WebSocket价格日志 - 🔥 修复：文件名不带时间戳，便于终端显示
        # 🔥 VPS修复：专门的价格日志器，只记录价格+深度+差价
        {
            'name': 'websocket_prices_only',
            'path': os.path.join(config["log_dir"], 'websocket_prices.log'),  # 🔥 修复：去除时间戳
            'level': logging.INFO,  # 🔥 修复：只记录INFO级别
            'filter_loggers': ['websocket_prices_only'],  # 🔥 专门的价格日志器
            'exclusive': True,  # 🔥 独占模式：只记录指定日志器的内容
            'header': True  # 🔥 添加文件头部说明
        }
    ]

    # 创建所有指定的日志处理器
    for log_config in log_files_config:
        # 确保日志文件目录存在
        log_dir = os.path.dirname(log_config['path'])
        os.makedirs(log_dir, exist_ok=True)

        # 创建文件处理器
        handler = RotatingFileHandler(
            log_config['path'],
            maxBytes=config["max_file_size"],
            backupCount=config["backup_count"],
            encoding='utf-8'
        )
        handler.setLevel(log_config['level'])

        # 🔥 用户要求：websocket_prices_only使用毫秒时间戳格式，便于终端显示
        if log_config.get('name') == 'websocket_prices_only':
            # 🔥 期现套利专用格式：毫秒时间戳 + 套利信息，便于终端实时监控
            arbitrage_formatter = logging.Formatter('%(asctime)s.%(msecs)03d %(message)s', datefmt='%H:%M:%S')
            handler.setFormatter(arbitrage_formatter)
        else:
            handler.setFormatter(file_formatter)

        # 🔥 VPS修复：支持独占模式，专门的价格日志器
        if log_config.get('exclusive', False):
            # 独占模式：只记录指定日志器的内容，不传播到根日志器
            if log_config['filter_loggers']:
                for logger_name in log_config['filter_loggers']:
                    exclusive_logger = logging.getLogger(logger_name)
                    exclusive_logger.setLevel(log_config['level'])
                    exclusive_logger.propagate = False  # 🔥 关键：不传播到根日志器
                    exclusive_logger.addHandler(handler)

                    # 🔥 添加文件头部说明 - 只在文件为空时写入，避免重复
                    if log_config.get('header', False) and logger_name == 'websocket_prices_only':
                        # 检查文件是否为空或不存在
                        log_file_path = log_config['path']
                        should_write_header = False

                        try:
                            if not os.path.exists(log_file_path):
                                should_write_header = True
                            else:
                                # 检查文件大小，如果小于100字节认为是空文件
                                file_size = os.path.getsize(log_file_path)
                                if file_size < 100:
                                    should_write_header = True
                        except Exception:
                            should_write_header = True

                        if should_write_header:
                            header_msg = (
                                "================================================================================\n"
                                "              🚀 期货溢价套利系统 - 实时监控终端 (VPS模式)\n"
                                "================================================================================\n"
                                "📊 6种期现套利组合 (根据全流程工作流文档):\n"
                                "   [A] Gate现货 ↔ Bybit期货     |   [B] Bybit现货 ↔ Gate期货\n"
                                "   [C] OKX现货  ↔ Bybit期货     |   [D] Bybit现货 ↔ OKX期货\n"
                                "   [E] OKX现货  ↔ Gate期货      |   [F] Gate现货  ↔ OKX期货\n"
                                "🎯 专注期货溢价套利 (期货价格 > 现货价格):\n"
                                "   +X.XXX% = 期货溢价 → 买现货+卖期货 (主要套利机会)\n"
                                "   -X.XXX% = 现货溢价 → 卖现货+买期货 (反向套利)\n"
                                "   0.000%  = 无差价   → 无套利机会\n"
                                "================================================================================\n"
                                "🚀 实时监控格式: [组合] 币种 | 现货交易所$价格 ↔ 期货交易所$价格 | 差价% | 类型 | 操作\n"
                                "================================================================================\n"
                            )
                            exclusive_logger.info(header_msg)
        else:
            # 普通模式：创建过滤器并添加到根日志器
            if log_config['filter_loggers']:
                class LoggerFilter:
                    def __init__(self, logger_names):
                        self.logger_names = logger_names

                    def filter(self, record):
                        return any(name in record.name for name in self.logger_names)

                handler.addFilter(LoggerFilter(log_config['filter_loggers']))

            root_logger.addHandler(handler)

    # 确保所有常用的日志器都正确配置
    common_loggers = [
        'main', 'arbitrage_system', 'websocket', 'exchanges', 'trading', 'core',
        'ArbitrageEngine', 'OpportunityScanner', 'fund_management', 'monitoring',
        'websocket.gate', 'websocket.bybit', 'websocket.okx',
        'exchanges.gate', 'exchanges.bybit', 'exchanges.okx'
    ]

    for logger_name in common_loggers:
        logger = logging.getLogger(logger_name)
        logger.setLevel(logging.DEBUG)
        logger.propagate = True  # 确保日志传播到根日志器

    # 终端日志处理器
    if config["use_rich_console"] and HAS_RICH:
        # 使用Rich美化终端输出
        console = Console()
        console_handler = RichHandler(
            console=console,
            show_time=True,
            show_path=False,
            markup=True,
            rich_tracebacks=True
        )
    else:
        # 使用标准终端输出
        console_handler = logging.StreamHandler(sys.stdout)

    # 🔥 用户要求：设置终端日志级别和格式 - 支持毫秒显示
    console_handler.setLevel(config["console_level"])
    console_formatter = logging.Formatter('%(asctime)s.%(msecs)03d | %(levelname)-8s | %(message)s', datefmt='%H:%M:%S')
    console_handler.setFormatter(console_formatter)

    # 添加终端处理器
    root_logger.addHandler(console_handler)

    # 记录启动信息到文件
    root_logger.info(f"🔥 统一日志系统初始化完成 - 8个指定日志文件已配置")
    root_logger.debug(f"日志配置: 文件级别={config['file_level']}, "
                f"终端级别={config['console_level']}")

    return root_logger


# 使用方法示例
if __name__ == "__main__":
    # 初始化日志系统
    setup_logging()

    # 获取各模块的日志器
    ws_logger = logging.getLogger("websocket")
    exchange_logger = logging.getLogger("exchanges")
    core_logger = logging.getLogger("core")

    # 测试各级别日志
    ws_logger.debug("这是一条WebSocket的调试日志，只会出现在文件中")
    ws_logger.info("这是一条WebSocket的信息日志，会同时出现在终端和文件中")
    ws_logger.warning("这是一条WebSocket的警告日志")

    exchange_logger.debug("这是一条交易所API的调试日志")
    exchange_logger.info("这是一条交易所API的信息日志")

    core_logger.info("这是一条核心模块的信息日志")
    core_logger.error("这是一条错误日志，会被记录到错误日志文件中")

    # 🔥 修复：日志文件位置信息通过正式日志输出
    logging.info(f"日志文件保存在: {os.path.abspath(DEFAULT_LOG_CONFIG['log_dir'])}")