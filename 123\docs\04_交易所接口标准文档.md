# 🏪 通用期货溢价套利系统 - 交易所接口标准文档

## 📋 文档概述

本文档定义了Gate.io、Bybit、OKX三个交易所的统一接口规范和实现要求，确保所有交易所使用完全一致的接口标准，实现零重复代码和统一管理。

## 🔥 **统一模块接口标准（2025-06-29更新）**

### 📊 **订单簿数据处理统一接口**

#### **统一验证接口**
```python
# 🔥 统一订单簿验证接口
def validate_orderbook_data(
    orderbook: Dict[str, Any],
    exchange: str = "",
    symbol: str = "",
    market_type: str = "spot",
    config: Optional[OrderbookValidationConfig] = None
) -> OrderbookValidationResult:
    """统一订单簿数据验证接口"""

# 🔥 统一同步性验证接口
def validate_orderbook_synchronization(
    spot_orderbook: Dict[str, Any],
    futures_orderbook: Dict[str, Any],
    max_time_diff_ms: int = 1000
) -> Tuple[bool, str]:
    """统一订单簿同步性验证接口"""
```

#### **统一格式化接口**
```python
# 🔥 统一数据格式化接口
def format_orderbook_data(
    asks: List[Any],
    bids: List[Any],
    symbol: str,
    exchange: str,
    market_type: str = "spot",
    timestamp: Optional[int] = None,
    additional_fields: Optional[Dict[str, Any]] = None,
    config: Optional[OrderbookFormatConfig] = None
) -> Dict[str, Any]:
    """统一订单簿数据格式化接口"""

# 🔥 统一符号标准化接口
def normalize_symbol(symbol: str, target_format: str = "standard") -> str:
    """统一交易对符号标准化接口"""
```

#### **统一时间戳处理接口**
```python
# 🔥 统一时间戳获取接口
def get_synced_timestamp(
    exchange_name: str,
    data: Optional[Dict[str, Any]] = None
) -> int:
    """统一时间戳获取接口"""

# 🔥 统一时间同步接口
async def sync_all_exchanges(
    exchanges: list[str] = ["gate", "bybit", "okx"],
    force: bool = False
) -> Dict[str, bool]:
    """统一多交易所时间同步接口"""
```

#### **统一深度分析接口**
```python
# 🔥 统一深度分析接口
def analyze_orderbook_depth(
    orderbook: Dict[str, Any],
    required_amount: float,
    side: str,
    reference_price: float = 0.0,
    exchange: str = "",
    symbol: str = "",
    config: Optional[DepthAnalysisConfig] = None
) -> DepthAnalysisResult:
    """统一订单簿深度分析接口"""
```

## 🎯 核心设计原则

- **接口统一**: 三个交易所使用完全相同的方法签名
- **参数标准化**: 统一的参数格式和命名规范
- **返回值一致**: 统一的返回数据结构
- **错误处理统一**: 统一的异常类型和错误码

## 📊 1. 基础接口标准

### 1.1 交易所基类规范

```python
# 文件位置: exchanges/exchanges_base.py

class BaseExchange(ABC):
    """交易所基类 - 定义统一接口标准"""
    
    # 🔥 必须实现的核心属性
    name: str                    # 交易所名称
    exchange_name: str           # 兼容性属性
    api_key: str                # API密钥
    api_secret: str             # API密钥
    passphrase: str             # OKX需要的passphrase
    rate_limit: int             # 请求频率限制
    
    # 🔥 统计属性
    request_count: int          # 请求计数
    error_count: int            # 错误计数
    last_request_time: float    # 最后请求时间
```

### 1.2 统一初始化规范

```python
def __init__(self, name: str, api_key: str, api_secret: str, **kwargs):
    """
    统一初始化规范
    
    Args:
        name: 交易所名称 ("Gate.io"/"Bybit"/"OKX")
        api_key: API密钥
        api_secret: API密钥
        **kwargs: 交易所特定参数
            - passphrase: OKX需要
            - rate_limit: 请求频率限制
            - testnet: 是否测试网络
    """
    # 🔥 统一初始化逻辑
    self.name = name
    self.exchange_name = name
    self.api_key = api_key
    self.api_secret = api_secret
    self.passphrase = kwargs.get("passphrase", "")
    
    # 🔥 统一请求限制配置
    default_rate_limit = 10
    env_rate_limit = os.environ.get(f"{name.upper().replace('.', '')}_RATE_LIMIT")
    self.rate_limit = int(env_rate_limit) if env_rate_limit else kwargs.get("rate_limit", default_rate_limit)
    
    # 🔥 统一统计初始化
    self.request_count = 0
    self.error_count = 0
    self.last_request_time = 0
```

## 🛒 2. 核心交易接口标准

### 2.1 下单接口标准

```python
@abstractmethod
async def place_order(
    self,
    symbol: str,
    side: OrderSide,
    order_type: OrderType,
    amount: float,
    price: Optional[float] = None,
    market_type: str = "spot",
    params: Optional[Dict] = None
) -> Dict[str, Any]:
    """
    统一下单接口标准
    
    Args:
        symbol: 交易对 (统一格式: BTC-USDT)
        side: 买卖方向 (OrderSide.BUY/SELL)
        order_type: 订单类型 (OrderType.MARKET，禁用LIMIT)
        amount: 交易数量 (基础币种数量)
        price: 价格 (市价单传None)
        market_type: 市场类型 ("spot"/"futures")
        params: 交易所特定参数
    
    Returns:
        Dict[str, Any]: 统一订单信息格式
        {
            "order_id": str,        # 订单ID
            "symbol": str,          # 交易对
            "side": str,            # 买卖方向
            "amount": float,        # 委托数量
            "filled_amount": float, # 成交数量
            "price": float,         # 委托价格
            "average_price": float, # 成交均价
            "status": str,          # 订单状态
            "timestamp": int,       # 时间戳
            "fee": float,          # 手续费
            "fee_currency": str    # 手续费币种
        }
    
    实际实现方式:
        1. 使用统一开仓管理器 (UnifiedOpeningManager)
        2. 通过opening_manager.prepare_opening_params()准备参数
        3. 通过opening_manager.execute_opening_order()执行下单
        4. 自动处理符号转换、精度处理、频率限制
        5. 统一异常处理和统计记录
    """
    pass
```

### 2.2 余额查询接口标准

```python
@abstractmethod
async def get_balance(self, account_type: AccountType) -> Dict[str, Any]:
    """
    统一余额查询接口标准

    Args:
        account_type: 账户类型
            - AccountType.SPOT: 现货账户
            - AccountType.FUTURES: 期货账户
            - AccountType.UNIFIED: 统一账户 (Bybit/OKX)

    Returns:
        Dict[str, Any]: 统一余额格式
        {
            "USDT": {"available": 1000.0, "locked": 0.0},
            "BTC": {"available": 0.5, "locked": 0.0},
            ...
        }
        或简化格式：
        {
            "USDT": 1000.0,    # 直接返回可用余额
            "BTC": 0.5,
            ...
        }
    
    实现要求:
        1. Gate.io: 分离账户，需要分别查询spot/futures
        2. Bybit: 统一账户，使用unified账户类型
        3. OKX: 统一账户，使用trading账户类型
        4. 必须返回可用余额 (available)，不是总余额
        5. 必须过滤掉余额为0的币种
    """
    pass
```

### 2.3 持仓查询接口标准

```python
@abstractmethod
async def get_position(self, symbol: str = None) -> List[Dict[str, Any]]:
    """
    统一持仓查询接口标准
    
    Args:
        symbol: 交易对 (可选，None表示查询所有持仓)
    
    Returns:
        List[Dict[str, Any]]: 统一持仓格式
        [{
            "symbol": str,          # 交易对
            "side": str,            # 持仓方向 ("long"/"short")
            "size": float,          # 持仓数量 (正数)
            "entry_price": float,   # 开仓均价
            "mark_price": float,    # 标记价格
            "unrealized_pnl": float,# 未实现盈亏
            "margin": float,        # 保证金
            "leverage": int,        # 杠杆倍数
            "timestamp": int        # 时间戳
        }]
    
    实现要求:
        1. 只返回有持仓的合约 (size > 0)
        2. 统一持仓方向命名 ("long"/"short")
        3. 数量统一为正数，方向通过side字段区分
        4. 必须包含未实现盈亏和保证金信息
    """
    pass
```

## 🔧 3. 交易所特定实现要求

### 3.1 Gate.io实现要求

```python
# 文件位置: exchanges/gate_exchange.py

class GateExchange(BaseExchange):
    """Gate.io交易所实现"""
    
    # 🔥 特定要求
    def __init__(self, api_key: str, api_secret: str, **kwargs):
        super().__init__("Gate.io", api_key, api_secret, **kwargs)
        # Gate.io特定初始化
        self.spot_client = gate_api.SpotApi(...)
        self.futures_client = gate_api.FuturesApi(...)
    
    # 🔥 符号转换要求 - 使用统一货币适配器
    def _convert_symbol(self, symbol: str, market_type: str = "spot") -> str:
        """Gate.io符号转换 - 使用统一货币适配器"""
        from exchanges.currency_adapter import get_exchange_symbol
        return get_exchange_symbol(symbol, "gate", market_type)
    
    # 🔥 杠杆设置特殊要求
    async def set_leverage(self, symbol: str, leverage: int) -> bool:
        """Gate.io杠杆设置使用表单格式，不是JSON"""
        # 必须使用表单格式，不能使用JSON
        data = f"symbol={symbol}&leverage={leverage}"
        headers = {"Content-Type": "application/x-www-form-urlencoded"}
    
    # 🚨 订单簿数据禁用REST API - 强制使用WebSocket
    async def get_orderbook(self, symbol: str, market_type: str = "spot", limit: int = 10):
        """禁用REST API获取订单簿 - 强制使用WebSocket数据源"""
        self.logger.warning(f"🚨 get_orderbook已禁用！订单簿数据只能使用WebSocket！")
        return {'asks': [], 'bids': [], 'error': 'REST_API_DISABLED_USE_WEBSOCKET'}

    # 🔥 账户类型处理
    async def get_balance(self, account_type: AccountType) -> Dict[str, float]:
        """Gate.io分离账户处理"""
        if account_type == AccountType.SPOT:
            # 查询现货账户
            return await self._get_spot_balance()
        elif account_type == AccountType.FUTURES:
            # 查询期货账户
            return await self._get_futures_balance()
        else:
            raise ValueError("Gate.io不支持统一账户")
```

### 3.2 Bybit实现要求

```python
# 文件位置: exchanges/bybit_exchange.py

class BybitExchange(BaseExchange):
    """Bybit交易所实现"""
    
    # 🔥 特定要求
    def __init__(self, api_key: str, api_secret: str, **kwargs):
        super().__init__("Bybit", api_key, api_secret, **kwargs)
        # Bybit统一账户
        self.client = pybit.HTTP(...)
    
    # 🔥 符号转换要求 - 使用统一货币适配器
    def _convert_symbol(self, symbol: str, market_type: str = "spot") -> str:
        """Bybit符号转换 - 使用统一货币适配器"""
        from exchanges.currency_adapter import get_exchange_symbol
        return get_exchange_symbol(symbol, "bybit", market_type)
    
    # 🚨 订单簿数据禁用REST API - 强制使用WebSocket
    async def get_orderbook(self, symbol: str, market_type: str = "spot", limit: int = 10):
        """禁用REST API获取订单簿 - 强制使用WebSocket数据源"""
        self.logger.warning(f"🚨 get_orderbook已禁用！订单簿数据只能使用WebSocket！")
        return {'asks': [], 'bids': [], 'error': 'REST_API_DISABLED_USE_WEBSOCKET'}

    # 🔥 统一账户处理
    async def get_balance(self, account_type: AccountType) -> Dict[str, float]:
        """Bybit统一账户处理"""
        if account_type in [AccountType.UNIFIED, AccountType.SPOT, AccountType.FUTURES]:
            # 统一账户查询
            return await self._get_unified_balance()
        else:
            raise ValueError("Bybit只支持统一账户")
    
    # 🔥 持仓查询特殊处理
    async def get_position(self, symbol: str = None) -> List[Dict[str, Any]]:
        """Bybit持仓查询需要分别查询USDT和USDC合约"""
        positions = []
        # 查询USDT永续合约
        usdt_positions = await self._get_usdt_positions(symbol)
        positions.extend(usdt_positions)
        return positions
```

### 3.3 OKX实现要求

```python
# 文件位置: exchanges/okx_exchange.py

class OKXExchange(BaseExchange):
    """OKX交易所实现"""
    
    # 🔥 特定要求
    def __init__(self, api_key: str, api_secret: str, passphrase: str, **kwargs):
        super().__init__("OKX", api_key, api_secret, passphrase=passphrase, **kwargs)
        # OKX需要passphrase
        self.client = okx.Client(api_key, api_secret, passphrase, ...)
    
    # 🔥 符号转换要求 - 使用统一货币适配器
    def _convert_symbol(self, symbol: str, market_type: str = "spot") -> str:
        """OKX符号转换 - 使用统一货币适配器"""
        from exchanges.currency_adapter import get_exchange_symbol
        return get_exchange_symbol(symbol, "okx", market_type)
    
    # 🔥 合约数量转换要求
    def _convert_amount(self, amount: float, symbol: str, market_type: str) -> float:
        """OKX期货合约数量转换"""
        if market_type == "futures":
            # 期货合约需要转换为张数
            contract_value = self._get_contract_value(symbol)
            return amount / contract_value
        return amount
    
    # 🚨 订单簿数据禁用REST API - 强制使用WebSocket
    async def get_orderbook(self, symbol: str, market_type: str = "spot", limit: int = 10):
        """禁用REST API获取订单簿 - 强制使用WebSocket数据源"""
        self.logger.warning(f"🚨 get_orderbook已禁用！订单簿数据只能使用WebSocket！")
        return {'asks': [], 'bids': [], 'error': 'REST_API_DISABLED_USE_WEBSOCKET'}

    # 🔥 统一账户处理
    async def get_balance(self, account_type: AccountType) -> Dict[str, float]:
        """OKX统一账户处理"""
        if account_type in [AccountType.UNIFIED, AccountType.SPOT, AccountType.FUTURES]:
            # 查询trading账户
            return await self._get_trading_balance()
        else:
            raise ValueError("OKX只支持统一账户")
```

## 🔄 4. 统一模块集成标准

### 4.1 统一初始化器集成

```python
# 所有交易所必须支持统一初始化器
from exchanges.unified_exchange_initializer import init_exchange_modules

# 统一初始化方式
modules = init_exchange_modules("Gate.io")  # 或 "Bybit" 或 "OKX"

# 获取统一模块
currency_adapter = modules["currency_adapter"]
rules_preloader = modules["rules_preloader"]
opening_manager = modules["opening_manager"]
closing_manager = modules["closing_manager"]
```

### 4.2 统一管理器集成

```python
# 所有交易所必须支持统一开仓管理器
result = await opening_manager.unified_market_buy(
    symbol="BTC-USDT",
    quantity=0.001,
    exchange=exchange,
    market_type="spot",
    orderbook=orderbook_data
)

# 所有交易所必须支持统一平仓管理器
result = await closing_manager.close_position_unified(
    symbol="BTC-USDT",
    exchange=exchange,
    market_type="spot",
    side="sell",
    orderbook=orderbook_data
)
```

## 🛠️ 5. 辅助功能标准

### 5.1 请求频率限制标准

```python
async def _rate_limit(self) -> None:
    """统一请求频率限制实现"""
    current_time = time.time()
    time_since_last = current_time - self.last_request_time
    min_interval = 1.0 / self.rate_limit
    
    if time_since_last < min_interval:
        await asyncio.sleep(min_interval - time_since_last)
    
    self.last_request_time = time.time()
    self.request_count += 1
```

### 5.2 统计信息标准

```python
def get_stats(self) -> Dict[str, Any]:
    """统一统计信息格式"""
    return {
        "name": self.name,
        "request_count": self.request_count,
        "error_count": self.error_count,
        "success_rate": (self.request_count - self.error_count) / max(self.request_count, 1),
        "last_request_time": self.last_request_time
    }
```

### 5.3 异常处理标准

```python
async def _handle_common_errors(self, func, *args, **kwargs):
    """统一异常处理"""
    try:
        await self._rate_limit()
        result = await func(*args, **kwargs)
        return result
    except Exception as e:
        self.error_count += 1
        logger.error(f"{self.name} API错误: {e}")
        raise
```

## ✅ 6. 接口验证标准

### 6.1 必须通过的测试

```python
# 每个交易所实现必须通过以下测试
async def test_exchange_interface():
    """交易所接口标准测试"""
    
    # 1. 初始化测试
    exchange = ExchangeClass(api_key, api_secret)
    assert exchange.name in ["Gate.io", "Bybit", "OKX"]
    
    # 2. 余额查询测试
    balance = await exchange.get_balance(AccountType.SPOT)
    assert isinstance(balance, dict)
    
    # 3. 下单接口测试
    order = await exchange.place_order(
        "BTC-USDT", OrderSide.BUY, OrderType.MARKET, 0.001
    )
    assert "order_id" in order
    
    # 4. 统计信息测试
    stats = exchange.get_stats()
    assert "success_rate" in stats
```

### 6.2 性能要求

```python
# 性能标准
- 下单延迟: < 100ms
- 余额查询: < 200ms
- 持仓查询: < 300ms
- 请求成功率: > 99%
- 连接稳定性: > 99.9%
```

## 🎯 7. 实现检查清单

### 7.1 必须实现的接口

- [ ] `place_order()` - 统一下单接口
- [ ] `get_balance()` - 统一余额查询
- [ ] `get_position()` - 统一持仓查询
- [ ] `close_position()` - 统一平仓接口
- [ ] `set_leverage()` - 统一杠杆设置
- [ ] `get_stats()` - 统一统计信息

### 7.2 必须集成的模块

- [ ] `UnifiedExchangeInitializer` - 统一初始化器
- [ ] `CurrencyAdapter` - 货币适配器
- [ ] `TradingRulesPreloader` - 交易规则预加载器
- [ ] `UnifiedOpeningManager` - 统一开仓管理器
- [ ] `UnifiedClosingManager` - 统一平仓管理器

### 7.3 必须遵循的规范

- [ ] 统一的方法签名
- [ ] 统一的返回值格式
- [ ] 统一的异常处理
- [ ] 统一的请求频率限制
- [ ] 统一的日志记录格式

---

**📝 注意**: 本文档定义的接口标准为强制性规范，所有交易所实现必须严格遵循这些标准，确保系统的一致性和可维护性。任何偏离标准的实现都可能导致系统异常。
