"""
动态查询交易对最小金额要求模块

功能：
1. 动态查询OKX、Gate、Bybit的交易对最小金额要求
2. 基于.env配置的交易对列表自动检测
3. 缓存查询结果，避免频繁API调用
4. 提供统一接口给执行引擎使用

Author: AI Assistant
Date: 2024-12-30
"""

import asyncio
import json
import os
import time
from typing import Dict, List, Optional, Tuple
from decimal import Decimal
from config.settings import get_config
from utils.logger import get_logger
from exchanges.currency_adapter import get_exchange_symbol

class MinOrderDetector:
    """交易对最小金额要求检测器"""
    
    def __init__(self):
        self.config = get_config()
        self.logger = get_logger("MinOrderDetector")
        self.cache = {}  # 缓存查询结果
        self.cache_ttl = 3600  # 缓存有效期1小时
        self.last_update = {}  # 记录每个交易对的最后更新时间
        
    async def detect_min_requirements(self, symbol: str, exchange: str, market_type: str = "spot") -> Dict:
        """
        检测指定交易对的最小金额要求
        
        Args:
            symbol: 交易对，如 "BTC-USDT"
            exchange: 交易所名称 ("okx", "gate", "bybit")
            market_type: 市场类型 ("spot", "futures")
            
        Returns:
            Dict: {
                "min_notional": float,  # 最小名义金额(USDT)
                "min_qty": float,       # 最小数量
                "min_qty_increment": float,  # 数量精度
                "price_precision": int,      # 价格精度
                "last_update": int,         # 最后更新时间戳
                "source": str              # 数据来源
            }
        """
        cache_key = f"{exchange}_{symbol}_{market_type}"
        
        # 检查缓存
        if cache_key in self.cache:
            cache_data = self.cache[cache_key]
            if time.time() - cache_data.get("last_update", 0) < self.cache_ttl:
                self.logger.debug(f"使用缓存数据: {cache_key}")
                return cache_data
        
        try:
            if exchange.lower() == "okx":
                result = await self._detect_okx_requirements(symbol, market_type)
            elif exchange.lower() == "gate":
                result = await self._detect_gate_requirements(symbol, market_type)
            elif exchange.lower() == "bybit":
                result = await self._detect_bybit_requirements(symbol, market_type)
            else:
                raise ValueError(f"不支持的交易所: {exchange}")
            
            # 更新缓存
            result["last_update"] = int(time.time())
            result["source"] = f"{exchange}_api"
            self.cache[cache_key] = result
            
            self.logger.info(f"更新{exchange} {symbol} {market_type}最小要求: 最小金额=${result['min_notional']:.2f}")
            return result
            
        except Exception as e:
            self.logger.error(f"检测{exchange} {symbol} {market_type}最小要求失败: {e}")
            
            # 🔥 删除重复配置读取：使用配置管理器
            try:
                from config.settings import get_config
                config = get_config()
                default_min_usd = config.MIN_ORDER_AMOUNT_USD
            except:
                default_min_usd = 90.0  # 应急默认值

            return {
                "min_notional": default_min_usd,
                "min_qty": 0.001,
                "min_qty_increment": 0.000001,
                "price_precision": 6,
                "last_update": int(time.time()),
                "source": "default_fallback"
            }
    
    async def _detect_okx_requirements(self, symbol: str, market_type: str) -> Dict:
        """检测OKX交易对最小要求"""
        from exchanges.okx_exchange import OKXExchange
        
        # 创建临时OKX客户端
        okx = OKXExchange(
            api_key=self.config.get('OKX_API_KEY'),
            api_secret=self.config.get('OKX_API_SECRET'),
            passphrase=self.config.get('OKX_API_PASSPHRASE'),
            testnet=self.config.get('OKX_TESTNET', 'false').lower() == 'true'
        )
        
        try:
            # 🔥 修复：使用正确的OKX格式转换
            okx_symbol = get_exchange_symbol(symbol, "okx", market_type)
            
            # 获取交易规范
            if market_type == "spot":
                inst_type = "SPOT"
                inst_id = okx_symbol  # 使用转换后的格式
            else:
                inst_type = "SWAP"
                inst_id = okx_symbol  # 使用转换后的格式
            
            # 调用OKX API获取交易规范
            response = await okx._request("GET", "/api/v5/public/instruments", {
                "instType": inst_type,
                "instId": inst_id
            })
            
            if not response or len(response) == 0:
                raise Exception(f"OKX未找到交易对: {inst_id}")
            
            instrument = response[0]
            
            # 解析最小交易要求
            min_size = float(instrument.get("minSz", "0.001"))
            tick_size = float(instrument.get("tickSz", "0.01"))
            lot_size = float(instrument.get("lotSz", "0.001"))
            
            # 获取当前价格估算最小金额
            ticker_data = await okx.get_ticker(symbol, market_type)
            current_price = float(ticker_data.get("last", 100))  # 默认100 USDT
            
            min_notional = min_size * current_price
            
            # OKX特殊规则：某些币种有额外的最小金额要求
            special_min_notional = self._get_okx_special_requirements(symbol)
            if special_min_notional:
                min_notional = max(min_notional, special_min_notional)
            
            # 计算价格精度
            price_precision = len(str(tick_size).split('.')[-1]) if '.' in str(tick_size) else 0
            
            return {
                "min_notional": max(min_notional, 5.0),  # OKX一般最小5 USDT
                "min_qty": min_size,
                "min_qty_increment": lot_size,
                "price_precision": price_precision,
                "instrument_info": instrument
            }
            
        except Exception as e:
            self.logger.error(f"OKX API查询失败: {e}")
            raise
    
    async def _detect_gate_requirements(self, symbol: str, market_type: str) -> Dict:
        """检测Gate交易对最小要求"""
        from exchanges.gate_exchange import GateExchange
        
        gate = GateExchange(
            api_key=self.config.get('GATE_API_KEY'),
            api_secret=self.config.get('GATE_API_SECRET'),
            testnet=self.config.get('GATE_TESTNET', 'false').lower() == 'true'
        )
        
        try:
            # 🔥 修复：使用正确的Gate格式转换
            gate_symbol = get_exchange_symbol(symbol, "gate", market_type)
            
            if market_type == "spot":
                # Gate现货API - 使用转换后的格式
                symbol_info = await gate._request("GET", f"/spot/currency_pairs/{gate_symbol}")
            else:
                # Gate期货API - 使用转换后的格式
                settle = "usdt"
                symbol_info = await gate._request("GET", f"/futures/{settle}/contracts/{gate_symbol}")
            
            # 解析Gate的最小交易要求
            if market_type == "spot":
                min_quote_amount = float(symbol_info.get("min_quote_amount", "1.0"))
                min_base_amount = float(symbol_info.get("min_base_amount", "0.001"))
                
                # 获取当前价格
                ticker = await gate.get_ticker(symbol, market_type)
                current_price = float(ticker.get("last", 1))
                
                min_notional = max(min_quote_amount, min_base_amount * current_price)
            else:
                order_size_min = float(symbol_info.get("order_size_min", "1"))
                min_notional = order_size_min  # Gate期货已经是USDT计价
            
            return {
                "min_notional": max(min_notional, 1.0),  # Gate最小1 USDT
                "min_qty": min_base_amount if market_type == "spot" else order_size_min,
                "min_qty_increment": 0.000001,
                "price_precision": 6
            }
            
        except Exception as e:
            self.logger.error(f"Gate API查询失败: {e}")
            raise
    
    async def _detect_bybit_requirements(self, symbol: str, market_type: str) -> Dict:
        """检测Bybit交易对最小要求"""
        from exchanges.bybit_exchange import BybitExchange
        
        bybit = BybitExchange(
            api_key=self.config.get('BYBIT_API_KEY'),
            api_secret=self.config.get('BYBIT_API_SECRET'),
            testnet=self.config.get('BYBIT_TESTNET', 'false').lower() == 'true'
        )
        
        try:
            # 🔥 修复：使用正确的Bybit格式转换
            bybit_symbol = get_exchange_symbol(symbol, "bybit", market_type)
            
            # Bybit统一使用v5 API
            if market_type == "spot":
                category = "spot"
            else:
                category = "linear"  # USDT永续
            
            response = await bybit._request("GET", "/v5/market/instruments-info", {
                "category": category,
                "symbol": bybit_symbol  # 🔥 修复：使用转换后的格式
            })
            
            if not response or not response.get("result", {}).get("list"):
                raise Exception(f"Bybit未找到交易对: {bybit_symbol}")
            
            instrument = response["result"]["list"][0]
            
            min_order_qty = float(instrument.get("minOrderQty", "0.001"))
            min_order_amt = float(instrument.get("minOrderAmt", "1.0"))
            
            # Bybit通常直接提供最小金额
            min_notional = min_order_amt if min_order_amt > 0 else 5.0
            
            return {
                "min_notional": max(min_notional, 5.0),  # Bybit最小5 USDT
                "min_qty": min_order_qty,
                "min_qty_increment": float(instrument.get("qtyStep", "0.000001")),
                "price_precision": len(str(instrument.get("tickSize", "0.01")).split('.')[-1])
            }
            
        except Exception as e:
            self.logger.error(f"Bybit API查询失败: {e}")
            raise
    
    def _get_okx_special_requirements(self, symbol: str) -> Optional[float]:
        """获取OKX特殊币种的额外最小金额要求"""
        special_requirements = {
            "BNB-USDT": 100.0,   # BNB需要更高的最小金额
            "ETH-USDT": 50.0,    # ETH最小50 USDT
            "BTC-USDT": 100.0,   # BTC最小100 USDT
            "SOL-USDT": 30.0,    # SOL最小30 USDT
            "ADA-USDT": 20.0,    # ADA最小20 USDT
        }
        return special_requirements.get(symbol)
    
    async def batch_detect_all_symbols(self) -> Dict[str, Dict]:
        """批量检测.env中配置的所有交易对"""
        symbols_config = self.config.get('TRADING_SYMBOLS', 'BTC-USDT,ETH-USDT')
        symbols = [s.strip() for s in symbols_config.split(',')]
        
        exchanges = ['okx', 'gate', 'bybit']
        market_types = ['spot', 'futures']
        
        results = {}
        
        for symbol in symbols:
            if not symbol:
                continue
                
            results[symbol] = {}
            
            for exchange in exchanges:
                for market_type in market_types:
                    try:
                        key = f"{exchange}_{market_type}"
                        requirements = await self.detect_min_requirements(symbol, exchange, market_type)
                        results[symbol][key] = requirements
                        
                        # 🚀 手动极限优化：移除检测延迟，最大化检测速度
                        # 让API请求自然限流，无需人工延迟
                        
                    except Exception as e:
                        self.logger.error(f"检测{symbol} {exchange} {market_type}失败: {e}")
                        continue
        
        self.logger.info(f"批量检测完成，共处理{len(symbols)}个交易对")
        return results
    
    def get_recommended_min_amount(self, symbol: str, exchange: str, market_type: str = "spot") -> float:
        """获取推荐的最小交易金额"""
        cache_key = f"{exchange}_{symbol}_{market_type}"
        
        if cache_key in self.cache:
            detected_min = self.cache[cache_key].get("min_notional", 50.0)
        else:
            detected_min = 50.0  # 默认值
        
        # 🔥 删除重复配置读取：使用配置管理器
        try:
            from config.settings import get_config
            config = get_config()
            config_min = config.MIN_ORDER_AMOUNT_USD
        except:
            config_min = 50.0  # 应急默认值
        
        # 返回两者的最大值，确保安全
        recommended = max(detected_min, config_min)
        
        self.logger.debug(f"推荐{symbol} {exchange} {market_type}最小金额: ${recommended:.2f}")
        return recommended
    
    async def save_cache_to_file(self, filepath: str = "logs/min_order_cache.json"):
        """保存缓存到文件"""
        try:
            os.makedirs(os.path.dirname(filepath), exist_ok=True)
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(self.cache, f, indent=2, ensure_ascii=False)
            self.logger.info(f"缓存已保存到: {filepath}")
        except Exception as e:
            self.logger.error(f"保存缓存失败: {e}")
    
    async def load_cache_from_file(self, filepath: str = "logs/min_order_cache.json"):
        """从文件加载缓存"""
        try:
            if os.path.exists(filepath):
                with open(filepath, 'r', encoding='utf-8') as f:
                    self.cache = json.load(f)
                self.logger.info(f"缓存已从{filepath}加载")
            else:
                self.logger.info("缓存文件不存在，使用空缓存")
        except Exception as e:
            self.logger.error(f"加载缓存失败: {e}")
            self.cache = {}
    
    async def quick_detect_min_amount(self, symbol: str, exchange: str, market_type: str = "spot") -> float:
        """快速检测最小金额（实例方法）"""
        result = await self.detect_min_requirements(symbol, exchange, market_type)
        return result.get("min_notional", 90.0)


# 全局单例
_detector_instance = None

def get_min_order_detector() -> MinOrderDetector:
    """获取全局单例检测器"""
    global _detector_instance
    if _detector_instance is None:
        _detector_instance = MinOrderDetector()
    return _detector_instance


async def quick_detect_min_amount(symbol: str, exchange: str, market_type: str = "spot") -> float:
    """快速检测最小金额（便捷函数）"""
    detector = get_min_order_detector()
    result = await detector.detect_min_requirements(symbol, exchange, market_type)
    return result.get("min_notional", 90.0)


# 🔥 生产环境：移除测试代码，保持模块纯净