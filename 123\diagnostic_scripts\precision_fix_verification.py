#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 通用系统精度修复验证脚本
验证三个交易所的精度处理修复是否正确
"""

import asyncio
import sys
import os
import logging
from decimal import Decimal

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.trading_rules_preloader import TradingRulesPreloader
from exchanges.bybit_exchange import BybitExchange
from exchanges.gate_exchange import GateExchange
from exchanges.okx_exchange import OKXExchange

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class PrecisionFixVerifier:
    """精度修复验证器"""
    
    def __init__(self):
        self.preloader = TradingRulesPreloader()
        self.test_symbols = [
            "RESOLV-USDT",  # 已知的问题代币
            "BTC-USDT",     # 主流币对比
            "DOGE-USDT",    # 可能的整数精度
            "ETH-USDT"      # 主流币对比
        ]
        
    async def verify_all_exchanges(self):
        """验证所有交易所的精度修复"""
        logger.info("🎯 开始验证通用系统精度修复...")

        # 简化版本：直接测试默认值逻辑
        results = {}

        for exchange_name in ["bybit", "gate", "okx"]:
            logger.info(f"\n🔍 验证 {exchange_name.upper()} 交易所默认值...")
            results[exchange_name] = await self.verify_default_values(exchange_name)

        # 生成验证报告
        self.generate_report(results)
        
    async def verify_exchange(self, exchange, exchange_name):
        """验证单个交易所"""
        results = {}
        
        for symbol in self.test_symbols:
            logger.info(f"\n📊 测试 {exchange_name} - {symbol}")
            
            # 测试现货
            spot_result = await self.test_precision(exchange, symbol, "spot")
            
            # 测试期货
            futures_result = await self.test_precision(exchange, symbol, "futures")
            
            results[symbol] = {
                "spot": spot_result,
                "futures": futures_result
            }
            
        return results

    async def verify_default_values(self, exchange_name):
        """验证交易所的默认值逻辑"""
        results = {}

        for symbol in self.test_symbols:
            logger.info(f"\n📊 测试 {exchange_name} - {symbol} 默认值逻辑")

            # 测试现货默认值
            spot_result = self.test_default_values(exchange_name, symbol, "spot")

            # 测试期货默认值
            futures_result = self.test_default_values(exchange_name, symbol, "futures")

            results[symbol] = {
                "spot": spot_result,
                "futures": futures_result
            }

        return results

    def test_default_values(self, exchange_name, symbol, market_type):
        """测试默认值逻辑"""
        try:
            # 直接调用默认值方法
            default_info = self.preloader._get_exchange_specific_defaults(
                exchange_name, symbol, market_type
            )

            if not default_info:
                return {
                    "status": "NO_DEFAULT",
                    "step_size": None,
                    "source": None,
                    "error": "无默认值"
                }

            step_size = default_info.get("step_size")
            source = default_info.get("source", "unknown")

            # 验证step_size的合理性
            if isinstance(step_size, (int, float)):
                step_size_float = float(step_size)

                # 检查是否是异常的高精度（可能导致API拒绝）
                if step_size_float < 0.0001:
                    status = "SUSPICIOUS_HIGH_PRECISION"
                    warning = f"默认步长过小可能导致API拒绝: {step_size_float}"
                elif step_size_float >= 0.01:
                    status = "CONSERVATIVE_OK"
                    warning = None
                else:
                    status = "MODERATE_PRECISION"
                    warning = None

                return {
                    "status": status,
                    "step_size": step_size_float,
                    "source": source,
                    "warning": warning,
                    "default_info": default_info
                }
            else:
                return {
                    "status": "INVALID_STEP_SIZE",
                    "step_size": step_size,
                    "source": source,
                    "error": f"无效的步长类型: {type(step_size)}"
                }

        except Exception as e:
            return {
                "status": "ERROR",
                "step_size": None,
                "source": None,
                "error": str(e)
            }

    async def test_precision(self, exchange, symbol, market_type):
        """测试单个交易对的精度"""
        try:
            # 获取精度信息
            precision_info = await self.preloader._get_precision_from_exchange_api(
                exchange, symbol, market_type
            )
            
            if not precision_info:
                return {
                    "status": "UNSUPPORTED",
                    "step_size": None,
                    "source": None,
                    "error": "交易对不支持"
                }
                
            step_size = precision_info.get("step_size")
            source = precision_info.get("source", "unknown")
            
            # 验证step_size的合理性
            if isinstance(step_size, (int, float)):
                step_size_float = float(step_size)
                
                # 检查是否是异常的高精度（可能导致API拒绝）
                if step_size_float < 0.0001:
                    status = "SUSPICIOUS_HIGH_PRECISION"
                    warning = f"步长过小可能导致API拒绝: {step_size_float}"
                elif step_size_float >= 0.01:
                    status = "CONSERVATIVE_OK"
                    warning = None
                else:
                    status = "MODERATE_PRECISION"
                    warning = None
                    
                return {
                    "status": status,
                    "step_size": step_size_float,
                    "source": source,
                    "warning": warning,
                    "precision_info": precision_info
                }
            else:
                return {
                    "status": "INVALID_STEP_SIZE",
                    "step_size": step_size,
                    "source": source,
                    "error": f"无效的步长类型: {type(step_size)}"
                }
                
        except Exception as e:
            return {
                "status": "ERROR",
                "step_size": None,
                "source": None,
                "error": str(e)
            }
            
    def generate_report(self, results):
        """生成验证报告"""
        logger.info("\n" + "="*80)
        logger.info("🎯 通用系统精度修复验证报告")
        logger.info("="*80)
        
        total_tests = 0
        suspicious_count = 0
        error_count = 0
        conservative_count = 0
        
        for exchange_name, exchange_results in results.items():
            logger.info(f"\n📊 {exchange_name.upper()} 交易所结果:")
            logger.info("-" * 50)
            
            for symbol, symbol_results in exchange_results.items():
                for market_type, result in symbol_results.items():
                    total_tests += 1
                    status = result.get("status")
                    step_size = result.get("step_size")
                    source = result.get("source")
                    warning = result.get("warning")
                    
                    if status == "SUSPICIOUS_HIGH_PRECISION":
                        suspicious_count += 1
                        logger.warning(f"⚠️  {symbol} {market_type}: {status}")
                        logger.warning(f"    步长: {step_size}, 来源: {source}")
                        if warning:
                            logger.warning(f"    警告: {warning}")
                    elif status == "ERROR":
                        error_count += 1
                        logger.error(f"❌ {symbol} {market_type}: {result.get('error')}")
                    elif status == "CONSERVATIVE_OK":
                        conservative_count += 1
                        logger.info(f"✅ {symbol} {market_type}: 保守精度 (步长: {step_size})")
                    elif status == "UNSUPPORTED":
                        logger.info(f"🚫 {symbol} {market_type}: 不支持")
                    else:
                        logger.info(f"📊 {symbol} {market_type}: {status} (步长: {step_size})")
                        
        # 总结
        logger.info("\n" + "="*80)
        logger.info("📈 验证总结:")
        logger.info(f"总测试数: {total_tests}")
        logger.info(f"保守精度 (推荐): {conservative_count}")
        logger.info(f"可疑高精度 (可能有问题): {suspicious_count}")
        logger.info(f"错误: {error_count}")
        
        success_rate = ((total_tests - error_count - suspicious_count) / total_tests * 100) if total_tests > 0 else 0
        logger.info(f"修复成功率: {success_rate:.1f}%")
        
        if suspicious_count == 0 and error_count == 0:
            logger.info("🎉 所有测试通过！精度修复成功！")
        elif suspicious_count > 0:
            logger.warning(f"⚠️  发现 {suspicious_count} 个可疑的高精度问题，可能需要进一步修复")
        
        logger.info("="*80)

async def main():
    """主函数"""
    verifier = PrecisionFixVerifier()
    await verifier.verify_all_exchanges()

if __name__ == "__main__":
    asyncio.run(main())
