# UserLtvInfo

User's currency statistics data
## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**collateral_currency** | **str** | Collateral | [optional] 
**borrow_currency** | **str** | Borrowed currency | [optional] 
**init_ltv** | **str** | The initial collateralization rate | [optional] 
**alert_ltv** | **str** | Warning collateralization ratio | [optional] 
**liquidate_ltv** | **str** | The liquidation collateralization rate | [optional] 
**min_borrow_amount** | **str** | Minimum borrowable amount for the loan currency | [optional] 
**left_borrowable_amount** | **str** | Remaining borrowable amount for the loan currency | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


