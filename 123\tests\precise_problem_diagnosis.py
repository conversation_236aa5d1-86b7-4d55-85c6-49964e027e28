#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔥 精确问题诊断脚本 - 2025-07-30

针对用户报告的两个核心问题进行精准诊断：
1. 交易规则获取失败：SPK-USDT_gate_spot1 无法获取交易规则
2. 平仓逻辑错误：平仓实际成功但系统显示失败

按照修复提示词要求：
- 精准定位问题根源
- 深度检查实际代码
- 创建精确的诊断脚本
- 一直到找到问题所在
"""

import sys
import os
import asyncio
import time
import json
from typing import Dict, Any, Optional

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class PreciseProblemDiagnosis:
    """精确问题诊断器"""
    
    def __init__(self):
        self.results = {
            "timestamp": time.time(),
            "problems": {},
            "root_causes": {},
            "fix_recommendations": {}
        }
    
    def diagnose_trading_rules_failure(self):
        """诊断交易规则获取失败问题"""
        print("🔍 诊断问题1: 交易规则获取失败")
        
        try:
            # 1. 检查交易规则预加载器
            from core.trading_rules_preloader import get_trading_rules_preloader
            preloader = get_trading_rules_preloader()
            
            print(f"✅ 交易规则预加载器获取成功")
            
            # 2. 检查全局交易所实例
            try:
                from core.trading_system_initializer import get_global_exchanges
                global_exchanges = get_global_exchanges()
                
                if global_exchanges:
                    print(f"✅ 全局交易所实例存在: {list(global_exchanges.keys())}")
                else:
                    print(f"❌ 全局交易所实例为None - 这是根本原因！")
                    self.results["root_causes"]["trading_rules"] = "get_global_exchanges()返回None，无法进行API调用"
                    
            except Exception as e:
                print(f"❌ 获取全局交易所实例失败: {e}")
                self.results["root_causes"]["trading_rules"] = f"无法获取全局交易所实例: {e}"
            
            # 3. 测试临时交易所实例创建
            test_result = preloader._create_temporary_exchange_instance_sync("gate")
            if test_result:
                print(f"✅ 临时Gate.io实例创建成功")
            else:
                print(f"❌ 临时Gate.io实例创建失败 - API密钥问题")
                
        except Exception as e:
            print(f"❌ 交易规则诊断失败: {e}")
            self.results["problems"]["trading_rules"] = str(e)
    
    def diagnose_closing_logic_error(self):
        """诊断平仓逻辑错误问题"""
        print("\n🔍 诊断问题2: 平仓逻辑错误")
        
        # 模拟实际的平仓结果数据
        mock_result = {
            'id': '03fb2b5d-6d3f-453d-8b0a-c2f3db20efa1', 
            'symbol': 'SPK-USDT', 
            'side': 'buy', 
            'amount': 350.0, 
            'price': 0.09858, 
            'status': 'filled',  # 状态是filled，表示成功
            'filled': 350.0, 
            'average': 0.09858, 
            'timestamp': 1753860247068
        }
        
        print(f"📊 模拟平仓结果: {mock_result}")
        
        # 检查unified_closing_manager的判断逻辑
        try:
            from core.unified_closing_manager import UnifiedClosingManager
            
            # 分析判断逻辑
            order_status = mock_result.get("status", "unknown")
            success_statuses = ["filled", "open", "new", "success", "partially_filled"]
            has_order_id = bool(mock_result.get("order_id"))  # 这里是关键！
            
            print(f"🔍 状态检查: status='{order_status}' in {success_statuses} = {order_status in success_statuses}")
            print(f"🔍 订单ID检查: order_id存在 = {has_order_id}")
            print(f"🔍 实际order_id字段: {mock_result.get('order_id', 'None')}")
            print(f"🔍 实际id字段: {mock_result.get('id', 'None')}")
            
            # 发现问题：代码检查order_id，但实际返回的是id字段
            if not has_order_id and mock_result.get("id"):
                print(f"❌ 发现根本原因：代码检查'order_id'字段，但API返回'id'字段！")
                self.results["root_causes"]["closing_logic"] = "字段名不匹配：代码检查order_id，API返回id"
                
        except Exception as e:
            print(f"❌ 平仓逻辑诊断失败: {e}")
            self.results["problems"]["closing_logic"] = str(e)
    
    def generate_fix_recommendations(self):
        """生成修复建议"""
        print("\n🔧 生成修复建议")
        
        if "trading_rules" in self.results["root_causes"]:
            self.results["fix_recommendations"]["trading_rules"] = {
                "问题": "交易规则预加载器缺少全局交易所实例",
                "修复方案": "在get_trading_rule方法中添加临时交易所实例创建和异步加载逻辑",
                "修复文件": "123/core/trading_rules_preloader.py",
                "具体修复": "使用_create_temporary_exchange_instance_sync创建临时实例进行API调用"
            }
        
        if "closing_logic" in self.results["root_causes"]:
            self.results["fix_recommendations"]["closing_logic"] = {
                "问题": "平仓成功判断逻辑中字段名不匹配",
                "修复方案": "修复unified_closing_manager中的字段检查逻辑",
                "修复文件": "123/core/unified_closing_manager.py",
                "具体修复": "检查order_id或id字段，兼容不同API返回格式"
            }
    
    def save_results(self):
        """保存诊断结果"""
        with open("123/diagnostic_results/precise_diagnosis_20250730.json", "w", encoding="utf-8") as f:
            json.dump(self.results, f, ensure_ascii=False, indent=2)
        
        print(f"\n📊 诊断结果已保存到: 123/diagnostic_results/precise_diagnosis_20250730.json")

def main():
    """主函数"""
    print("🚀 开始精确问题诊断 - 2025-07-30")
    
    diagnosis = PreciseProblemDiagnosis()
    
    # 执行诊断
    diagnosis.diagnose_trading_rules_failure()
    diagnosis.diagnose_closing_logic_error()
    diagnosis.generate_fix_recommendations()
    diagnosis.save_results()
    
    print("\n✅ 精确问题诊断完成")

if __name__ == "__main__":
    main()