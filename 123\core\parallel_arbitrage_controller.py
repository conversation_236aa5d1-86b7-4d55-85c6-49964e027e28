#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ParallelArbitrageController - 并行套利控制器
实现最多3个交易对同时执行的并行控制功能，不影响现有结构

核心功能：
1. 并行数量控制 (最多3个同时执行)
2. 活跃套利注册和管理
3. 槽位释放机制
4. 状态监控和清理
5. 零破坏性集成，完全向下兼容
"""

import asyncio
import time
import os
from typing import Dict, Optional, Any
from utils.logger import get_logger


class ParallelArbitrageController:
    """
    并行套利控制器 - 不影响现有结构

    按照07文档设计，实现：
    - 最多3个交易对同时执行
    - 防止重复套利同一交易对
    - 自动清理完成的套利
    - 完全向下兼容现有单对套利逻辑
    """

    def __init__(self, max_concurrent: int = 3):
        """
        初始化并行控制器

        Args:
            max_concurrent: 最大并行数量，默认3个
        """
        self.max_concurrent = max_concurrent
        self.active_arbitrages: Dict[str, Dict[str, Any]] = {}  # {symbol: arbitrage_info}
        self.lock = asyncio.Lock()
        self.logger = get_logger(self.__class__.__name__)

        # 性能统计
        self.stats = {
            'total_requests': 0,
            'accepted_requests': 0,
            'rejected_requests': 0,
            'completed_arbitrages': 0,
            'max_concurrent_reached': 0
        }

        self.logger.info(f"并行套利控制器初始化完成")
        self.logger.info(f"   最大并行数量: {self.max_concurrent}")
        self.logger.info(f"   使用异步锁保证线程安全")

    async def can_execute_new_arbitrage(self, symbol: str) -> bool:
        """
        检查是否可以执行新的套利

        Args:
            symbol: 交易对符号

        Returns:
            bool: 是否可以执行
        """
        async with self.lock:
            self.stats['total_requests'] += 1

            # 1. 清理已完成的套利
            await self._cleanup_completed_arbitrages()

            # 2. 检查并行数量限制
            if len(self.active_arbitrages) >= self.max_concurrent:
                self.stats['rejected_requests'] += 1
                self.stats['max_concurrent_reached'] += 1
                self.logger.info(f"达到最大并行数量{self.max_concurrent}，拒绝新套利: {symbol}")
                self.logger.info(f"   当前活跃: {list(self.active_arbitrages.keys())}")
                return False

            # 3. 检查是否已有相同交易对
            if symbol in self.active_arbitrages:
                self.stats['rejected_requests'] += 1
                self.logger.info(f"交易对{symbol}已在执行中，拒绝重复套利")
                return False

            self.stats['accepted_requests'] += 1
            self.logger.info(f"并行控制检查通过: {symbol}")
            self.logger.info(f"   当前活跃: {len(self.active_arbitrages)}/{self.max_concurrent}")
            return True

    async def register_arbitrage(self, symbol: str, arbitrage_info: Dict[str, Any]) -> bool:
        """
        注册新的活跃套利

        Args:
            symbol: 交易对符号
            arbitrage_info: 套利信息

        Returns:
            bool: 注册是否成功
        """
        async with self.lock:
            if symbol in self.active_arbitrages:
                self.logger.warning(f"套利{symbol}已存在，跳过注册")
                return False

            # 注册套利信息
            self.active_arbitrages[symbol] = {
                'start_time': time.time(),
                'status': 'active',
                'order_id': f"ARB_{symbol}_{int(time.time())}",  # 生成订单编号
                **arbitrage_info
            }

            self.logger.info(f"注册套利: {symbol}")
            self.logger.info(f"   订单编号: {self.active_arbitrages[symbol]['order_id']}")
            self.logger.info(f"   初始差价: {arbitrage_info.get('initial_spread', 0)*100:.3f}%")
            self.logger.info(f"   当前活跃数量: {len(self.active_arbitrages)}")

            return True

    async def complete_arbitrage(self, symbol: str) -> bool:
        """
        完成套利，释放并行槽位

        Args:
            symbol: 交易对符号

        Returns:
            bool: 释放是否成功
        """
        async with self.lock:
            if symbol not in self.active_arbitrages:
                self.logger.warning(f"套利{symbol}不存在，无法释放")
                return False

            # 记录完成信息
            arbitrage_info = self.active_arbitrages[symbol]
            duration = time.time() - arbitrage_info['start_time']
            order_id = arbitrage_info.get('order_id', 'UNKNOWN')

            # 释放槽位
            del self.active_arbitrages[symbol]
            self.stats['completed_arbitrages'] += 1

            self.logger.info(f"完成套利: {symbol}")
            self.logger.info(f"   订单编号: {order_id}")
            self.logger.info(f"   持续时间: {duration:.1f}秒")
            self.logger.info(f"   释放槽位，当前活跃: {len(self.active_arbitrages)}")

            return True

    async def _cleanup_completed_arbitrages(self):
        """
        清理已完成或超时的套利
        """
        current_time = time.time()
        timeout_threshold = 300  # 5分钟超时

        to_remove = []
        for symbol, info in self.active_arbitrages.items():
            # 检查超时
            if current_time - info['start_time'] > timeout_threshold:
                to_remove.append(symbol)
                self.logger.warning(f"套利{symbol}超时，自动清理")
                self.logger.warning(f"   订单编号: {info.get('order_id', 'UNKNOWN')}")
                self.logger.warning(f"   持续时间: {current_time - info['start_time']:.1f}秒")

        # 移除超时的套利
        for symbol in to_remove:
            del self.active_arbitrages[symbol]

    def get_active_arbitrages(self) -> Dict[str, Dict[str, Any]]:
        """
        获取当前活跃的套利列表

        Returns:
            Dict: 活跃套利信息
        """
        return self.active_arbitrages.copy()

    def get_stats(self) -> Dict[str, Any]:
        """
        获取性能统计信息

        Returns:
            Dict: 统计信息
        """
        return {
            **self.stats,
            'current_active': len(self.active_arbitrages),
            'acceptance_rate': (
                self.stats['accepted_requests'] / max(self.stats['total_requests'], 1) * 100
            )
        }

    def is_symbol_active(self, symbol: str) -> bool:
        """
        检查指定交易对是否正在执行套利

        Args:
            symbol: 交易对符号

        Returns:
            bool: 是否活跃
        """
        return symbol in self.active_arbitrages

    def get_order_id(self, symbol: str) -> Optional[str]:
        """
        获取指定交易对的订单编号

        Args:
            symbol: 交易对符号

        Returns:
            Optional[str]: 订单编号，如果不存在返回None
        """
        if symbol in self.active_arbitrages:
            return self.active_arbitrages[symbol].get('order_id')
        return None

    async def force_cleanup_all(self) -> int:
        """
        🔥 强制清理所有活跃套利 - 紧急恢复功能

        Returns:
            int: 清理的套利数量
        """
        async with self.lock:
            count = len(self.active_arbitrages)
            if count > 0:
                self.logger.warning(f"🚨 强制清理所有活跃套利: {count}个")
                for symbol, info in self.active_arbitrages.items():
                    self.logger.warning(f"   清理: {symbol} (订单: {info.get('order_id', 'UNKNOWN')})")

                self.active_arbitrages.clear()
                self.logger.info(f"✅ 强制清理完成，释放{count}个槽位")

            return count

    async def force_cleanup_symbol(self, symbol: str) -> bool:
        """
        🔥 强制清理指定交易对 - 紧急恢复功能

        Args:
            symbol: 交易对符号

        Returns:
            bool: 是否成功清理
        """
        async with self.lock:
            if symbol in self.active_arbitrages:
                info = self.active_arbitrages[symbol]
                order_id = info.get('order_id', 'UNKNOWN')
                duration = time.time() - info['start_time']

                del self.active_arbitrages[symbol]

                self.logger.warning(f"🚨 强制清理套利: {symbol}")
                self.logger.warning(f"   订单编号: {order_id}")
                self.logger.warning(f"   持续时间: {duration:.1f}秒")
                self.logger.info(f"✅ 强制清理完成，槽位已释放")

                return True

            return False