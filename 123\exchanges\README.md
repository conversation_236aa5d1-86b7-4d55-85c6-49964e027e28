# 交易所模块

## 🔥 **系统核心定位**

### **⚠️ 重要说明：期货溢价专业套利系统**

**本系统是通用的专注期货溢价执行套利，现货溢价平仓的套利系统，支持任何代币！**

#### **🎯 核心套利逻辑**
```
期货溢价阈值达到 → 开仓锁定差价 → 等待趋同 → 现货溢价阈值达到 → 平仓获利
```

#### **📊 价差符号含义说明**
- **+0.25%** = 期货溢价差价0.25%（期货 > 现货）→ 开仓信号，买现货卖期货
- **-0.3%** = 现货溢价差价0.3%（现货 > 期货）→ 平仓信号，卖现货买期货
- **+/-符号** = 表示差价类型，不是正负值概念
- **所有价差实时记录** → 不受阈值限制
- **阈值控制执行** → 决定是否开仓/平仓
- **总利润** = |期货溢价差价| + |现货溢价差价| = 0.25% + 0.3% = 0.55%

---

## 修复说明

本次修复主要解决了以下问题：

### 1. 参数适配器 (ExchangeParamAdapter)

- 重新设计并实现了交易所参数适配器，统一处理不同交易所的参数差异
- 增加了市价单特殊处理，特别是买单使用交易额而不是数量
- 实现了价格精度控制，避免价格小数位数过多导致的错误
- 增加了期货合约交易量转换逻辑，确保符合交易所规则

### 2. Gate.io 交易所修复

- 修复了期货下单时的合约张数计算问题
- 修正了API路径和参数格式问题
- 添加了市价单所需的time_in_force参数
- 统一使用交易所参数适配器处理参数

### 3. Bybit 交易所修复

- 修复了时间同步和签名生成逻辑
- 修正了下单参数大小写问题（要求BUY/SELL而非buy/sell）
- 修正了路径拼接问题，确保查询参数正确附加到URL
- 完善了响应处理和错误处理逻辑

### 4. OKX 交易所修复

- 添加了交易对信息查询功能，用于获取最小交易量和批量单位
- 修复了期货交易量必须是最小单位整数倍的问题
- 修正了市价单参数处理，区分现货和期货的不同要求
- 优化了错误处理和日志记录

## 已解决问题

- 基础连接和认证问题
- 市场数据获取问题（ticker, orderbook）
- 账户余额查询问题
- 资金划转问题（Gate.io）
- OKX期货限价单下单问题

## 待解决问题

1. **Gate.io 期货下单问题**:
   - 现有错误: `Gate.io API错误: {'refu': 0, 'tkfr': '0.0005', ...}`
   - 建议: 进一步调整期货下单参数，特别是size字段的计算方式

2. **Bybit 期货下单问题**:
   - 现有错误: `Bybit API错误: 10001: Side invalid`
   - 建议: 修正side参数大小写和格式，可能需要根据最新API规范调整

3. **OKX 期货市价单问题**:
   - 现有错误: `OKX API错误: The instrument corresponding to this BTC-USDT-SWAP does not support the tgtCcy parameter.`
   - 建议: 对期货市价单移除tgtCcy参数，已在参数适配器中实现但需要测试确认

4. **最小交易量问题**:
   - 部分交易对的最小交易量可能需要获取交易对配置来确定
   - 建议: 实现交易对配置缓存机制，避免频繁查询

## 测试覆盖情况

- 连接和认证: ✅
- 余额查询: ✅
- 行情获取: ✅
- 订单簿获取: ✅
- 限价单下单: ⚠️ 部分交易所成功
- 市价单下单: ❌ 需要进一步改进
- 持仓查询: ✅
- 资金划转: ✅

## 后续优化建议

1. 实现交易对信息缓存机制，避免频繁查询
2. 增加重试机制，特别是对于市场状态依赖的操作
3. 完善错误处理，提供更详细的错误分类和自动恢复
4. 考虑添加模拟交易模式，用于全面测试而不实际下单
5. 为所有API调用添加详细日志，便于问题排查 