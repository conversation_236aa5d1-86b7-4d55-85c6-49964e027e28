"""
仓位监控模块
实时监控所有交易所的现货和期货仓位
"""

# 修复导入路径
import sys
from pathlib import Path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import asyncio
import time
import json
from typing import Dict, List, Optional, Tuple, Any, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import threading

from config.debug_config import DEBUG
from utils.logger import get_logger

logger = get_logger(__name__)

class PositionType(Enum):
    SPOT = "spot"
    FUTURES = "futures"

class PositionStatus(Enum):
    NORMAL = "normal"
    IMBALANCED = "imbalanced"
    MISSING = "missing"
    EXCESS = "excess"
    ERROR = "error"

@dataclass
class PositionInfo:
    """仓位信息"""
    exchange: str
    symbol: str
    position_type: PositionType
    side: str  # buy/sell, long/short
    quantity: float
    value: float  # USDT价值
    average_price: float
    unrealized_pnl: float
    fee_paid: float
    timestamp: int
    status: PositionStatus = PositionStatus.NORMAL

    def to_dict(self) -> Dict:
        return {
            "exchange": self.exchange,
            "symbol": self.symbol,
            "position_type": self.position_type.value,
            "side": self.side,
            "quantity": self.quantity,
            "value": self.value,
            "average_price": self.average_price,
            "unrealized_pnl": self.unrealized_pnl,
            "fee_paid": self.fee_paid,
            "timestamp": self.timestamp,
            "status": self.status.value
        }

@dataclass
class HedgePositionPair:
    """对冲仓位对"""
    spot_position: Optional[PositionInfo]
    futures_position: Optional[PositionInfo]
    symbol_pair: str  # "BTCUSDT"
    hedge_ratio: float = 1.0  # 对冲比例
    quantity_diff: float = 0.0
    value_diff: float = 0.0
    is_balanced: bool = False
    last_check_time: float = field(default_factory=time.time)

    def calculate_imbalance(self):
        """计算仓位不平衡度"""
        if not self.spot_position or not self.futures_position:
            self.is_balanced = False
            return

        # 计算数量差异
        spot_qty = abs(self.spot_position.quantity)
        futures_qty = abs(self.futures_position.quantity)
        self.quantity_diff = abs(spot_qty - futures_qty)

        # 计算价值差异
        spot_value = abs(self.spot_position.value)
        futures_value = abs(self.futures_position.value)
        self.value_diff = abs(spot_value - futures_value)

        # 判断是否平衡（容忍度）
        quantity_tolerance = max(spot_qty * 0.00001, 0.0001)  # 0.001%或最小0.0001
        value_tolerance = 0.01  # 0.01 USDT

        quantity_balanced = self.quantity_diff <= quantity_tolerance
        value_balanced = self.value_diff <= value_tolerance

        self.is_balanced = quantity_balanced and value_balanced
        self.last_check_time = time.time()

class PositionMonitor:
    """仓位监控器"""

    def __init__(self, exchanges: Dict[str, Any], opportunity_scanner=None):
        self.exchanges = exchanges  # {"gate": exchange_client, "bybit": exchange_client}
        self.opportunity_scanner = opportunity_scanner  # 🔥 新增：OpportunityScanner依赖注入
        self.logger = get_logger("PositionMonitor")

        # 仓位数据存储
        self.spot_positions: Dict[str, Dict[str, PositionInfo]] = {}  # {exchange: {symbol: position}}
        self.futures_positions: Dict[str, Dict[str, PositionInfo]] = {}
        self.hedge_pairs: Dict[str, HedgePositionPair] = {}  # {symbol: hedge_pair}

        # 监控参数
        self.monitor_interval = 5  # 5秒检查一次
        self.position_tolerance = 0.00001  # 0.001%
        self.value_tolerance = 0.01  # 0.01 USDT
        self.alert_threshold = 0.001  # 0.1%不平衡阈值

        # 监控状态
        self.is_running = False
        self.monitor_thread = None
        self.last_update_time = {}

        # 回调函数
        self.position_change_callbacks: List[Callable] = []
        self.imbalance_alert_callbacks: List[Callable] = []

        # 统计信息
        self.total_checks = 0
        self.imbalance_alerts = 0
        self.position_errors = 0

        if DEBUG:
            self.logger.info("PositionMonitor initialized")

    async def initialize(self):
        """初始化仓位监控器"""
        try:
            self.logger.info("初始化仓位监控器...")

            # 执行初始仓位检查
            await self.update_all_positions()

            self.logger.info("✅ 仓位监控器初始化完成")

        except Exception as e:
            self.logger.error(f"❌ 仓位监控器初始化失败: {e}")
            raise

    def add_position_change_callback(self, callback: Callable):
        """添加仓位变化回调"""
        self.position_change_callbacks.append(callback)

    def add_imbalance_alert_callback(self, callback: Callable):
        """添加不平衡告警回调"""
        self.imbalance_alert_callbacks.append(callback)

    async def fetch_spot_positions(self, exchange_name: str) -> Dict[str, PositionInfo]:
        """
        获取现货仓位

        Args:
            exchange_name: 交易所名称

        Returns:
            Dict[str, PositionInfo]: 现货仓位信息
        """
        try:
            exchange = self.exchanges.get(exchange_name)
            if not exchange:
                self.logger.error(f"Exchange {exchange_name} not found")
                return {}

            # 🔥 修复：统一余额获取接口，支持所有交易所
            balance_data = None
            if hasattr(exchange, 'get_spot_balance'):
                balance_data = await exchange.get_spot_balance()
            elif hasattr(exchange, 'get_balance'):
                # 🔥 关键修复：统一传递account_type参数，确保三交易所一致性
                from exchanges.exchanges_base import AccountType
                try:
                    # 优先获取现货余额
                    raw_balance = await exchange.get_balance(AccountType.SPOT)
                    self.logger.debug(f"✅ {exchange_name} 使用SPOT账户类型获取余额")
                except Exception as e:
                    # 降级处理：对于统一账户交易所（如Bybit），使用UNIFIED
                    try:
                        raw_balance = await exchange.get_balance(AccountType.UNIFIED)
                        self.logger.debug(f"✅ {exchange_name} 降级使用UNIFIED账户类型获取余额")
                    except Exception as e2:
                        self.logger.error(f"❌ {exchange_name} 所有账户类型都失败: SPOT={e}, UNIFIED={e2}")
                        return {}

                # 转换为兼容格式：{"BTC": {"available": 1.0, "locked": 0.0}} -> {"BTC": 1.0}
                balance_data = {}
                for currency, info in raw_balance.items():
                    if isinstance(info, dict):
                        available = info.get("available", 0.0)
                        if available > 0.0001:  # 只包含有余额的币种
                            balance_data[currency] = available
                    elif isinstance(info, (int, float)):
                        if info > 0.0001:
                            balance_data[currency] = float(info)
                self.logger.debug(f"✅ {exchange_name} 统一账户余额转换完成: {len(balance_data)}个币种")
            else:
                self.logger.warning(f"Exchange {exchange_name} doesn't support balance APIs")
                return {}
            if not balance_data:
                return {}

            positions = {}
            current_time = int(time.time() * 1000)

            # 适配实际返回格式：Dict[str, float] (币种 -> 余额)
            for asset, total_balance in balance_data.items():
                if total_balance > 0.0001:  # 忽略极小余额
                    # 获取当前价格来计算价值
                    if asset != 'USDT':
                        symbol = f"{asset}USDT"
                        price = await self._get_current_price(exchange_name, symbol, 'spot')
                        value = total_balance * price
                    else:
                        price = 1.0
                        value = total_balance

                    position = PositionInfo(
                        exchange=exchange_name,
                        symbol=asset,
                        position_type=PositionType.SPOT,
                        side="buy",
                        quantity=total_balance,
                        value=value,
                        average_price=price,
                        unrealized_pnl=0,  # 现货无未实现盈亏
                        fee_paid=0,
                        timestamp=current_time,
                        status=PositionStatus.NORMAL
                    )
                    positions[asset] = position

            return positions

        except Exception as e:
            self.logger.error(f"Error fetching spot positions from {exchange_name}: {e}")
            self.position_errors += 1
            return {}

    async def fetch_futures_positions(self, exchange_name: str) -> Dict[str, PositionInfo]:
        """
        获取期货仓位

        Args:
            exchange_name: 交易所名称

        Returns:
            Dict[str, PositionInfo]: 期货仓位信息
        """
        try:
            exchange = self.exchanges.get(exchange_name)
            if not exchange:
                self.logger.error(f"Exchange {exchange_name} not found")
                return {}

            # 🔥 修复：统一期货仓位获取接口，支持所有交易所
            positions_data = None
            if hasattr(exchange, 'get_futures_positions'):
                positions_data = await exchange.get_futures_positions()
            elif hasattr(exchange, 'get_position'):
                # 🔥 关键修复：OKX等交易所使用get_position
                positions_data = await exchange.get_position()
                self.logger.debug(f"✅ {exchange_name} 使用get_position获取期货仓位")
            else:
                self.logger.warning(f"Exchange {exchange_name} doesn't support position APIs")
                return {}
            if not positions_data:
                return {}

            positions = {}
            current_time = int(time.time() * 1000)

            for pos_info in positions_data:
                symbol = pos_info.get('symbol', '')
                size = float(pos_info.get('size', 0))

                if abs(size) > 0.0001:  # 忽略极小仓位
                    side = pos_info.get('side', 'long')
                    entry_price = float(pos_info.get('entry_price', 0))
                    mark_price = float(pos_info.get('mark_price', entry_price))
                    unrealized_pnl = float(pos_info.get('unrealized_pnl', 0))

                    value = abs(size) * mark_price

                    position = PositionInfo(
                        exchange=exchange_name,
                        symbol=symbol,
                        position_type=PositionType.FUTURES,
                        side=side,
                        quantity=abs(size),
                        value=value,
                        average_price=entry_price,
                        unrealized_pnl=unrealized_pnl,
                        fee_paid=0,  # 需要从订单历史计算
                        timestamp=current_time,
                        status=PositionStatus.NORMAL
                    )
                    positions[symbol] = position

            return positions

        except Exception as e:
            self.logger.error(f"Error fetching futures positions from {exchange_name}: {e}")
            self.position_errors += 1
            return {}

    async def _get_current_price(self, exchange_name: str, symbol: str,
                               position_type: str) -> float:
        """
        获取当前价格 - 🔥 修复：使用OpportunityScanner.market_data作为唯一价格数据源

        Args:
            exchange_name: 交易所名称
            symbol: 交易对
            position_type: 仓位类型

        Returns:
            float: 当前价格
        """
        try:
            # 🔥 修复：使用依赖注入的OpportunityScanner实例
            if not self.opportunity_scanner:
                if DEBUG:
                    self.logger.debug(f"OpportunityScanner未注入，无法获取价格: {exchange_name}_{position_type}_{symbol}")
                return 0.0

            # 标准化交易所名称和数据key
            normalized_exchange = exchange_name.lower().replace("exchange", "")
            data_key = f"{normalized_exchange}_{position_type}_{symbol}"

            # 🔥 从OpportunityScanner.market_data获取价格
            try:
                # 检查market_data中是否有对应的价格数据
                if hasattr(self.opportunity_scanner, 'market_data'):
                    market_data = self.opportunity_scanner.market_data

                    # 尝试多种可能的key格式
                    possible_keys = [
                        data_key,  # gate_spot_BTC-USDT
                        f"{normalized_exchange}_{symbol}_{position_type}",  # gate_BTC-USDT_spot
                        f"{normalized_exchange}_{symbol}",  # gate_BTC-USDT
                    ]

                    for key in possible_keys:
                        if key in market_data:
                            price_data = market_data[key]
                            if hasattr(price_data, 'price') and price_data.price > 0:
                                if DEBUG:
                                    self.logger.debug(f"✅ 从OpportunityScanner获取价格: {key} = {price_data.price}")
                                return float(price_data.price)
                            elif isinstance(price_data, dict) and 'price' in price_data:
                                price = price_data['price']
                                if price > 0:
                                    if DEBUG:
                                        self.logger.debug(f"✅ 从OpportunityScanner获取价格: {key} = {price}")
                                    return float(price)

                if DEBUG:
                    self.logger.debug(f"⚠️ OpportunityScanner.market_data中未找到价格: {data_key}")
                return 0.0

            except Exception as e:
                if DEBUG:
                    self.logger.debug(f"从OpportunityScanner获取价格失败: {e}")
                return 0.0

        except Exception as e:
            if DEBUG:
                self.logger.debug(f"获取价格异常 {symbol}: {e}")
            return 0.0

    async def update_all_positions(self):
        """更新所有仓位信息"""
        try:
            # 并发获取所有交易所的仓位
            tasks = []

            for exchange_name in self.exchanges.keys():
                # 现货仓位
                spot_task = self.fetch_spot_positions(exchange_name)
                tasks.append(('spot', exchange_name, spot_task))

                # 期货仓位
                futures_task = self.fetch_futures_positions(exchange_name)
                tasks.append(('futures', exchange_name, futures_task))

            # 执行所有任务
            results = await asyncio.gather(*[task[2] for task in tasks], return_exceptions=True)

            # 处理结果
            for i, result in enumerate(results):
                position_type, exchange_name, _ = tasks[i]

                if isinstance(result, Exception):
                    self.logger.error(f"Error updating {position_type} positions for {exchange_name}: {result}")
                    continue

                if position_type == 'spot':
                    old_positions = self.spot_positions.get(exchange_name, {})
                    self.spot_positions[exchange_name] = result
                    self.last_update_time[f"{exchange_name}_spot"] = time.time()

                    # 检查变化
                    await self._check_position_changes('spot', exchange_name, old_positions, result)

                elif position_type == 'futures':
                    old_positions = self.futures_positions.get(exchange_name, {})
                    self.futures_positions[exchange_name] = result
                    self.last_update_time[f"{exchange_name}_futures"] = time.time()

                    # 检查变化
                    await self._check_position_changes('futures', exchange_name, old_positions, result)

            # 更新对冲仓位对
            await self._update_hedge_pairs()

            self.total_checks += 1

            if DEBUG:
                self.logger.debug(f"Updated all positions (check #{self.total_checks})")

        except Exception as e:
            self.logger.error(f"Error updating all positions: {e}")
            self.position_errors += 1

    async def _check_position_changes(self, position_type: str, exchange_name: str,
                                    old_positions: Dict, new_positions: Dict):
        """检查仓位变化"""
        try:
            # 检查新增仓位
            for symbol, position in new_positions.items():
                if symbol not in old_positions:
                    await self._notify_position_change('new', position_type, exchange_name, symbol, position)
                elif old_positions[symbol].quantity != position.quantity:
                    await self._notify_position_change('changed', position_type, exchange_name, symbol, position)

            # 检查消失的仓位
            for symbol in old_positions:
                if symbol not in new_positions:
                    await self._notify_position_change('closed', position_type, exchange_name, symbol, None)

        except Exception as e:
            self.logger.error(f"Error checking position changes: {e}")

    async def _notify_position_change(self, change_type: str, position_type: str,
                                    exchange_name: str, symbol: str, position: Optional[PositionInfo]):
        """通知仓位变化"""
        try:
            change_info = {
                'type': change_type,
                'position_type': position_type,
                'exchange': exchange_name,
                'symbol': symbol,
                'position': position.to_dict() if position else None,
                'timestamp': time.time()
            }

            for callback in self.position_change_callbacks:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(change_info)
                    else:
                        callback(change_info)
                except Exception as e:
                    self.logger.error(f"Error in position change callback: {e}")

        except Exception as e:
            self.logger.error(f"Error notifying position change: {e}")

    async def _update_hedge_pairs(self):
        """更新对冲仓位对"""
        try:
            # 获取所有交易对
            all_symbols = set()

            # 从现货仓位中提取交易对
            for exchange_positions in self.spot_positions.values():
                for symbol in exchange_positions.keys():
                    if symbol != 'USDT':  # 排除USDT
                        all_symbols.add(f"{symbol}USDT")

            # 从期货仓位中提取交易对
            for exchange_positions in self.futures_positions.values():
                for symbol in exchange_positions.keys():
                    all_symbols.add(symbol)

            # 更新每个交易对的对冲状态
            for symbol in all_symbols:
                await self._update_single_hedge_pair(symbol)

        except Exception as e:
            self.logger.error(f"Error updating hedge pairs: {e}")

    async def _update_single_hedge_pair(self, symbol: str):
        """更新单个对冲仓位对"""
        try:
            # 收集所有相关仓位
            spot_positions = []
            futures_positions = []

            # 查找现货仓位
            base_asset = symbol.replace('USDT', '')
            for exchange_name, positions in self.spot_positions.items():
                if base_asset in positions:
                    spot_positions.append(positions[base_asset])

            # 查找期货仓位
            for exchange_name, positions in self.futures_positions.items():
                if symbol in positions:
                    futures_positions.append(positions[symbol])

            # 计算总仓位
            total_spot_qty = sum(pos.quantity for pos in spot_positions)
            total_futures_qty = sum(pos.quantity for pos in futures_positions)

            total_spot_value = sum(pos.value for pos in spot_positions)
            total_futures_value = sum(pos.value for pos in futures_positions)

            # 创建或更新对冲仓位对
            if total_spot_qty > 0.0001 or total_futures_qty > 0.0001:
                # 合并现货仓位信息
                spot_position = PositionInfo(
                    exchange="combined",
                    symbol=base_asset,
                    position_type=PositionType.SPOT,
                    side="buy",
                    quantity=total_spot_qty,
                    value=total_spot_value,
                    average_price=total_spot_value / total_spot_qty if total_spot_qty > 0 else 0,
                    unrealized_pnl=0,
                    fee_paid=0,
                    timestamp=int(time.time() * 1000)
                ) if total_spot_qty > 0.0001 else None

                # 合并期货仓位信息
                futures_position = PositionInfo(
                    exchange="combined",
                    symbol=symbol,
                    position_type=PositionType.FUTURES,
                    side="combined",
                    quantity=total_futures_qty,
                    value=total_futures_value,
                    average_price=total_futures_value / total_futures_qty if total_futures_qty > 0 else 0,
                    unrealized_pnl=sum(pos.unrealized_pnl for pos in futures_positions),
                    fee_paid=0,
                    timestamp=int(time.time() * 1000)
                ) if total_futures_qty > 0.0001 else None

                # 创建或更新对冲仓位对
                hedge_pair = HedgePositionPair(
                    spot_position=spot_position,
                    futures_position=futures_position,
                    symbol_pair=symbol
                )

                # 计算不平衡度
                hedge_pair.calculate_imbalance()

                # 检查是否需要告警
                if not hedge_pair.is_balanced and (spot_position or futures_position):
                    await self._check_imbalance_alert(hedge_pair)

                self.hedge_pairs[symbol] = hedge_pair
            else:
                # 移除空仓位对
                if symbol in self.hedge_pairs:
                    del self.hedge_pairs[symbol]

        except Exception as e:
            self.logger.error(f"Error updating hedge pair for {symbol}: {e}")

    async def _check_imbalance_alert(self, hedge_pair: HedgePositionPair):
        """检查不平衡告警"""
        try:
            # 计算不平衡百分比
            if hedge_pair.spot_position and hedge_pair.futures_position:
                total_value = (hedge_pair.spot_position.value + hedge_pair.futures_position.value) / 2
                if total_value > 0:
                    imbalance_pct = hedge_pair.value_diff / total_value

                    if imbalance_pct > self.alert_threshold:
                        alert_info = {
                            'symbol': hedge_pair.symbol_pair,
                            'imbalance_percentage': imbalance_pct * 100,
                            'quantity_diff': hedge_pair.quantity_diff,
                            'value_diff': hedge_pair.value_diff,
                            'spot_position': hedge_pair.spot_position.to_dict() if hedge_pair.spot_position else None,
                            'futures_position': hedge_pair.futures_position.to_dict() if hedge_pair.futures_position else None,
                            'timestamp': time.time()
                        }

                        self.imbalance_alerts += 1

                        for callback in self.imbalance_alert_callbacks:
                            try:
                                if asyncio.iscoroutinefunction(callback):
                                    await callback(alert_info)
                                else:
                                    callback(alert_info)
                            except Exception as e:
                                self.logger.error(f"Error in imbalance alert callback: {e}")

        except Exception as e:
            self.logger.error(f"Error checking imbalance alert: {e}")

    def start_monitoring(self):
        """启动仓位监控"""
        if self.is_running:
            self.logger.warning("Position monitoring is already running")
            return

        self.is_running = True
        self.monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitor_thread.start()

        self.logger.info("Position monitoring started")

    def stop_monitoring(self):
        """停止仓位监控"""
        self.is_running = False

        if self.monitor_thread:
            self.monitor_thread.join(timeout=10)

        self.logger.info("Position monitoring stopped")

    def _monitoring_loop(self):
        """监控循环"""
        asyncio.set_event_loop(asyncio.new_event_loop())
        loop = asyncio.get_event_loop()

        while self.is_running:
            try:
                loop.run_until_complete(self.update_all_positions())
                time.sleep(self.monitor_interval)
            except Exception as e:
                self.logger.error(f"Error in monitoring loop: {e}")
                time.sleep(self.monitor_interval)

        loop.close()

    def get_all_positions(self) -> Dict:
        """获取所有仓位信息"""
        # 计算汇总信息
        total_spot_value = 0.0
        total_futures_value = 0.0
        total_positions = 0
        imbalanced_count = 0

        # 统计现货仓位
        for exchange, positions in self.spot_positions.items():
            for symbol, position in positions.items():
                total_spot_value += position.value
                total_positions += 1

        # 统计期货仓位
        for exchange, positions in self.futures_positions.items():
            for symbol, position in positions.items():
                total_futures_value += position.value
                total_positions += 1

        # 统计不平衡仓位
        for symbol, pair in self.hedge_pairs.items():
            if not pair.is_balanced:
                imbalanced_count += 1

        return {
            'spot_positions': {
                exchange: {symbol: pos.to_dict() for symbol, pos in positions.items()}
                for exchange, positions in self.spot_positions.items()
            },
            'futures_positions': {
                exchange: {symbol: pos.to_dict() for symbol, pos in positions.items()}
                for exchange, positions in self.futures_positions.items()
            },
            'hedge_pairs': {
                symbol: {
                    'spot_position': pair.spot_position.to_dict() if pair.spot_position else None,
                    'futures_position': pair.futures_position.to_dict() if pair.futures_position else None,
                    'quantity_diff': pair.quantity_diff,
                    'value_diff': pair.value_diff,
                    'is_balanced': pair.is_balanced,
                    'last_check_time': pair.last_check_time
                }
                for symbol, pair in self.hedge_pairs.items()
            },
            'summary': {
                'total_spot_value': total_spot_value,
                'total_futures_value': total_futures_value,
                'total_positions': total_positions,
                'total_hedge_pairs': len(self.hedge_pairs),
                'imbalanced_pairs': imbalanced_count,
                'exchange_count': len(self.exchanges),
                'balance_ratio': (total_spot_value / total_futures_value) if total_futures_value > 0 else 0,
                'last_update': max(self.last_update_time.values()) if self.last_update_time else 0,
                'is_monitoring': self.is_running
            }
        }

    def get_imbalanced_positions(self) -> List[Dict]:
        """获取不平衡的仓位对"""
        imbalanced = []

        for symbol, hedge_pair in self.hedge_pairs.items():
            if not hedge_pair.is_balanced:
                imbalanced.append({
                    'symbol': symbol,
                    'quantity_diff': hedge_pair.quantity_diff,
                    'value_diff': hedge_pair.value_diff,
                    'spot_position': hedge_pair.spot_position.to_dict() if hedge_pair.spot_position else None,
                    'futures_position': hedge_pair.futures_position.to_dict() if hedge_pair.futures_position else None
                })

        return imbalanced

    def get_monitor_stats(self) -> Dict:
        """获取监控统计"""
        return {
            'is_running': self.is_running,
            'total_checks': self.total_checks,
            'imbalance_alerts': self.imbalance_alerts,
            'position_errors': self.position_errors,
            'active_hedge_pairs': len(self.hedge_pairs),
            'imbalanced_pairs': len([p for p in self.hedge_pairs.values() if not p.is_balanced]),
            'last_update': max(self.last_update_time.values()) if self.last_update_time else 0
        }

# 测试和调试功能
async def test_position_monitor():
    """测试仓位监控器"""
    logger.info("Testing PositionMonitor...")

    # 创建模拟交易所
    class MockExchange:
        def __init__(self, name):
            self.name = name

        async def get_spot_balance(self):
            return {'BTC': 0.1, 'USDT': 5000.0}

        async def get_futures_positions(self):
            return [
                {
                    'symbol': 'BTCUSDT',
                    'size': '-0.1',
                    'side': 'short',
                    'entry_price': '50000.0',
                    'mark_price': '50100.0',
                    'unrealized_pnl': '-10.0'
                }
            ]

        # 🔥 删除REST API ticker方法 - 统一使用OpportunityScanner.market_data
        # async def get_spot_ticker() 已删除，改为使用 OpportunityScanner.market_data
        # async def get_futures_ticker() 已删除，改为使用 OpportunityScanner.market_data

    # 创建模拟交易所
    exchanges = {
        'gate': MockExchange('gate'),
        'bybit': MockExchange('bybit')
    }

    # 创建仓位监控器
    monitor = PositionMonitor(exchanges)

    # 添加回调函数
    def position_change_callback(info):
        logger.info(f"Position change: {info}")

    def imbalance_alert_callback(info):
        logger.warning(f"Imbalance alert: {info}")

    monitor.add_position_change_callback(position_change_callback)
    monitor.add_imbalance_alert_callback(imbalance_alert_callback)

    # 测试单次更新
    await monitor.update_all_positions()

    # 检查结果
    positions = monitor.get_all_positions()
    assert 'spot_positions' in positions, "Should have spot positions"
    assert 'futures_positions' in positions, "Should have futures positions"
    assert 'hedge_pairs' in positions, "Should have hedge pairs"

    # 检查统计
    stats = monitor.get_monitor_stats()
    assert stats['total_checks'] == 1, "Should have 1 check"

    logger.info("✅ PositionMonitor test completed successfully")

if __name__ == '__main__':
    asyncio.run(test_position_monitor())