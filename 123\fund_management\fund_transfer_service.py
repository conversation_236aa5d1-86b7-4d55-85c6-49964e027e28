"""
资金划转服务
负责执行具体的资金划转操作
"""

import asyncio
import logging
from typing import Dict, Optional
from datetime import datetime
import time
import os
from dotenv import load_dotenv

from exchanges.exchanges_base import BaseExchange, AccountType

logger = logging.getLogger(__name__)

# 确保环境变量已加载
load_dotenv()


class FundTransferService:
    """资金划转服务
    :param exchanges: 交易所实例字典
    :param settings: 全局配置实例（可选，如果为None则从环境变量读取）
    """
    
    def __init__(self, exchanges: Dict[str, BaseExchange], settings=None):
        self.exchanges = exchanges
        self.settings = settings
        
        # 划转记录
        self.transfer_history = []
        
        # 从settings或环境变量读取划转超时时间（秒）
        if settings is None:
            self.transfer_timeout = int(os.getenv("FUND_TRANSFER_TIMEOUT", "10"))
            logger.info(f"从环境变量加载资金划转超时时间: {self.transfer_timeout}秒")
        else:
            self.transfer_timeout = getattr(settings.system, "fund_transfer_timeout", 10)
        
        # 统计信息
        self.stats = {
            "total_transfers": 0,
            "successful_transfers": 0,
            "failed_transfers": 0,
            "total_amount": 0.0
        }
    
    async def transfer(self, exchange_name: str, currency: str, amount: float,
                      from_account: AccountType, to_account: AccountType,
                      timeout: Optional[float] = None) -> bool:
        """
        执行资金划转
        :param exchange_name: 交易所名称
        :param currency: 币种
        :param amount: 数量
        :param from_account: 源账户
        :param to_account: 目标账户
        :param timeout: 超时时间
        :return: 是否成功
        """
        timeout = timeout or self.transfer_timeout
        start_time = time.time()
        
        try:
            logger.info(f"开始划转: {exchange_name} {amount} {currency} "
                       f"{from_account.value} -> {to_account.value}")
            
            exchange = self.exchanges.get(exchange_name)
            if not exchange:
                logger.error(f"未找到交易所: {exchange_name}")
                return False
            
            # 检查是否为统一账户
            if exchange.is_unified_account():
                logger.info(f"{exchange_name}是统一账户，无需划转")
                return True
            
            # 记录划转前余额
            before_balance = await self._get_account_balance(
                exchange, currency, from_account, to_account
            )
            
            # 执行划转
            result = await asyncio.wait_for(
                exchange.transfer_funds(currency, amount, from_account, to_account),
                timeout=timeout
            )
            
            if result:
                # 所有交易所都进行余额验证，包括Gate.io
                retry_count = 5  # 增加重试次数
                base_wait_time = 2.0  # 基础等待时间
                
                for retry in range(retry_count):
                    # 逐渐增加等待时间
                    wait_time = base_wait_time + (retry * 1.0)
                    await asyncio.sleep(wait_time)
                    
                    # 验证划转结果
                    try:
                        after_balance = await self._get_account_balance(
                            exchange, currency, from_account, to_account
                        )
                        
                        # 🔥 增强：确保before_balance和after_balance都是有效的数值元组
                        if (not isinstance(before_balance, tuple) or len(before_balance) != 2 or
                            not isinstance(after_balance, tuple) or len(after_balance) != 2):
                            logger.error(f"余额数据格式异常: before={before_balance}, after={after_balance}")
                            continue
                        
                        # 🔥 增强：确保所有值都是数值类型
                        try:
                            before_from = float(before_balance[0]) if before_balance[0] is not None else 0.0
                            before_to = float(before_balance[1]) if before_balance[1] is not None else 0.0
                            after_from = float(after_balance[0]) if after_balance[0] is not None else 0.0
                            after_to = float(after_balance[1]) if after_balance[1] is not None else 0.0
                        except (ValueError, TypeError) as convert_error:
                            logger.error(f"余额类型转换失败: {convert_error}")
                            logger.error(f"before_balance: {before_balance}, after_balance: {after_balance}")
                            continue
                        
                        # 检查余额变化
                        from_diff = before_from - after_from
                        to_diff = after_to - before_to
                        
                        logger.info(f"第{retry+1}次验证: 预期变化{amount}, "
                                   f"实际变化: from={from_diff:.4f}, to={to_diff:.4f}")
                        
                        # 改进容错性：允许更大的误差范围，考虑手续费
                        error_threshold = max(0.01, amount * 0.01)  # 1%误差或0.01 USDT
                        
                        # 检查源账户减少
                        from_match = abs(from_diff - amount) <= error_threshold
                        # 检查目标账户增加（可能有手续费）
                        to_match = to_diff >= (amount * 0.99)  # 允许1%的手续费
                        
                        if from_match and to_match:
                            # 记录成功的划转
                            self._record_transfer(
                                exchange_name, currency, amount,
                                from_account, to_account, True,
                                time.time() - start_time
                            )
                            
                            logger.info(f"划转成功: {exchange_name} {amount} {currency} "
                                      f"{from_account.value} -> {to_account.value}")
                            return True
                            
                        elif retry < retry_count - 1:
                            logger.warning(f"划转验证重试({retry+1}/{retry_count}): "
                                        f"from_match={from_match}, to_match={to_match}")
                        
                    except Exception as verify_error:
                        logger.warning(f"验证第{retry+1}次出错: {verify_error}")
                        if retry == retry_count - 1:
                            # 🔥 最后一次重试失败时，不抛出异常，而是记录失败
                            logger.error(f"划转验证最终失败: {verify_error}")
                            self._record_transfer(
                                exchange_name, currency, amount,
                                from_account, to_account, False,
                                time.time() - start_time, f"验证异常: {verify_error}"
                            )
                            return False
                        else:
                            # 继续重试
                            continue
                
                # 验证失败，记录为失败
                logger.error(f"划转验证失败: 预期变化{amount}, "
                            f"最终变化: from={from_diff:.4f}, to={to_diff:.4f}")
                
                # 记录失败的划转
                self._record_transfer(
                    exchange_name, currency, amount,
                    from_account, to_account, False,
                    time.time() - start_time, f"余额验证失败: from={from_diff:.4f}, to={to_diff:.4f}"
                )
                return False
            else:
                logger.error(f"划转失败: {exchange_name}")
                # 记录失败的划转
                self._record_transfer(
                    exchange_name, currency, amount,
                    from_account, to_account, False,
                    time.time() - start_time, "API调用失败"
                )
                return False
                
        except asyncio.TimeoutError:
            logger.error(f"划转超时: {exchange_name} {amount} {currency}")
            self._record_transfer(
                exchange_name, currency, amount,
                from_account, to_account, False,
                timeout, "timeout"
            )
            return False
            
        except Exception as e:
            logger.error(f"划转异常: {exchange_name} {e}")
            self._record_transfer(
                exchange_name, currency, amount,
                from_account, to_account, False,
                time.time() - start_time, str(e)
            )
            return False
    
    async def batch_transfer(self, transfers: list) -> Dict[str, bool]:
        """
        批量划转
        :param transfers: 划转列表
        :return: 划转结果字典
        """
        results = {}
        
        for transfer in transfers:
            key = f"{transfer['exchange']}_{transfer['from']}_{transfer['to']}"
            result = await self.transfer(
                transfer['exchange'],
                transfer['currency'],
                transfer['amount'],
                AccountType(transfer['from']),
                AccountType(transfer['to'])
            )
            results[key] = result
        
        return results
    
    async def _get_account_balance(self, exchange: BaseExchange, currency: str,
                                  from_account: AccountType,
                                  to_account: AccountType) -> tuple:
        """获取账户余额 - 🔥 修复：确保返回数值类型而不是dict"""
        try:
            from_balance_dict = await exchange.get_balance(from_account)
            to_balance_dict = await exchange.get_balance(to_account)
            
            # 🔥 关键修复：安全地提取数值，处理各种可能的返回格式
            def extract_balance_value(balance_dict, currency):
                """安全地从余额字典中提取数值"""
                if not isinstance(balance_dict, dict):
                    logger.warning(f"余额返回类型异常: {type(balance_dict)}, 值: {balance_dict}")
                    return 0.0
                
                # 尝试直接获取
                value = balance_dict.get(currency, 0)
                
                # 如果是嵌套字典（某些交易所可能返回 {'USDT': {'available': 100, 'total': 100}}）
                if isinstance(value, dict):
                    # 尝试获取available或free字段
                    if 'available' in value:
                        value = value['available']
                    elif 'free' in value:
                        value = value['free']
                    elif 'total' in value:
                        value = value['total']
                    else:
                        # 如果都没有，尝试第一个数值字段
                        for k, v in value.items():
                            if isinstance(v, (int, float)):
                                value = v
                                break
                        else:
                            logger.warning(f"无法从嵌套字典中提取余额: {value}")
                            value = 0.0
                
                # 确保返回数值类型
                try:
                    return float(value) if value is not None else 0.0
                except (ValueError, TypeError):
                    logger.warning(f"余额转换失败: {value}")
                    return 0.0
            
            from_balance = extract_balance_value(from_balance_dict, currency)
            to_balance = extract_balance_value(to_balance_dict, currency)
            
            logger.debug(f"余额提取: {from_account.value}={from_balance}, {to_account.value}={to_balance}")
            
            return (from_balance, to_balance)
            
        except Exception as e:
            logger.error(f"获取账户余额失败: {e}")
            # 返回安全的默认值
            return (0.0, 0.0)
    
    def _record_transfer(self, exchange: str, currency: str, amount: float,
                        from_account: AccountType, to_account: AccountType,
                        success: bool, duration: float, error: str = ""):
        """记录划转"""
        record = {
            "timestamp": datetime.now().isoformat(),
            "exchange": exchange,
            "currency": currency,
            "amount": amount,
            "from_account": from_account.value,
            "to_account": to_account.value,
            "success": success,
            "duration": duration,
            "error": error
        }
        
        self.transfer_history.append(record)
        
        # 更新统计
        self.stats["total_transfers"] += 1
        if success:
            self.stats["successful_transfers"] += 1
            self.stats["total_amount"] += amount
        else:
            self.stats["failed_transfers"] += 1
        
        # 只保留最近1000条记录
        if len(self.transfer_history) > 1000:
            self.transfer_history = self.transfer_history[-1000:]
    
    async def get_balance_summary(self) -> Dict[str, Dict]:
        """🔥 简化版：直接使用ArbitrageEngine余额缓存，删除冲突模块"""
        try:
            from core.arbitrage_engine import get_arbitrage_engine
            engine = get_arbitrage_engine()

            summary = {}
            if engine and hasattr(engine, 'balance_cache') and engine.balance_cache:
                balances = engine.balance_cache

                for exchange_name in ["gate", "bybit", "okx"]:
                    exchange = self.exchanges.get(exchange_name)
                    if not exchange:
                        logger.warning(f"{exchange_name}未连接，余额查询跳过")
                        summary[exchange_name] = {"error": "未连接"}
                        continue

                    # 从ArbitrageEngine缓存中获取余额数据
                    if exchange.is_unified_account():
                        unified_key = f"{exchange_name}_unified_usdt"
                        total_usdt = balances.get(unified_key, 0.0)
                        summary[exchange_name] = {
                            "type": "unified",
                            "balance": {"USDT": total_usdt},
                            "total_usdt": total_usdt
                        }
                    else:
                        spot_key = f"{exchange_name}_spot_usdt"
                        futures_key = f"{exchange_name}_futures_usdt"
                        spot_usdt = balances.get(spot_key, 0.0)
                        futures_usdt = balances.get(futures_key, 0.0)

                        summary[exchange_name] = {
                            "type": "separated",
                            "spot": {"USDT": spot_usdt},
                            "futures": {"USDT": futures_usdt},
                            "total_usdt": spot_usdt + futures_usdt
                        }
            else:
                logger.warning("ArbitrageEngine缓存为空，无法提供余额汇总")
                summary = {"error": "缓存为空"}

            return summary

        except Exception as e:
            logger.error(f"获取余额汇总失败: {e}")
            return {"error": str(e)}
    
    def get_transfer_stats(self) -> Dict:
        """获取划转统计"""
        success_rate = 0
        if self.stats["total_transfers"] > 0:
            success_rate = (self.stats["successful_transfers"] / 
                          self.stats["total_transfers"] * 100)
        
        return {
            **self.stats,
            "success_rate": f"{success_rate:.2f}%",
            "recent_transfers": self.transfer_history[-10:]  # 最近10条
        }
    
    def get_transfer_history(self, exchange: Optional[str] = None,
                           limit: int = 100) -> list:
        """
        获取划转历史
        :param exchange: 交易所名称，None表示所有
        :param limit: 限制数量
        :return: 划转记录列表
        """
        history = self.transfer_history

        if exchange:
            history = [h for h in history if h["exchange"] == exchange]

        return history[-limit:]

    async def transfer_to_target_allocation(self, exchange_name: str,
                                          target_allocation: Dict[str, float]) -> bool:
        """
        将资金调整到目标分配 - 专门为Gate.io资金平衡功能设计
        :param exchange_name: 交易所名称
        :param target_allocation: 目标分配，如 {'spot': 100.0, 'futures': 100.0}
        :return: 是否成功
        """
        try:
            logger.info(f"🎯 开始调整{exchange_name}资金到目标分配: {target_allocation}")

            exchange = self.exchanges.get(exchange_name)
            if not exchange:
                logger.error(f"❌ 未找到交易所: {exchange_name}")
                return False

            # 检查是否为统一账户
            if exchange.is_unified_account():
                logger.info(f"✅ {exchange_name}是统一账户，无需资金分配调整")
                return True

            # 获取当前余额
            spot_balance_dict = await exchange.get_balance(AccountType.SPOT)
            futures_balance_dict = await exchange.get_balance(AccountType.FUTURES)

            # 安全地提取USDT余额
            def extract_usdt_balance(balance_dict):
                if not isinstance(balance_dict, dict):
                    logger.warning(f"余额格式异常: {balance_dict}")
                    return 0.0

                usdt_value = balance_dict.get('USDT', 0)
                if isinstance(usdt_value, dict):
                    # 处理嵌套字典格式 {'USDT': {'available': 100}}
                    return float(usdt_value.get('available', 0) or
                               usdt_value.get('free', 0) or
                               usdt_value.get('total', 0) or 0)
                return float(usdt_value) if usdt_value is not None else 0.0

            current_spot = extract_usdt_balance(spot_balance_dict)
            current_futures = extract_usdt_balance(futures_balance_dict)

            target_spot = target_allocation.get('spot', 0)
            target_futures = target_allocation.get('futures', 0)

            logger.info(f"📊 {exchange_name}当前余额: 现货=${current_spot:.2f}, 期货=${current_futures:.2f}")
            logger.info(f"🎯 {exchange_name}目标余额: 现货=${target_spot:.2f}, 期货=${target_futures:.2f}")

            # 计算需要调整的金额
            spot_diff = target_spot - current_spot

            # 设置最小调整阈值（避免小额频繁调整）
            min_transfer_amount = 1.0  # 1 USDT

            if abs(spot_diff) < min_transfer_amount:
                logger.info(f"✅ {exchange_name}现货账户余额差异小于{min_transfer_amount} USDT，无需调整")
                return True

            # 执行资金划转
            if spot_diff > 0:
                # 需要向现货账户转入资金
                transfer_amount = abs(spot_diff)
                logger.info(f"💸 执行划转: {exchange_name} 期货→现货 ${transfer_amount:.2f}")

                success = await self.transfer(
                    exchange_name, "USDT", transfer_amount,
                    AccountType.FUTURES, AccountType.SPOT
                )
            else:
                # 需要向期货账户转入资金
                transfer_amount = abs(spot_diff)
                logger.info(f"💸 执行划转: {exchange_name} 现货→期货 ${transfer_amount:.2f}")

                success = await self.transfer(
                    exchange_name, "USDT", transfer_amount,
                    AccountType.SPOT, AccountType.FUTURES
                )

            if success:
                logger.info(f"✅ {exchange_name}资金分配调整成功")
                return True
            else:
                logger.error(f"❌ {exchange_name}资金分配调整失败")
                return False

        except Exception as e:
            logger.error(f"💥 {exchange_name}资金分配调整异常: {e}")
            return False


# 测试代码
if __name__ == "__main__":
    from exchanges import GateExchange, BybitExchange, OKXExchange
    from config.settings import Settings
    
    async def test():
        # 创建交易所实例
        exchanges = {
            "gate": GateExchange(
                os.getenv("GATE_API_KEY", ""),
                os.getenv("GATE_API_SECRET", "")
            )
        }
        
        # 创建settings实例
        settings = Settings()
        
        # 创建划转服务（推荐用法）
        service = FundTransferService(exchanges, settings)
        
        # 获取余额汇总
        print("获取余额汇总...")
        summary = await service.get_balance_summary()
        print(f"余额汇总: {summary}")
        
        # 测试划转（请谨慎，这会真实划转资金）
        # result = await service.transfer(
        #     "gate", "USDT", 10,
        #     AccountType.SPOT, AccountType.FUTURES
        # )
        # print(f"划转结果: {result}")
        
        # 获取统计
        stats = service.get_transfer_stats()
        print(f"划转统计: {stats}")
        
        # 关闭连接
        for exchange in exchanges.values():
            await exchange.close()
    
    asyncio.run(test())