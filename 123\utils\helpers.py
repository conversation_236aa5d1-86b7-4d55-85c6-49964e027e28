"""
辅助函数模块
提供通用的工具函数
"""

import time
import math
from typing import Union, Optional
from decimal import Decimal
from datetime import datetime, timezone


def format_currency(amount: Union[float, Decimal], currency: str = "USDT", decimals: int = 2) -> str:
    """格式化货币金额"""
    if isinstance(amount, Decimal):
        amount = float(amount)
    
    if amount >= 1000000:
        return f"{amount/1000000:.1f}M {currency}"
    elif amount >= 1000:
        return f"{amount/1000:.1f}K {currency}"
    else:
        # 🔥 统一精度：对于小额，使用8位小数确保最高精度
        if amount < 0.01 and amount > 0:
            # 对于小于0.01的金额，保留8位小数
            return f"{amount:.8f} {currency}"
        elif amount < 0.1 and amount > 0:
            # 对于0.01-0.1的金额，使用8位小数
            return f"{amount:.8f} {currency}".rstrip('0').rstrip('.')
        else:
            return f"{amount:.{decimals}f} {currency}"


def calculate_percentage(value: Union[float, Decimal], base: Union[float, Decimal] = 1.0) -> float:
    """计算百分比"""
    if isinstance(value, Decimal):
        value = float(value)
    if isinstance(base, Decimal):
        base = float(base)
    
    if base == 0:
        return 0.0
    
    return (value / base) * 100


# 🔥 删除重复的精度处理方法：format_decimal, round_to_precision
# 所有精度处理统一使用 trading_rules_preloader.format_amount_unified()
# 避免重复实现，确保一致性


def format_timestamp(timestamp: Optional[float] = None, format_str: str = "%Y-%m-%d %H:%M:%S") -> str:
    """格式化时间戳"""
    if timestamp is None:
        timestamp = time.time()
    
    dt = datetime.fromtimestamp(timestamp, tz=timezone.utc)
    return dt.strftime(format_str)


# 🔥 删除重复的价差计算函数
# 统一使用 OpportunityScanner._calculate_spread() 作为唯一价差计算源
# 其他模块应该通过OpportunityScanner获取价差计算，避免重复实现


def safe_float(value, default: float = 0.0) -> float:
    """安全转换为浮点数"""
    try:
        if isinstance(value, str):
            value = value.strip()
            if not value:
                return default
        return float(value)
    except (ValueError, TypeError):
        return default


def safe_int(value, default: int = 0) -> int:
    """安全转换为整数"""
    try:
        if isinstance(value, str):
            value = value.strip()
            if not value:
                return default
        return int(float(value))
    except (ValueError, TypeError):
        return default


# 🔥 删除重复方法：truncate_to_precision
# 统一使用 trading_rules_preloader.truncate_to_step_size()


def validate_symbol(symbol: str) -> bool:
    """验证交易对格式"""
    if not symbol or not isinstance(symbol, str):
        return False
    
    # 支持多种格式: BTC-USDT, BTC/USDT, BTCUSDT
    if '-' in symbol or '/' in symbol:
        parts = symbol.replace('-', '/').split('/')
        return len(parts) == 2 and all(len(part) >= 2 for part in parts)
    else:
        # BTCUSDT 格式，简单验证长度
        return len(symbol) >= 5


# 🔥 修复：删除重复的normalize_symbol函数，统一使用currency_adapter.normalize_symbol
# 所有符号标准化统一使用 exchanges.currency_adapter.normalize_symbol()
# 避免重复实现，确保一致性


def get_base_quote_from_symbol(symbol: str) -> tuple:
    """从交易对中提取基础货币和计价货币"""
    if '-' in symbol:
        parts = symbol.split('-')
    elif '/' in symbol:
        parts = symbol.split('/')
    elif '_' in symbol:
        parts = symbol.split('_')
    else:
        # BTCUSDT 格式，需要特殊处理
        if symbol.endswith('USDT'):
            return symbol[:-4], 'USDT'
        elif symbol.endswith('BTC'):
            return symbol[:-3], 'BTC'
        elif symbol.endswith('ETH'):
            return symbol[:-3], 'ETH'
        else:
            return symbol, ''
    
    if len(parts) == 2:
        return parts[0], parts[1]
    else:
        return symbol, ''


def format_execution_time(milliseconds: float) -> str:
    """格式化执行时间"""
    if milliseconds < 1000:
        return f"{milliseconds:.0f}ms"
    else:
        seconds = milliseconds / 1000
        return f"{seconds:.2f}s"


def calculate_profit_margin(cost: float, selling_price: float) -> float:
    """计算利润率"""
    if cost <= 0:
        return 0.0
    
    profit = selling_price - cost
    return (profit / cost) * 100


def is_within_tolerance(value1: float, value2: float, tolerance: float = 0.001) -> bool:
    """检查两个值是否在容差范围内"""
    return abs(value1 - value2) <= tolerance


if __name__ == '__main__':
    # 测试函数
    print("Testing helper functions...")
    
    # 测试格式化
    print(f"format_currency(1234.56): {format_currency(1234.56)}")
    print(f"calculate_percentage(0.002): {calculate_percentage(0.002):.2f}%")
    print(f"normalize_symbol('BTC-USDT', 'gate'): {normalize_symbol('BTC-USDT', 'gate')}")
    print(f"get_base_quote_from_symbol('BTC-USDT'): {get_base_quote_from_symbol('BTC-USDT')}")
    
    print("All tests passed!") 