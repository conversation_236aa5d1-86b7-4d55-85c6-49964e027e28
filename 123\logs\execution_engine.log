2025-07-31 10:06:37.001 [INFO] [ExecutionEngine] 🔥 ExecutionEngine日志系统启动 - 使用统一日志配置
2025-07-31 10:06:37.001 [INFO] [ExecutionEngine] ================================================================================
2025-07-31 10:06:37.002 [INFO] [ExecutionEngine] 🚀 ExecutionEngine初始化开始
2025-07-31 10:06:37.002 [INFO] [ExecutionEngine] ================================================================================
2025-07-31 10:06:37.002 [INFO] [ExecutionEngine] ✅ 并行套利控制器集成完成
2025-07-31 10:06:37.002 [INFO] [ExecutionEngine]    📊 最大并行数量: 3
2025-07-31 10:06:37.002 [INFO] [ExecutionEngine]    🔒 完全向下兼容现有逻辑
2025-07-31 10:06:37.002 [INFO] [ExecutionEngine] ✅ ExecutionEngine初始化完成
2025-07-31 10:06:37.002 [INFO] [ExecutionEngine] 🚀 初始化ExecutionEngine...
2025-07-31 10:06:37.002 [INFO] [ExecutionEngine] 🚀 步骤1.1: 检查交易所实例...
2025-07-31 10:06:37.002 [INFO] [ExecutionEngine] ✅ 使用传递的交易所实例: ['gate', 'bybit', 'okx']
2025-07-31 10:06:37.003 [INFO] [ExecutionEngine] 🔍 gate交易所需要初始化...
2025-07-31 10:06:38.536 [INFO] [ExecutionEngine] ✅ gate交易所初始化完成
2025-07-31 10:06:38.536 [INFO] [ExecutionEngine] 🔍 bybit交易所需要初始化...
2025-07-31 10:06:40.195 [INFO] [ExecutionEngine] ✅ bybit交易所初始化完成
2025-07-31 10:06:40.195 [INFO] [ExecutionEngine] 🔍 okx交易所需要初始化...
2025-07-31 10:06:40.195 [INFO] [ExecutionEngine] ✅ okx交易所初始化完成
2025-07-31 10:06:40.195 [INFO] [ExecutionEngine] 🎯 步骤1.2: 交易所验证完成，可用: 3个
2025-07-31 10:06:40.195 [INFO] [ExecutionEngine] 🚀 步骤2.1: 开始初始化交易器...
2025-07-31 10:06:40.223 [INFO] [ExecutionEngine] 🔍 步骤2.2: 导入交易器模块完成
2025-07-31 10:06:40.224 [INFO] [ExecutionEngine] 🔍 步骤2.3.1: 为gate创建交易器...
2025-07-31 10:06:40.224 [INFO] [ExecutionEngine]    ✅ gate现货交易器创建完成
2025-07-31 10:06:40.224 [INFO] [ExecutionEngine]    ✅ gate期货交易器创建完成
2025-07-31 10:06:40.224 [INFO] [ExecutionEngine] 🔍 步骤2.3.2: 为bybit创建交易器...
2025-07-31 10:06:40.224 [INFO] [ExecutionEngine]    ✅ bybit现货交易器创建完成
2025-07-31 10:06:40.224 [INFO] [ExecutionEngine]    ✅ bybit期货交易器创建完成
2025-07-31 10:06:40.224 [INFO] [ExecutionEngine] 🔍 步骤2.3.3: 为okx创建交易器...
2025-07-31 10:06:40.224 [INFO] [ExecutionEngine]    ✅ okx现货交易器创建完成
2025-07-31 10:06:40.224 [INFO] [ExecutionEngine]    ✅ okx期货交易器创建完成
2025-07-31 10:06:40.224 [INFO] [ExecutionEngine] 🎯 步骤2.4: 交易器初始化完成，共创建: 6个交易器
2025-07-31 10:06:40.224 [INFO] [ExecutionEngine] 🚀 步骤3.1: 开始初始化统一管理器...
2025-07-31 10:06:40.225 [INFO] [ExecutionEngine] 🔍 步骤3.2: 获取统一开仓管理器...
2025-07-31 10:06:40.225 [INFO] [ExecutionEngine] ✅ 统一开仓管理器获取成功
2025-07-31 10:06:40.225 [INFO] [ExecutionEngine] 🔍 步骤3.3: 获取统一平仓管理器...
2025-07-31 10:06:40.225 [INFO] [ExecutionEngine] ✅ 统一平仓管理器获取成功
2025-07-31 10:06:40.225 [INFO] [ExecutionEngine] 🔍 步骤3.4: 获取交易规则预加载器...
2025-07-31 10:06:40.225 [INFO] [ExecutionEngine] ✅ 交易规则预加载器获取成功
2025-07-31 10:06:40.225 [INFO] [ExecutionEngine] 🔍 步骤3.5: 初始化保证金计算器...
2025-07-31 10:06:40.225 [INFO] [ExecutionEngine] ✅ 保证金计算器初始化成功
2025-07-31 10:06:40.225 [INFO] [ExecutionEngine] 🔍 步骤3.6: 检查开仓管理器初始化方法...
2025-07-31 10:06:40.225 [INFO] [ExecutionEngine] ℹ️ 开仓管理器无需初始化（已就绪）
2025-07-31 10:06:40.225 [INFO] [ExecutionEngine] 🔍 步骤3.7: 检查平仓管理器初始化方法...
2025-07-31 10:06:40.225 [INFO] [ExecutionEngine] ℹ️ 平仓管理器无需初始化（已就绪）
2025-07-31 10:06:40.225 [INFO] [ExecutionEngine] 🎯 步骤3.8: 统一管理器初始化完成
2025-07-31 10:06:40.225 [INFO] [ExecutionEngine] 🚀 步骤4.1: 开始初始化订单管理器...
2025-07-31 10:06:40.225 [INFO] [ExecutionEngine] 🔍 步骤4.2: 导入OrderManager模块完成
2025-07-31 10:06:40.225 [INFO] [ExecutionEngine] 🔍 步骤4.3: 创建OrderManager实例...
2025-07-31 10:06:40.225 [INFO] [ExecutionEngine] 🔍 现货交易器列表: ['gate', 'bybit', 'okx']
2025-07-31 10:06:40.226 [INFO] [ExecutionEngine] 🔍 期货交易器列表: ['gate', 'bybit', 'okx']
2025-07-31 10:06:40.226 [INFO] [ExecutionEngine] 🔍 检查现货交易器 gate: SpotTrader
2025-07-31 10:06:40.226 [INFO] [ExecutionEngine] 🔍 检查现货交易器 bybit: SpotTrader
2025-07-31 10:06:40.226 [INFO] [ExecutionEngine] 🔍 检查现货交易器 okx: SpotTrader
2025-07-31 10:06:40.226 [INFO] [ExecutionEngine] 🔍 检查期货交易器 gate: FuturesTrader
2025-07-31 10:06:40.226 [INFO] [ExecutionEngine] 🔍 检查期货交易器 bybit: FuturesTrader
2025-07-31 10:06:40.226 [INFO] [ExecutionEngine] 🔍 检查期货交易器 okx: FuturesTrader
2025-07-31 10:06:40.226 [INFO] [ExecutionEngine] 🔍 开始调用OrderManager构造函数...
2025-07-31 10:06:40.226 [INFO] [ExecutionEngine] ✅ OrderManager实例创建成功
2025-07-31 10:06:40.226 [INFO] [ExecutionEngine] 🎯 步骤4.4: 订单管理器初始化完成
2025-07-31 10:06:40.226 [INFO] [ExecutionEngine] 🚀 步骤5.1: 开始预加载交易规则...
2025-07-31 10:06:40.226 [INFO] [ExecutionEngine] 🔍 步骤5.2: 调用preload_all_trading_rules...
2025-07-31 10:06:47.984 [INFO] [ExecutionEngine] ✅ 步骤5.3: 交易规则预加载成功
2025-07-31 10:06:47.984 [INFO] [ExecutionEngine] 🎯 预加载统计:
2025-07-31 10:06:47.984 [INFO] [ExecutionEngine]    缓存规则数: 60
2025-07-31 10:06:47.984 [INFO] [ExecutionEngine]    成功加载: 120
2025-07-31 10:06:47.984 [INFO] [ExecutionEngine]    失败加载: 0
2025-07-31 10:06:47.984 [INFO] [ExecutionEngine]    预加载耗时: 12.8ms
2025-07-31 10:06:47.985 [INFO] [ExecutionEngine] 🎯 步骤5.4: 交易规则预加载阶段完成
2025-07-31 10:06:47.985 [INFO] [ExecutionEngine] ✅ ExecutionEngine初始化完成
2025-07-31 10:15:41.098 [INFO] [ExecutionEngine] ================================================================================
2025-07-31 10:15:41.098 [INFO] [ExecutionEngine] 🚀 【套利执行开始】 RESOLV-USDT
2025-07-31 10:15:41.098 [INFO] [ExecutionEngine]    现货交易所: bybit
2025-07-31 10:15:41.098 [INFO] [ExecutionEngine]    期货交易所: gate
2025-07-31 10:15:41.098 [INFO] [ExecutionEngine]    基础数量: 173.01038062283735
2025-07-31 10:15:41.098 [INFO] [ExecutionEngine]    价差百分比: 0.69%
2025-07-31 10:15:41.098 [INFO] [ExecutionEngine] ================================================================================
2025-07-31 10:15:41.098 [INFO] [ExecutionEngine] 📊 EXECUTION_START | RESOLV-USDT | bybit -> gate | 差价=0.69%
2025-07-31 10:15:41.098 [INFO] [ExecutionEngine] 🔍 执行步骤A.0: 并行控制检查...
2025-07-31 10:15:41.099 [INFO] [ExecutionEngine] ✅ 并行控制检查通过，订单编号: ARB_RESOLV-USDT_1753949741
2025-07-31 10:15:41.099 [INFO] [ExecutionEngine] 🔍 执行步骤A.1: 更新执行状态...
2025-07-31 10:15:41.099 [INFO] [ExecutionEngine] ✅ 执行状态已更新为EXECUTING
2025-07-31 10:15:41.099 [INFO] [ExecutionEngine] 🔍 执行步骤A.2: 创建执行结果对象...
2025-07-31 10:15:41.099 [INFO] [ExecutionEngine] ✅ 执行结果对象创建完成
2025-07-31 10:15:41.099 [INFO] [ExecutionEngine] 🔍 执行步骤B.1: 开始98%对冲质量预检查...
2025-07-31 10:15:41.099 [INFO] [ExecutionEngine] 📊 6大缓存系统使用统计:
2025-07-31 10:15:41.099 [INFO] [ExecutionEngine]    - 余额缓存: 实时更新 (ArbitrageEngine)
2025-07-31 10:15:41.099 [INFO] [ExecutionEngine]    - 保证金缓存: 5分钟TTL (MarginCalculator)
2025-07-31 10:15:41.099 [INFO] [ExecutionEngine]    - 交易规则缓存: N/A个规则 (TradingRulesPreloader)
2025-07-31 10:15:41.100 [INFO] [ExecutionEngine]    - 精度缓存: 24小时TTL (TradingRulesPreloader)
2025-07-31 10:15:41.100 [INFO] [ExecutionEngine]    - 订单簿缓存: 实时更新 (WebSocket)
2025-07-31 10:15:41.100 [INFO] [ExecutionEngine]    - 对冲质量缓存: 10秒TTL (预检查)
2025-07-31 10:15:41.100 [INFO] [ExecutionEngine] 📈 缓存命中率: 100.0% (总条目: 0)
2025-07-31 10:15:41.100 [INFO] [ExecutionEngine] 🎯 统一对冲质量检查: 比例=0.9931 (✅通过)
2025-07-31 10:15:41.100 [INFO] [ExecutionEngine] ✅ 执行步骤B.1完成: 对冲质量检查通过
2025-07-31 10:15:41.100 [INFO] [ExecutionEngine] 🔍 执行步骤C.1: 开始极速并行执行...
2025-07-31 10:15:41.100 [DEBUG] [ExecutionEngine] 🔥 开始<30ms极速并行执行: 现货(bybit) + 期货(gate) RESOLV-USDT
2025-07-31 10:15:41.101 [INFO] [ExecutionEngine] 🔧 统一金额计算: $35.0 ÷ 0.202300 = 173.010380 代币
2025-07-31 10:15:41.101 [INFO] [ExecutionEngine] 🔥 智能协调开始: 原始数量=173.01038000币
2025-07-31 10:15:41.101 [INFO] [ExecutionEngine] 🔥 Gate期货整数截取: 173.010380 → 173币
2025-07-31 10:15:41.101 [INFO] [ExecutionEngine] 🎯 智能协调结果: 现货=173.010000币, 期货=173.000000币
2025-07-31 10:15:41.101 [INFO] [ExecutionEngine] 📊 对冲质量检查已在TradingRulesPreloader中完成（包含完美对冲逻辑）
2025-07-31 10:15:41.101 [INFO] [ExecutionEngine] ✅ 智能协调完成: 现货=173.010000币, 期货=173.000000币
2025-07-31 10:15:41.102 [INFO] [ExecutionEngine] 🔥 统一单位：返回币数量，各交易所在下单时自行转换
2025-07-31 10:15:41.102 [INFO] [ExecutionEngine] ✅ 统一架构：数量协调完成，精度转换由各交易所统一处理
2025-07-31 10:15:41.102 [INFO] [ExecutionEngine]    协调后数量: 现货=173.01000000, 期货=173.00000000
2025-07-31 10:15:41.102 [INFO] [ExecutionEngine]    对冲质量检查已在预检查阶段完成，符合MD文件统一计算要求
2025-07-31 10:15:41.102 [DEBUG] [ExecutionEngine] ⚡ 零延迟参数准备: 1.30ms
2025-07-31 10:15:41.114 [INFO] [ExecutionEngine] ✅ 开仓验证：数据快照验证通过，使用快照数据确保一致性
2025-07-31 10:15:41.114 [DEBUG] [ExecutionEngine] ✅ 快照详情: 快照降级使用: 5730.0ms > 800ms (标准阈值)
2025-07-31 10:15:41.114 [DEBUG] [ExecutionEngine] ✅ 使用数据快照，跳过新鲜度检查
2025-07-31 10:15:41.115 [DEBUG] [ExecutionEngine] ✅ 使用快照中的差价计算结果: 0.692%
2025-07-31 10:15:41.115 [DEBUG] [ExecutionEngine] ✅ 开仓验证通过: 当前价差0.692%
2025-07-31 10:15:41.115 [DEBUG] [ExecutionEngine] 🔍 [ORDERBOOK] 验证market_data结构:
2025-07-31 10:15:41.115 [DEBUG] [ExecutionEngine]    market_data类型: <class 'core.opportunity_scanner.MarketData'>
2025-07-31 10:15:41.115 [DEBUG] [ExecutionEngine]    是否有orderbook属性: True
2025-07-31 10:15:41.115 [DEBUG] [ExecutionEngine]    orderbook类型: <class 'dict'>
2025-07-31 10:15:41.115 [DEBUG] [ExecutionEngine]    orderbook是否为dict: True
2025-07-31 10:15:41.115 [DEBUG] [ExecutionEngine] 📊 [ORDERBOOK] bybit_spot_RESOLV-USDT - asks=30, bids=30
2025-07-31 10:15:41.116 [INFO] [ExecutionEngine] ✅ [ORDERBOOK] 成功获取订单簿: bybit_spot_RESOLV-USDT
2025-07-31 10:15:41.116 [INFO] [ExecutionEngine]    数据质量: asks=30档, bids=30档
2025-07-31 10:15:41.116 [DEBUG] [ExecutionEngine] 🔍 [ORDERBOOK] 验证market_data结构:
2025-07-31 10:15:41.116 [DEBUG] [ExecutionEngine]    market_data类型: <class 'core.opportunity_scanner.MarketData'>
2025-07-31 10:15:41.116 [DEBUG] [ExecutionEngine]    是否有orderbook属性: True
2025-07-31 10:15:41.116 [DEBUG] [ExecutionEngine]    orderbook类型: <class 'dict'>
2025-07-31 10:15:41.116 [DEBUG] [ExecutionEngine]    orderbook是否为dict: True
2025-07-31 10:15:41.116 [DEBUG] [ExecutionEngine] 📊 [ORDERBOOK] gate_futures_RESOLV-USDT - asks=20, bids=20
2025-07-31 10:15:41.116 [INFO] [ExecutionEngine] ✅ [ORDERBOOK] 成功获取订单簿: gate_futures_RESOLV-USDT
2025-07-31 10:15:41.116 [INFO] [ExecutionEngine]    数据质量: asks=20档, bids=20档
2025-07-31 10:15:41.219 [INFO] [ExecutionEngine] ✅ 使用WebSocket实时订单簿数据，时间差：140.0ms，数据年龄：spot=379.6ms, futures=239.6ms
2025-07-31 10:15:41.219 [DEBUG] [ExecutionEngine] ⚡ WebSocket深度获取: 117.20ms
2025-07-31 10:15:41.219 [INFO] [ExecutionEngine] 📊 [ORDERBOOK] 分析套利深度数据: RESOLV-USDT
2025-07-31 10:15:41.219 [INFO] [ExecutionEngine]    现货深度: asks=30档, bids=30档
2025-07-31 10:15:41.220 [INFO] [ExecutionEngine]    期货深度: asks=20档, bids=20档
2025-07-31 10:15:41.220 [INFO] [ExecutionEngine] ✅ [ORDERBOOK] 套利数据可用: RESOLV-USDT
2025-07-31 10:15:41.220 [INFO] [ExecutionEngine]    买现货卖期货: 现货asks=30, 期货bids=20
2025-07-31 10:15:41.224 [DEBUG] [ExecutionEngine] ✅ 执行前验证通过: RESOLV-USDT 当前价差0.692% (验证耗时0.0ms)
2025-07-31 10:15:41.224 [INFO] [ExecutionEngine] ✅ 30档Order差价分析: 可执行价差0.791%, 滑点0.049%, 使用档位: 现货1, 期货2
2025-07-31 10:15:41.224 [INFO] [ExecutionEngine] ✅ 独立滑点风险控制通过: 总滑点0.049% 在可接受范围内
2025-07-31 10:15:41.224 [DEBUG] [ExecutionEngine] ⚡ 快速验证: 4.57ms
2025-07-31 10:15:41.224 [INFO] [ExecutionEngine] 🚀 启动并行差价锁定: 现货=173.01000000, 期货=173.00000000 (精度转换由各交易所统一处理)
2025-07-31 10:15:41.266 [DEBUG] [ExecutionEngine] 🔍 [ORDERBOOK] 验证market_data结构:
2025-07-31 10:15:41.266 [DEBUG] [ExecutionEngine]    market_data类型: <class 'core.opportunity_scanner.MarketData'>
2025-07-31 10:15:41.266 [DEBUG] [ExecutionEngine]    是否有orderbook属性: True
2025-07-31 10:15:41.266 [DEBUG] [ExecutionEngine]    orderbook类型: <class 'dict'>
2025-07-31 10:15:41.266 [DEBUG] [ExecutionEngine]    orderbook是否为dict: True
2025-07-31 10:15:41.266 [DEBUG] [ExecutionEngine] 📊 [ORDERBOOK] bybit_spot_RESOLV-USDT - asks=30, bids=30
2025-07-31 10:15:41.267 [INFO] [ExecutionEngine] ✅ [ORDERBOOK] 成功获取订单簿: bybit_spot_RESOLV-USDT
2025-07-31 10:15:41.267 [INFO] [ExecutionEngine]    数据质量: asks=30档, bids=30档
2025-07-31 10:15:41.267 [DEBUG] [ExecutionEngine] 🔍 [ORDERBOOK] 验证market_data结构:
2025-07-31 10:15:41.267 [DEBUG] [ExecutionEngine]    market_data类型: <class 'core.opportunity_scanner.MarketData'>
2025-07-31 10:15:41.267 [DEBUG] [ExecutionEngine]    是否有orderbook属性: True
2025-07-31 10:15:41.267 [DEBUG] [ExecutionEngine]    orderbook类型: <class 'dict'>
2025-07-31 10:15:41.267 [DEBUG] [ExecutionEngine]    orderbook是否为dict: True
2025-07-31 10:15:41.267 [DEBUG] [ExecutionEngine] 📊 [ORDERBOOK] gate_futures_RESOLV-USDT - asks=20, bids=20
2025-07-31 10:15:41.268 [INFO] [ExecutionEngine] ✅ [ORDERBOOK] 成功获取订单簿: gate_futures_RESOLV-USDT
2025-07-31 10:15:41.268 [INFO] [ExecutionEngine]    数据质量: asks=20档, bids=20档
2025-07-31 10:15:41.268 [INFO] [ExecutionEngine] 🔍 现货执行: bybit RESOLV-USDT 173.01
2025-07-31 10:15:41.268 [DEBUG] [ExecutionEngine] ✅ 使用预取的现货深度数据: RESOLV-USDT
2025-07-31 10:15:41.268 [INFO] [ExecutionEngine] ✅ [ORDERBOOK] 现货数据可用: RESOLV-USDT - asks=30, bids=30
2025-07-31 10:15:41.271 [INFO] [ExecutionEngine] 🔍 期货执行: gate RESOLV-USDT 173
2025-07-31 10:15:41.278 [INFO] [ExecutionEngine] 🔧 设置gate杠杆: RESOLV-USDT
2025-07-31 10:15:42.125 [ERROR] [ExecutionEngine] ❌ 现货执行失败: OpeningResult(success=False, order_id=None, executed_quantity=0.0, executed_price=0.0, error_message="开仓失败: 无效的订单结果 - {'id': '', 'status': 'failed', 'error': 'Bybit API错误: 170137: Order quantity has too many decimals.'}", execution_time_ms=856.5785884857178, params_used=OpeningOrderParams(symbol='RESOLV-USDT', side='buy', order_type='market', quantity='173.01', price=None, market_type='spot', original_quantity=173.01, original_price=None, step_size='0.001', price_step='0.01', exchange_name='bybit'))
2025-07-31 10:15:43.764 [INFO] [ExecutionEngine] ✅ 杠杆设置成功: gate RESOLV-USDT = 3x (API调用, 2486.2ms)
2025-07-31 10:15:43.841 [INFO] [ExecutionEngine] ✅ [ORDERBOOK] 期货数据可用: RESOLV-USDT - asks=20, bids=20
2025-07-31 10:15:48.391 [INFO] [ExecutionEngine] ✅ 期货执行成功: OpeningResult(success=True, order_id='242912905010387011', executed_quantity=173.0, executed_price=0.204, error_message=None, execution_time_ms=4549.5030879974365, params_used=OpeningOrderParams(symbol='RESOLV-USDT', side='sell', order_type='market', quantity='173.0000', price=None, market_type='futures', original_quantity=173, original_price=None, step_size='0.0001', price_step='0.01', exchange_name='gate'))
2025-07-31 10:15:48.475 [INFO] [ExecutionEngine] ⚡ 并行交易执行: 7250.68ms
2025-07-31 10:15:48.475 [INFO] [ExecutionEngine] 🎯 总执行时间: 7374.31ms (目标: <30ms)
2025-07-31 10:15:48.475 [WARNING] [ExecutionEngine] ⚠️ 超过30ms目标: 7374.31ms
2025-07-31 10:15:48.475 [ERROR] [ExecutionEngine] 🚨 期货成功但现货失败，执行紧急平仓期货仓位
2025-07-31 10:15:50.411 [ERROR] [ExecutionEngine] 🚨 开始紧急平仓期货仓位: RESOLV-USDT
2025-07-31 10:15:51.434 [ERROR] [ExecutionEngine] ❌ 紧急平仓期货失败: 期货平仓失败: {'id': '', 'status': 'failed', 'error': 'Exchange does not support get_orderbook'}
2025-07-31 10:15:51.434 [INFO] [ExecutionEngine] 🔥 并行执行结果: ❌失败 (总耗时: 7374.31ms)
2025-07-31 10:15:51.435 [INFO] [ExecutionEngine] 🎯 执行步骤C.1完成: 并行执行耗时 10334.13ms
2025-07-31 10:15:51.435 [ERROR] [ExecutionEngine] ❌ 执行步骤C.1失败: 并行执行失败，结束套利: RESOLV-USDT
2025-07-31 10:15:51.435 [INFO] [ExecutionEngine] ✅ 执行失败，并行控制槽位已释放: RESOLV-USDT
2025-07-31 10:15:52.470 [INFO] [ExecutionEngine] 🔧 重置ExecutionEngine状态...
2025-07-31 10:15:52.479 [WARNING] [ExecutionEngine] ⚠️ ExecutionEngine健康检查失败，执行强制重置
2025-07-31 10:15:52.479 [DEBUG] [ExecutionEngine] ✅ 执行锁已释放
2025-07-31 10:15:52.479 [WARNING] [ExecutionEngine] ⚠️ ExecutionEngine状态重置完成，但系统恢复验证失败
2025-07-31 10:16:16.546 [INFO] [ExecutionEngine] ================================================================================
2025-07-31 10:16:16.546 [INFO] [ExecutionEngine] 🚀 【套利执行开始】 RESOLV-USDT
2025-07-31 10:16:16.546 [INFO] [ExecutionEngine]    现货交易所: bybit
2025-07-31 10:16:16.546 [INFO] [ExecutionEngine]    期货交易所: okx
2025-07-31 10:16:16.546 [INFO] [ExecutionEngine]    基础数量: 172.58382642998026
2025-07-31 10:16:16.546 [INFO] [ExecutionEngine]    价差百分比: 0.74%
2025-07-31 10:16:16.547 [INFO] [ExecutionEngine] ================================================================================
2025-07-31 10:16:16.547 [INFO] [ExecutionEngine] 📊 EXECUTION_START | RESOLV-USDT | bybit -> okx | 差价=0.74%
2025-07-31 10:16:16.547 [INFO] [ExecutionEngine] 🔍 执行步骤A.0: 并行控制检查...
2025-07-31 10:16:16.547 [INFO] [ExecutionEngine] ✅ 并行控制检查通过，订单编号: ARB_RESOLV-USDT_1753949776
2025-07-31 10:16:16.547 [INFO] [ExecutionEngine] 🔍 执行步骤A.1: 更新执行状态...
2025-07-31 10:16:16.547 [INFO] [ExecutionEngine] ✅ 执行状态已更新为EXECUTING
2025-07-31 10:16:16.547 [INFO] [ExecutionEngine] 🔍 执行步骤A.2: 创建执行结果对象...
2025-07-31 10:16:16.547 [INFO] [ExecutionEngine] ✅ 执行结果对象创建完成
2025-07-31 10:16:16.547 [INFO] [ExecutionEngine] 🔍 执行步骤B.1: 开始98%对冲质量预检查...
2025-07-31 10:16:16.548 [INFO] [ExecutionEngine] 📊 6大缓存系统使用统计:
2025-07-31 10:16:16.548 [INFO] [ExecutionEngine]    - 余额缓存: 实时更新 (ArbitrageEngine)
2025-07-31 10:16:16.548 [INFO] [ExecutionEngine]    - 保证金缓存: 5分钟TTL (MarginCalculator)
2025-07-31 10:16:16.548 [INFO] [ExecutionEngine]    - 交易规则缓存: N/A个规则 (TradingRulesPreloader)
2025-07-31 10:16:16.548 [INFO] [ExecutionEngine]    - 精度缓存: 24小时TTL (TradingRulesPreloader)
2025-07-31 10:16:16.548 [INFO] [ExecutionEngine]    - 订单簿缓存: 实时更新 (WebSocket)
2025-07-31 10:16:16.548 [INFO] [ExecutionEngine]    - 对冲质量缓存: 10秒TTL (预检查)
2025-07-31 10:16:16.548 [INFO] [ExecutionEngine] 📈 缓存命中率: 100.0% (总条目: 0)
2025-07-31 10:16:16.549 [INFO] [ExecutionEngine] 🎯 统一对冲质量检查: 比例=0.9927 (✅通过)
2025-07-31 10:16:16.549 [INFO] [ExecutionEngine] ✅ 执行步骤B.1完成: 对冲质量检查通过
2025-07-31 10:16:16.549 [INFO] [ExecutionEngine] 🔍 执行步骤C.1: 开始极速并行执行...
2025-07-31 10:16:16.549 [DEBUG] [ExecutionEngine] 🔥 开始<30ms极速并行执行: 现货(bybit) + 期货(okx) RESOLV-USDT
2025-07-31 10:16:16.549 [INFO] [ExecutionEngine] 🔧 统一金额计算: $35.0 ÷ 0.202800 = 172.583826 代币
2025-07-31 10:16:16.549 [INFO] [ExecutionEngine] 🔥 智能协调开始: 原始数量=172.58382600币
2025-07-31 10:16:16.550 [INFO] [ExecutionEngine] 🔥 okx期货步长截取: 172.583826 → 172.58382币
2025-07-31 10:16:16.550 [INFO] [ExecutionEngine] 🎯 智能协调结果: 现货=172.583000币, 期货=172.583820币
2025-07-31 10:16:16.550 [INFO] [ExecutionEngine] 📊 对冲质量检查已在TradingRulesPreloader中完成（包含完美对冲逻辑）
2025-07-31 10:16:16.550 [INFO] [ExecutionEngine] ✅ 智能协调完成: 现货=172.583000币, 期货=172.583820币
2025-07-31 10:16:16.550 [INFO] [ExecutionEngine] 🔥 统一单位：返回币数量，各交易所在下单时自行转换
2025-07-31 10:16:16.550 [INFO] [ExecutionEngine] ✅ 统一架构：数量协调完成，精度转换由各交易所统一处理
2025-07-31 10:16:16.550 [INFO] [ExecutionEngine]    协调后数量: 现货=172.58300000, 期货=172.58382000
2025-07-31 10:16:16.550 [INFO] [ExecutionEngine]    对冲质量检查已在预检查阶段完成，符合MD文件统一计算要求
2025-07-31 10:16:16.550 [DEBUG] [ExecutionEngine] ⚡ 零延迟参数准备: 0.99ms
2025-07-31 10:16:16.582 [INFO] [ExecutionEngine] ✅ 开仓验证：数据快照验证通过，使用快照数据确保一致性
2025-07-31 10:16:16.582 [DEBUG] [ExecutionEngine] ✅ 快照详情: 快照降级使用: 1920.0ms > 800ms (标准阈值)
2025-07-31 10:16:16.582 [DEBUG] [ExecutionEngine] ✅ 使用数据快照，跳过新鲜度检查
2025-07-31 10:16:16.582 [DEBUG] [ExecutionEngine] ✅ 使用快照中的差价计算结果: 0.740%
2025-07-31 10:16:16.582 [DEBUG] [ExecutionEngine] ✅ 开仓验证通过: 当前价差0.740%
2025-07-31 10:16:16.582 [DEBUG] [ExecutionEngine] 🔍 [ORDERBOOK] 验证market_data结构:
2025-07-31 10:16:16.582 [DEBUG] [ExecutionEngine]    market_data类型: <class 'core.opportunity_scanner.MarketData'>
2025-07-31 10:16:16.583 [DEBUG] [ExecutionEngine]    是否有orderbook属性: True
2025-07-31 10:16:16.583 [DEBUG] [ExecutionEngine]    orderbook类型: <class 'dict'>
2025-07-31 10:16:16.583 [DEBUG] [ExecutionEngine]    orderbook是否为dict: True
2025-07-31 10:16:16.583 [DEBUG] [ExecutionEngine] 📊 [ORDERBOOK] bybit_spot_RESOLV-USDT - asks=30, bids=30
2025-07-31 10:16:16.583 [INFO] [ExecutionEngine] ✅ [ORDERBOOK] 成功获取订单簿: bybit_spot_RESOLV-USDT
2025-07-31 10:16:16.583 [INFO] [ExecutionEngine]    数据质量: asks=30档, bids=30档
2025-07-31 10:16:16.583 [DEBUG] [ExecutionEngine] 🔍 [ORDERBOOK] 验证market_data结构:
2025-07-31 10:16:16.583 [DEBUG] [ExecutionEngine]    market_data类型: <class 'core.opportunity_scanner.MarketData'>
2025-07-31 10:16:16.583 [DEBUG] [ExecutionEngine]    是否有orderbook属性: True
2025-07-31 10:16:16.583 [DEBUG] [ExecutionEngine]    orderbook类型: <class 'dict'>
2025-07-31 10:16:16.583 [DEBUG] [ExecutionEngine]    orderbook是否为dict: True
2025-07-31 10:16:16.584 [DEBUG] [ExecutionEngine] 📊 [ORDERBOOK] okx_futures_RESOLV-USDT - asks=30, bids=30
2025-07-31 10:16:16.584 [INFO] [ExecutionEngine] ✅ [ORDERBOOK] 成功获取订单簿: okx_futures_RESOLV-USDT
2025-07-31 10:16:16.584 [INFO] [ExecutionEngine]    数据质量: asks=30档, bids=30档
2025-07-31 10:16:16.644 [INFO] [ExecutionEngine] ✅ 使用WebSocket实时订单簿数据，时间差：26.0ms，数据年龄：spot=314.5ms, futures=340.5ms
2025-07-31 10:16:16.644 [DEBUG] [ExecutionEngine] ⚡ WebSocket深度获取: 93.76ms
2025-07-31 10:16:16.644 [INFO] [ExecutionEngine] 📊 [ORDERBOOK] 分析套利深度数据: RESOLV-USDT
2025-07-31 10:16:16.644 [INFO] [ExecutionEngine]    现货深度: asks=30档, bids=30档
2025-07-31 10:16:16.644 [INFO] [ExecutionEngine]    期货深度: asks=30档, bids=30档
2025-07-31 10:16:16.645 [INFO] [ExecutionEngine] ✅ [ORDERBOOK] 套利数据可用: RESOLV-USDT
2025-07-31 10:16:16.645 [INFO] [ExecutionEngine]    买现货卖期货: 现货asks=30, 期货bids=30
2025-07-31 10:16:16.645 [DEBUG] [ExecutionEngine] ✅ 执行前验证通过: RESOLV-USDT 当前价差0.740% (验证耗时0.0ms)
2025-07-31 10:16:16.645 [INFO] [ExecutionEngine] ✅ 30档Order差价分析: 可执行价差0.740%, 滑点0.000%, 使用档位: 现货1, 期货1
2025-07-31 10:16:16.645 [INFO] [ExecutionEngine] ✅ 独立滑点风险控制通过: 总滑点0.000% 在可接受范围内
2025-07-31 10:16:16.646 [DEBUG] [ExecutionEngine] ⚡ 快速验证: 1.28ms
2025-07-31 10:16:16.646 [INFO] [ExecutionEngine] 🚀 启动并行差价锁定: 现货=172.58300000, 期货=172.58382000 (精度转换由各交易所统一处理)
2025-07-31 10:16:16.685 [DEBUG] [ExecutionEngine] 🔍 [ORDERBOOK] 验证market_data结构:
2025-07-31 10:16:16.685 [DEBUG] [ExecutionEngine]    market_data类型: <class 'core.opportunity_scanner.MarketData'>
2025-07-31 10:16:16.685 [DEBUG] [ExecutionEngine]    是否有orderbook属性: True
2025-07-31 10:16:16.685 [DEBUG] [ExecutionEngine]    orderbook类型: <class 'dict'>
2025-07-31 10:16:16.685 [DEBUG] [ExecutionEngine]    orderbook是否为dict: True
2025-07-31 10:16:16.685 [DEBUG] [ExecutionEngine] 📊 [ORDERBOOK] bybit_spot_RESOLV-USDT - asks=30, bids=30
2025-07-31 10:16:16.686 [INFO] [ExecutionEngine] ✅ [ORDERBOOK] 成功获取订单簿: bybit_spot_RESOLV-USDT
2025-07-31 10:16:16.686 [INFO] [ExecutionEngine]    数据质量: asks=30档, bids=30档
2025-07-31 10:16:16.686 [DEBUG] [ExecutionEngine] 🔍 [ORDERBOOK] 验证market_data结构:
2025-07-31 10:16:16.686 [DEBUG] [ExecutionEngine]    market_data类型: <class 'core.opportunity_scanner.MarketData'>
2025-07-31 10:16:16.686 [DEBUG] [ExecutionEngine]    是否有orderbook属性: True
2025-07-31 10:16:16.686 [DEBUG] [ExecutionEngine]    orderbook类型: <class 'dict'>
2025-07-31 10:16:16.686 [DEBUG] [ExecutionEngine]    orderbook是否为dict: True
2025-07-31 10:16:16.686 [DEBUG] [ExecutionEngine] 📊 [ORDERBOOK] okx_futures_RESOLV-USDT - asks=30, bids=30
2025-07-31 10:16:16.686 [INFO] [ExecutionEngine] ✅ [ORDERBOOK] 成功获取订单簿: okx_futures_RESOLV-USDT
2025-07-31 10:16:16.687 [INFO] [ExecutionEngine]    数据质量: asks=30档, bids=30档
2025-07-31 10:16:16.687 [INFO] [ExecutionEngine] 🔍 现货执行: bybit RESOLV-USDT 172.583
2025-07-31 10:16:16.687 [DEBUG] [ExecutionEngine] ✅ 使用预取的现货深度数据: RESOLV-USDT
2025-07-31 10:16:16.687 [INFO] [ExecutionEngine] ✅ [ORDERBOOK] 现货数据可用: RESOLV-USDT - asks=30, bids=30
2025-07-31 10:16:16.689 [INFO] [ExecutionEngine] 🔍 期货执行: okx RESOLV-USDT 172.58382
2025-07-31 10:16:16.724 [INFO] [ExecutionEngine] 🔧 设置okx杠杆: RESOLV-USDT
2025-07-31 10:16:16.938 [ERROR] [ExecutionEngine] ❌ 现货执行失败: OpeningResult(success=False, order_id=None, executed_quantity=0.0, executed_price=0.0, error_message="开仓失败: 无效的订单结果 - {'id': '', 'status': 'failed', 'error': 'Bybit API错误: 170137: Order quantity has too many decimals.'}", execution_time_ms=250.22077560424805, params_used=OpeningOrderParams(symbol='RESOLV-USDT', side='buy', order_type='market', quantity='172.583', price=None, market_type='spot', original_quantity=172.583, original_price=None, step_size='0.001', price_step='0.01', exchange_name='bybit'))
2025-07-31 10:16:17.346 [INFO] [ExecutionEngine] ✅ 杠杆设置成功: okx RESOLV-USDT = 3x (API调用, 622.1ms)
2025-07-31 10:16:17.401 [INFO] [ExecutionEngine] ✅ [ORDERBOOK] 期货数据可用: RESOLV-USDT - asks=30, bids=30
2025-07-31 10:16:19.561 [INFO] [ExecutionEngine] ✅ 期货执行成功: OpeningResult(success=True, order_id='2732920537643540480', executed_quantity=172.58382, executed_price=0.2043, error_message=None, execution_time_ms=2159.1992378234863, params_used=OpeningOrderParams(symbol='RESOLV-USDT', side='sell', order_type='market', quantity='172.58382', price=None, market_type='futures', original_quantity=172.58382, original_price=None, step_size='0.00001', price_step='0.01', exchange_name='okx'))
2025-07-31 10:16:19.615 [INFO] [ExecutionEngine] ⚡ 并行交易执行: 2969.25ms
2025-07-31 10:16:19.615 [INFO] [ExecutionEngine] 🎯 总执行时间: 3065.89ms (目标: <30ms)
2025-07-31 10:16:19.615 [WARNING] [ExecutionEngine] ⚠️ 超过30ms目标: 3065.89ms
2025-07-31 10:16:19.615 [ERROR] [ExecutionEngine] 🚨 期货成功但现货失败，执行紧急平仓期货仓位
2025-07-31 10:16:21.196 [ERROR] [ExecutionEngine] 🚨 开始紧急平仓期货仓位: RESOLV-USDT
2025-07-31 10:16:22.940 [INFO] [ExecutionEngine] ✅ 紧急平仓期货成功: 2732920652097708032
2025-07-31 10:16:22.940 [INFO] [ExecutionEngine] 🔥 并行执行结果: ❌失败 (总耗时: 3065.89ms)
2025-07-31 10:16:22.941 [INFO] [ExecutionEngine] 🎯 执行步骤C.1完成: 并行执行耗时 6391.57ms
2025-07-31 10:16:22.941 [ERROR] [ExecutionEngine] ❌ 执行步骤C.1失败: 并行执行失败，结束套利: RESOLV-USDT
2025-07-31 10:16:22.941 [INFO] [ExecutionEngine] ✅ 执行失败，并行控制槽位已释放: RESOLV-USDT
2025-07-31 10:16:23.976 [INFO] [ExecutionEngine] 🔧 重置ExecutionEngine状态...
2025-07-31 10:16:23.976 [WARNING] [ExecutionEngine] ⚠️ ExecutionEngine健康检查失败，执行强制重置
2025-07-31 10:16:23.976 [DEBUG] [ExecutionEngine] ✅ 执行锁已释放
2025-07-31 10:16:23.976 [WARNING] [ExecutionEngine] ⚠️ ExecutionEngine状态重置完成，但系统恢复验证失败
