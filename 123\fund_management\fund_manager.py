"""
资金管理器
负责监控和管理各交易所的资金状态
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime
import os
from enum import Enum
from decimal import Decimal
from dotenv import load_dotenv
import time

from exchanges.exchanges_base import BaseExchange, AccountType
from fund_management.fund_transfer_service import FundTransferService
from config.settings import Settings

logger = logging.getLogger(__name__)

# 确保环境变量已加载
load_dotenv()


class FundStatus(Enum):
    """资金状态"""
    BALANCED = "balanced"  # 平衡
    IMBALANCED = "imbalanced"  # 不平衡
    INSUFFICIENT = "insufficient"  # 不足
    TRANSFERRING = "transferring"  # 转账中


class FundManager:
    """🔥 资金管理器 - 优化版，删除重复余额获取，统一使用ArbitrageEngine缓存"""

    def __init__(self, exchanges: Dict[str, Any]):
        self.exchanges = exchanges
        self.transfer_service = FundTransferService(exchanges)
        # 🔥 核心优化：不再维护独立的余额状态，统一使用ArbitrageEngine缓存
        self.logger = logging.getLogger(__name__)

        # 🔥 修复：添加缺失的属性
        self.fund_status = {}  # 资金状态缓存
        self.transfer_lock = asyncio.Lock()  # 转账锁
        self.last_check_time = 0  # 最后检查时间
        self.initial_balances = {}  # 初始余额
        self.spot_ratio = {}  # 现货比例
        self.futures_ratio = {}  # 期货比例
        self.balance_threshold = 0.1  # 平衡阈值

        # 🔥 修复：从环境变量读取配置
        self.allow_low_balance = os.getenv("ALLOW_LOW_BALANCE_TEST", "false").lower() == "true"
        self.min_required_balance = float(os.getenv("MIN_REQUIRED_BALANCE", "100.0"))

        self.logger.info("✅ 资金管理器初始化 - 🔥 优化版：统一使用ArbitrageEngine余额缓存")

    async def check_all_balances(self) -> Dict[str, Dict[str, float]]:
        """🔥 强制使用UnifiedBalanceManager - 删除所有重复余额查询逻辑"""
        try:
            # 🔥 强制使用统一余额管理器，删除所有重复逻辑
            from core.unified_balance_manager import get_unified_balance_manager
            balance_manager = get_unified_balance_manager(self.exchanges)
            
            # 获取统一格式的余额
            unified_balances = await balance_manager.get_all_balances()
            
            # 转换为FundManager期望的格式
            balance_data = self._convert_unified_to_fund_format(unified_balances)
            
            # 更新fund_status
            self.fund_status = self._convert_to_fund_status_format(balance_data)
            self.last_check_time = time.time()
            
            self.logger.info("✅ [统一模块] 余额查询: UnifiedBalanceManager | 删除重复逻辑，强制使用统一模块")
            return balance_data

        except Exception as e:
            self.logger.error(f"❌ 统一余额管理器调用失败: {e}")
            return {}

    def _convert_unified_to_fund_format(self, unified_balances: Dict[str, float]) -> Dict[str, Dict[str, float]]:
        """转换统一余额格式为FundManager格式"""
        result = {}
        
        for cache_key, balance in unified_balances.items():
            # 解析缓存键：例如 "gate_spot_usdt" -> exchange="gate", account="spot"
            parts = cache_key.split('_')
            if len(parts) >= 3:
                exchange = parts[0]
                account = parts[1]
                currency = parts[2].upper()

                if exchange not in result:
                    result[exchange] = {}
                if account not in result[exchange]:
                    result[exchange][account] = {}

                result[exchange][account][currency] = balance

        return result

    def _convert_balance_cache_format(self, balance_cache: Dict) -> Dict[str, Dict[str, float]]:
        """🔥 工具方法：转换ArbitrageEngine缓存格式为FundManager格式"""
        result = {}

        for cache_key, balance in balance_cache.items():
            # 解析缓存键：例如 "gate_spot_usdt" -> exchange="gate", account="spot"
            parts = cache_key.split('_')
            if len(parts) >= 3:
                exchange = parts[0]
                account = parts[1]
                currency = parts[2].upper()

                if exchange not in result:
                    result[exchange] = {}
                if account not in result[exchange]:
                    result[exchange][account] = {}

                result[exchange][account][currency] = balance

        return result

    def _convert_to_fund_status_format(self, balance_data: Dict) -> Dict[str, Dict[str, float]]:
        """🔥 修复：转换余额数据为fund_status格式"""
        fund_status = {}

        for exchange_name, accounts in balance_data.items():
            fund_status[exchange_name] = {}

            spot_usdt = 0.0
            futures_usdt = 0.0
            unified_usdt = 0.0

            for account_type, currencies in accounts.items():
                usdt_amount = currencies.get('USDT', 0.0)
                
                # 🔥 修复：正确处理嵌套字典结构
                if isinstance(usdt_amount, dict):
                    # 如果USDT是字典，提取available字段
                    usdt_value = float(usdt_amount.get("available", 0))
                elif isinstance(usdt_amount, (int, float)):
                    usdt_value = float(usdt_amount)
                else:
                    # 处理其他类型（如字符串）
                    try:
                        usdt_value = float(usdt_amount) if usdt_amount else 0.0
                    except (ValueError, TypeError):
                        usdt_value = 0.0

                if account_type == 'spot':
                    spot_usdt = usdt_value
                elif account_type == 'futures':
                    futures_usdt = usdt_value
                elif account_type == 'unified':
                    unified_usdt = usdt_value

            # 🔥 修复：安全比较，确保都是数值类型
            unified_amount = float(unified_usdt) if unified_usdt else 0.0
            spot_amount = float(spot_usdt) if spot_usdt else 0.0
            futures_amount = float(futures_usdt) if futures_usdt else 0.0
            
            # 根据账户类型设置fund_status
            if unified_amount > 0:
                # 统一账户
                fund_status[exchange_name] = {
                    'unified_usdt': unified_amount,
                    'total_usdt': unified_amount
                }
            else:
                # 分离账户
                fund_status[exchange_name] = {
                    'spot_usdt': spot_amount,
                    'futures_usdt': futures_amount,
                    'total_usdt': spot_amount + futures_amount
                }

        return fund_status

    async def check_and_adjust_balance(self) -> bool:
        """🔥 优化版：基于ArbitrageEngine缓存检查并调整资金平衡"""
        try:
            # 🔥 使用ArbitrageEngine的余额缓存
            balances = await self.check_all_balances()
            
            if not balances:
                self.logger.warning("⚠️ 无法获取余额信息，跳过平衡调整")
                return False
                
            # 检查是否需要调整（简化逻辑）
            needs_adjustment = self._check_balance_needs_adjustment(balances)
            
            if needs_adjustment:
                self.logger.info("🔄 检测到资金不平衡，开始调整...")
                return await self._perform_balance_adjustment(balances)
            else:
                self.logger.info("✅ 资金平衡状态良好，无需调整")
                return True
                
        except Exception as e:
            self.logger.error(f"❌ 资金平衡检查失败: {e}")
            return False

    def _check_balance_needs_adjustment(self, balances: Dict) -> bool:
        """🔥 修复类型错误：正确处理余额数据类型"""
        try:
            # 简化逻辑：只检查关键交易所的USDT余额
            key_exchanges = ["gate", "bybit", "okx"]
            usdt_balances = []
            
            for exchange in key_exchanges:
                if exchange in balances:
                    total_usdt = 0.0
                    for account, currencies in balances[exchange].items():
                        try:
                            # 🔥 修复：安全提取USDT数值，处理不同数据格式
                            if isinstance(currencies, dict):
                                usdt_value = currencies.get("USDT", 0)
                                # 进一步检查USDT值的类型
                                if isinstance(usdt_value, dict):
                                    # 如果USDT是字典，提取available字段
                                    usdt_amount = float(usdt_value.get("available", 0))
                                elif isinstance(usdt_value, (int, float, str)):
                                    # 🔥 关键修复：安全转换数值类型，处理空字符串
                                    usdt_amount = float(usdt_value) if usdt_value != "" else 0.0
                                else:
                                    # 🔥 关键修复：记录未知格式并设为0
                                    self.logger.warning(f"⚠️ {exchange} {account} USDT格式未知: {type(usdt_value)} = {usdt_value}")
                                    usdt_amount = 0.0
                            else:
                                usdt_amount = 0.0
                            
                            total_usdt += usdt_amount
                            
                        except (TypeError, ValueError, AttributeError) as e:
                            self.logger.warning(f"解析{exchange} {account}余额失败: {e}")
                            continue
                    
                    usdt_balances.append(total_usdt)
                    self.logger.debug(f"{exchange}总USDT余额: {total_usdt:.2f}")
            
            if len(usdt_balances) < 2:
                self.logger.debug("可用交易所数量不足，无需平衡调整")
                return False
                
            # 如果最大值和最小值差异超过20%，则需要调整
            max_balance = max(usdt_balances)
            min_balance = min(usdt_balances)
            
            if max_balance > 0:
                ratio = (max_balance - min_balance) / max_balance
                need_adjustment = ratio > 0.2
                self.logger.debug(f"余额平衡检查: 最大{max_balance:.2f}, 最小{min_balance:.2f}, "
                                f"差异比例{ratio:.1%}, 需要调整: {need_adjustment}")
                return need_adjustment
                
            return False
            
        except Exception as e:
            self.logger.error(f"余额平衡检查失败: {e}")
            return False

    async def _perform_balance_adjustment(self, balances: Dict) -> bool:
        """🔥 简化版资金调整逻辑"""
        try:
            # 实际的资金调整逻辑（简化版）
            self.logger.info("🔄 执行资金平衡调整...")
            
            # 使用transfer_service进行调整
            # 这里可以添加具体的调整逻辑
            
            self.logger.info("✅ 资金平衡调整完成")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 资金调整失败: {e}")
            return False

    # 🔥 删除重复方法：get_balance_summary
    # 统一使用 UnifiedBalanceManager.get_balance_summary()
    
    async def get_balance_summary(self) -> Dict[str, Any]:
        """🔥 简化版：直接使用ArbitrageEngine余额缓存"""
        try:
            from core.arbitrage_engine import get_arbitrage_engine
            engine = get_arbitrage_engine()

            if engine and hasattr(engine, 'balance_cache') and engine.balance_cache:
                total_usdt = sum(balance for balance in engine.balance_cache.values() if isinstance(balance, (int, float)))
                return {
                    "total_usdt": total_usdt,
                    "balances": engine.balance_cache,
                    "last_updated": time.time(),
                    "source": "ArbitrageEngine缓存"
                }
            else:
                return {
                    "total_usdt": 0.0,
                    "balances": {},
                    "last_updated": time.time(),
                    "source": "缓存为空"
                }
        except Exception as e:
            self.logger.error(f"获取余额汇总失败: {e}")
            return {
                "total_usdt": 0.0,
                "balances": {},
                "last_updated": time.time(),
                "error": str(e)
            }

    # 🔥 保留必要的工具方法，但简化实现
    async def restore_balance(self) -> None:
        """恢复初始资金平衡状态"""
        await self.check_and_adjust_balance()

    async def adjust_to_initial_balance(self) -> bool:
        """调整到初始平衡状态"""
        return await self.check_and_adjust_balance()

    async def initialize(self):
        """初始化资金状态"""
        logger.info("开始初始化资金状态...")

        # 检查当前资金状态
        await self.check_all_balances()

        # 调整到初始平衡状态
        await self.adjust_to_initial_balance()

        logger.info("资金状态初始化完成")

    async def check_fund_sufficiency(self, exchange_name: str,
                                   account_type: AccountType,
                                   required_amount: float) -> bool:
        """
        🔥 强制使用UnifiedBalanceManager - 删除所有重复API调用
        """
        try:
            # 低余额测试模式
            if self.allow_low_balance:
                # 在测试模式下，如果设置了较低的资金需求，可以放宽检查
                if required_amount <= self.min_required_balance:
                    logger.info(f"{exchange_name}启用低余额测试模式，资金充足性检查通过")
                    return True

            # 🔥 强制使用统一余额管理器，删除所有重复API调用
            from core.unified_balance_manager import get_unified_balance_manager
            balance_manager = get_unified_balance_manager(self.exchanges)
            
            # 获取指定交易所和账户的余额
            available = balance_manager.get_exchange_balance(exchange_name, account_type.value.lower())
            
            # 🔥 新增：添加安全边际，避免边界情况下余额不足
            safety_margin = max(required_amount * 0.05, 1.0)  # 5%安全边际，最少1 USDT
            effective_required = required_amount + safety_margin
            
            is_sufficient = available >= effective_required
            
            logger.info(f"✅ [统一模块] 资金充足性: UnifiedBalanceManager | {exchange_name} {account_type.value}账户: "
                       f"可用=${available:.2f}, 需求=${required_amount:.2f}, "
                       f"含边际=${effective_required:.2f}, 充足={is_sufficient}")
            
            return is_sufficient

        except Exception as e:
            logger.error(f"检查{exchange_name}资金充足性失败: {e}")
            return False

    async def prepare_arbitrage_funds(self, opportunity) -> bool:
        """
        为套利准备资金 - 🔥 修复：接受opportunity参数
        :param opportunity: 套利机会对象
        :return: 是否准备成功
        """
        try:
            # 🔥 修复：安全获取配置，避免config.get()错误
            min_order_amount = 50.0  # 默认值
            if hasattr(self, 'config') and self.config and hasattr(self.config, 'get'):
                min_order_amount = float(self.config.get('MIN_ORDER_AMOUNT_USD', '50.0'))
            elif hasattr(self, 'settings') and self.settings:
                # 尝试从settings获取
                min_order_amount = getattr(self.settings, 'MIN_ORDER_AMOUNT_USD', 50.0)
            else:
                # 🔥 删除重复环境变量读取：使用配置管理器
                from config.settings import get_config
                config = get_config()
                min_order_amount = config.MIN_ORDER_AMOUNT_USD
            
            # 将opportunity转换为组合列表格式
            combinations = [{
                "spot_exchange": getattr(opportunity, 'buy_exchange', 'unknown'),
                "futures_exchange": getattr(opportunity, 'sell_exchange', 'unknown'),
                "amount": min_order_amount
            }]
            
            logger.info(f"为套利机会准备资金: {getattr(opportunity, 'symbol', 'UNKNOWN')} "
                       f"{combinations[0]['spot_exchange']}→{combinations[0]['futures_exchange']}, "
                       f"金额=${min_order_amount}")

            # 智能锁使用
            if isinstance(self.transfer_lock, asyncio.Lock):
                async with self.transfer_lock:
                    return await self._prepare_arbitrage_funds_impl(combinations)
            else:
                with self.transfer_lock:
                    return await self._prepare_arbitrage_funds_impl(combinations)
                    
        except Exception as e:
            logger.error(f"准备套利资金失败: {e}")
            return False

    async def _prepare_arbitrage_funds_impl(self, combinations: List[Dict]) -> bool:
        """为套利准备资金的实现 - 🔥 修复：真正执行资金调整"""
        try:
            adjustment_needed = False
            failed_adjustments = []
            
            # 检查每个组合需要的资金
            for combo in combinations:
                spot_exchange = combo["spot_exchange"]
                futures_exchange = combo["futures_exchange"]
                amount = combo["amount"]

                logger.info(f"检查资金充足性: {spot_exchange}现货需要${amount:.2f}, {futures_exchange}期货需要${amount:.2f}")

                # 检查现货资金
                spot_sufficient = await self.check_fund_sufficiency(
                    spot_exchange, AccountType.SPOT, amount
                )

                # 检查期货资金
                futures_sufficient = await self.check_fund_sufficiency(
                    futures_exchange, AccountType.FUTURES, amount
                )

                logger.info(f"资金充足性检查结果: {spot_exchange}现货={spot_sufficient}, {futures_exchange}期货={futures_sufficient}")

                # 🔥 关键修复：如果资金不足，真正执行调整
                if not spot_sufficient or not futures_sufficient:
                    adjustment_needed = True
                    
                    if self.allow_low_balance:
                        logger.warning(f"低余额测试模式: 资金不足但允许继续 {spot_exchange}现货={spot_sufficient}, "
                                     f"{futures_exchange}期货={futures_sufficient}")
                        continue
                    
                    logger.warning(f"💰 检测到资金不足，开始执行资金调整...")
                    
                    # 🔥 重要：先执行全局资金平衡调整
                    try:
                        logger.info("🔄 执行全局资金平衡调整...")
                        balance_success = await self.adjust_to_initial_balance()
                        if balance_success:
                            logger.info("✅ 全局资金平衡调整完成，重新检查充足性...")
                            
                            # 重新检查资金充足性
                            spot_sufficient_after = await self.check_fund_sufficiency(
                                spot_exchange, AccountType.SPOT, amount
                            )
                            futures_sufficient_after = await self.check_fund_sufficiency(
                                futures_exchange, AccountType.FUTURES, amount
                            )
                            
                            logger.info(f"调整后资金充足性: {spot_exchange}现货={spot_sufficient_after}, {futures_exchange}期货={futures_sufficient_after}")
                            
                            if spot_sufficient_after and futures_sufficient_after:
                                logger.info(f"✅ {spot_exchange}+{futures_exchange} 资金调整成功")
                                continue
                            else:
                                logger.warning(f"⚠️ {spot_exchange}+{futures_exchange} 调整后仍然不足")
                        else:
                            logger.error("❌ 全局资金平衡调整失败")
                            failed_adjustments.append(f"{spot_exchange}+{futures_exchange}")
                            
                    except Exception as adjust_error:
                        logger.error(f"资金调整过程异常: {adjust_error}")
                        failed_adjustments.append(f"{spot_exchange}+{futures_exchange} (异常: {adjust_error})")
                        
                    # 🔥 新增：如果全局调整失败，尝试针对性调整
                    if not spot_sufficient:
                        logger.info(f"尝试针对性调整{spot_exchange}现货资金...")
                        try:
                            await self._targeted_fund_adjustment(spot_exchange, AccountType.SPOT, amount)
                        except Exception as e:
                            logger.error(f"{spot_exchange}现货资金调整失败: {e}")
                            failed_adjustments.append(f"{spot_exchange}现货")
                    
                    if not futures_sufficient:
                        logger.info(f"尝试针对性调整{futures_exchange}期货资金...")
                        try:
                            await self._targeted_fund_adjustment(futures_exchange, AccountType.FUTURES, amount)
                        except Exception as e:
                            logger.error(f"{futures_exchange}期货资金调整失败: {e}")
                            failed_adjustments.append(f"{futures_exchange}期货")

            # 🔥 修复：根据实际调整结果决定返回值
            if not adjustment_needed:
                logger.info("✅ 所有资金充足，无需调整")
                return True
            elif len(failed_adjustments) == 0:
                logger.info("✅ 所有资金调整成功")
                return True
            elif self.allow_low_balance:
                logger.warning(f"⚠️ 低余额测试模式：部分调整失败但允许继续: {failed_adjustments}")
                return True
            else:
                logger.error(f"❌ 资金调整失败: {failed_adjustments}")
                return False

        except Exception as e:
            logger.error(f"准备套利资金实现失败: {e}")
            return False
            
    async def _targeted_fund_adjustment(self, exchange_name: str, account_type: AccountType, required_amount: float):
        """针对性的资金调整 - 🔥 新增：为特定交易所和账户类型调整资金"""
        try:
            exchange = self.exchanges.get(exchange_name)
            if not exchange:
                logger.error(f"找不到交易所: {exchange_name}")
                return False
                
            if exchange.is_unified_account():
                logger.info(f"{exchange_name}是统一账户，无需内部资金调整")
                return True
                
            # 🔥 强制使用统一余额管理器，删除重复API调用
            from core.unified_balance_manager import get_unified_balance_manager
            balance_manager = get_unified_balance_manager(self.exchanges)
            
            # 获取当前余额
            current_available = balance_manager.get_exchange_balance(exchange_name, account_type.value.lower())
            
            if current_available >= required_amount:
                logger.info(f"✅ [统一模块] 资金调整: UnifiedBalanceManager | {exchange_name} {account_type.value}已有足够资金: ${current_available:.2f} >= ${required_amount:.2f}")
                return True
                
            # 计算需要转入的金额
            needed_amount = required_amount - current_available + 10  # 加10美元缓冲
            
            # 确定转账方向
            if account_type == AccountType.SPOT:
                # 需要给现货账户转钱，从期货转入
                from_account = AccountType.FUTURES
                to_account = AccountType.SPOT
            else:
                # 需要给期货账户转钱，从现货转入
                from_account = AccountType.SPOT
                to_account = AccountType.FUTURES
                
            logger.info(f"执行针对性资金调整: {exchange_name} {from_account.value}→{to_account.value} ${needed_amount:.2f}")
            
            # 执行转账
            success = await self.transfer_service.transfer(
                exchange_name, "USDT", needed_amount, from_account, to_account
            )
            
            if success:
                logger.info(f"✅ {exchange_name}针对性资金调整成功")
                return True
            else:
                logger.error(f"❌ {exchange_name}针对性资金调整失败")
                return False
                
        except Exception as e:
            logger.error(f"针对性资金调整异常: {e}")
            return False

    async def restore_balance_after_arbitrage(self):
        """套利后恢复资金平衡"""
        logger.info("套利完成，恢复资金平衡...")

        # 重新检查余额
        await self.check_all_balances()

        # 调整到平衡状态
        await self.adjust_to_initial_balance()

    async def monitor_fund_balance(self):
        """监控资金平衡状态"""
        check_interval = float(getattr(self.settings.system, "balance_check_interval", 3600))
        while True:
            try:
                await asyncio.sleep(check_interval)
                logger.info("定期检查资金平衡状态...")
                await self.check_all_balances()
                # 检查是否需要重新平衡
                for exchange_name, status in self.fund_status.items():
                    if self.exchanges[exchange_name].is_unified_account():
                        continue
                    spot_usdt = status.get("spot_usdt", 0)
                    futures_usdt = status.get("futures_usdt", 0)
                    total_usdt = spot_usdt + futures_usdt
                    if total_usdt > 0:
                        spot_ratio_actual = spot_usdt / total_usdt
                        spot_ratio_target = self.spot_ratio.get(exchange_name, 0.5)
                        if abs(spot_ratio_actual - spot_ratio_target) > self.balance_threshold:
                            logger.warning(f"{exchange_name}资金比例失衡: "
                                         f"实际{spot_ratio_actual:.2%}, "
                                         f"目标{spot_ratio_target:.2%}")
                            await self.adjust_to_initial_balance()
                            break
            except Exception as e:
                logger.error(f"资金平衡监控异常: {e}")

    def get_fund_status(self) -> Dict:
        """获取资金状态"""
        return {
            "status": self.fund_status,
            "last_check": self.last_check_time,
            "initial_balances": self.initial_balances,
            "spot_ratios": self.spot_ratio,
            "futures_ratios": self.futures_ratio
        }

    def check_total_funds_sufficient(self) -> Tuple[bool, float]:
        """
        检查总资金是否充足
        :return: (是否充足, 当前总资金)
        """
        total_funds = 0.0
        for status in self.fund_status.values():
            # 安全地提取total_usdt，确保类型正确
            usdt_value = status.get("total_usdt", 0)
            if isinstance(usdt_value, (int, float)):
                total_funds += float(usdt_value)
            else:
                logger.warning(f"Invalid total_usdt type: {type(usdt_value)}, value: {usdt_value}")
                # 尝试从其他字段计算
                if "spot_usdt" in status and "futures_usdt" in status:
                    spot_usdt = float(status.get("spot_usdt", 0))
                    futures_usdt = float(status.get("futures_usdt", 0))
                    total_funds += spot_usdt + futures_usdt

        required_funds = sum(float(balance) for balance in self.initial_balances.values())

        # 在低余额测试模式下，只要有足够满足测试的资金即可
        if self.allow_low_balance:
            is_sufficient = total_funds >= self.min_required_balance
            logger.info(f"低余额测试模式: 当前总资金={total_funds}，最小要求={self.min_required_balance}")
        else:
            is_sufficient = total_funds >= required_funds * 0.9  # 90%即可

        return is_sufficient, total_funds



    async def restore_balance(self):
        """恢复资金平衡 - ArbitrageEngine调用接口"""
        logger.info("恢复资金平衡...")
        await self.restore_balance_after_arbitrage()

    async def get_all_balances(self) -> Dict[str, Dict]:
        """获取所有交易所余额 - ArbitrageEngine调用接口"""
        try:
            logger.info("获取所有交易所余额...")
            await self.check_all_balances()
            return self.fund_status
        except Exception as e:
            logger.error(f"获取所有余额失败: {e}")
            return {}

    async def cleanup(self):
        """清理资源 - ArbitrageEngine调用接口"""
        logger.info("清理资金管理器资源...")
        try:
            # 停止监控任务等
            if hasattr(self, 'monitor_task'):
                self.monitor_task.cancel()

            # 关闭划转服务
            if hasattr(self.transfer_service, 'cleanup'):
                await self.transfer_service.cleanup()

            logger.info("资金管理器资源清理完成")
        except Exception as e:
            logger.error(f"清理资金管理器资源失败: {e}")

    async def restore_initial_balance(self) -> bool:
        """🔥 新增：资金恢复调整（1000ms） - 回到初始平衡状态"""
        try:
            restore_start = time.time()
            logger.info("开始资金恢复调整...")
            
            # 智能锁使用
            if isinstance(self.transfer_lock, asyncio.Lock):
                async with self.transfer_lock:
                    return await self._restore_initial_balance_impl()
            else:
                with self.transfer_lock:
                    return await self._restore_initial_balance_impl()
                    
        except Exception as e:
            restore_time = (time.time() - restore_start) * 1000 if 'restore_start' in locals() else 0
            logger.error(f"资金恢复调整失败: {e}, 用时: {restore_time:.1f}ms")
            return False

    async def _restore_initial_balance_impl(self) -> bool:
        """资金恢复调整的实现"""
        try:
            # 重新检查所有余额
            await self.check_all_balances()
            
            # 调整到初始平衡状态  
            result = await self.adjust_to_initial_balance()
            
            if result:
                logger.info("✅ 资金恢复调整成功")
            else:
                logger.warning("⚠️ 资金恢复调整部分失败")
                
            return result
            
        except Exception as e:
            logger.error(f"资金恢复调整实现失败: {e}")
            return False


# 测试代码
if __name__ == "__main__":
    from exchanges import GateExchange, BybitExchange, OKXExchange

    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


    async def get_balance_summary(self) -> Dict[str, Any]:
        """
        获取余额汇总 - 全流程工作流阶段5要求的方法
        """
        summary = {
            "timestamp": time.time(),
            "exchanges": {},
            "total_usdt": 0.0,
            "initial_total": sum(self.initial_balances.values()),
            "deviation_percentage": 0.0
        }
        
        total_current = 0.0
        
        for exchange_name in self.exchanges.keys():
            if exchange_name in self.fund_status:
                status = self.fund_status[exchange_name]
                exchange_total = status.get("total_usdt", 0.0)
                total_current += exchange_total
                
                summary["exchanges"][exchange_name] = {
                    "total_usdt": exchange_total,
                    "initial_balance": self.initial_balances.get(exchange_name, 0.0),
                    "deviation": exchange_total - self.initial_balances.get(exchange_name, 0.0)
                }
        
        summary["total_usdt"] = total_current
        summary["deviation_percentage"] = ((total_current - summary["initial_total"]) / summary["initial_total"] * 100) if summary["initial_total"] > 0 else 0.0
        
        return summary

    async def test():
        # 创建交易所实例
        # 🔥 删除重复环境变量读取：使用配置管理器
        from config.settings import get_config
        config = get_config()

        exchanges = {
            "gate": GateExchange(
                config.GATE_API_KEY,
                config.GATE_API_SECRET
            ),
            "bybit": BybitExchange(
                config.BYBIT_API_KEY,
                config.BYBIT_API_SECRET
            ),
            "okx": OKXExchange(
                config.OKX_API_KEY,
                config.OKX_API_SECRET,
                config.OKX_API_PASSPHRASE
            )
        }

        # 创建Settings实例
        settings = Settings()

        # 创建资金管理器
        manager = FundManager(exchanges, settings)

        # 初始化
        await manager.initialize()

        # 获取状态
        status = manager.get_fund_status()
        print(f"资金状态: {status}")

        # 检查总资金
        sufficient, total = manager.check_total_funds_sufficient()
        print(f"总资金: {total:.2f} USDT, 是否充足: {sufficient}")

        # 关闭连接
        for exchange in exchanges.values():
            await exchange.close()

    asyncio.run(test())

    async def restore_balance_unified(self) -> bool:
        """🔥 统一的资金恢复接口 - 修复重复调用"""
        try:
            self.logger.info("🔄 执行统一资金恢复...")
            return await self.restore_initial_balance()
        except Exception as e:
            self.logger.error(f"统一资金恢复失败: {e}")
            return False
