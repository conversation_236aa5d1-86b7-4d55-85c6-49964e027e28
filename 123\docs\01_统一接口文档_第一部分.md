# 🔥 通用期货溢价套利系统 - 统一接口文档（第一部分：基础接口）

## 📋 文档概述

本文档定义了通用期货溢价套利系统中所有核心模块的统一接口规范，确保三个交易所（Gate.io、Bybit、OKX）使用完全一致的接口标准。

## 🎯 核心设计原则

- **统一接口**: 所有交易所使用相同的方法签名
- **零重复**: 消除重复代码，统一实现逻辑
- **类型安全**: 明确的参数类型和返回值定义
- **异步优先**: 所有IO操作使用异步接口

## 📊 1. 交易所基类接口 (BaseExchange)

### 1.1 核心交易接口

```python
# 文件位置: exchanges/exchanges_base.py

class BaseExchange(ABC):
    """交易所基类 - 定义统一接口标准"""
    
    @abstractmethod
    async def place_order(
        self,
        symbol: str,
        side: OrderSide,
        order_type: OrderType,
        amount: float,
        price: Optional[float] = None,
        market_type: str = "spot",
        params: Optional[Dict] = None
    ) -> Dict[str, Any]:
        """
        统一下单接口
        
        Args:
            symbol: 交易对 (如: BTC-USDT)
            side: 买卖方向 (OrderSide.BUY/SELL)
            order_type: 订单类型 (OrderType.MARKET)
            amount: 交易数量
            price: 价格 (市价单为None)
            market_type: 市场类型 ("spot"/"futures")
            params: 交易所特定参数
            
        Returns:
            Dict[str, Any]: 订单信息
            {
                "order_id": str,
                "symbol": str,
                "side": str,
                "amount": float,
                "price": float,
                "status": str,
                "timestamp": int
            }
        """
        pass
    
    @abstractmethod
    async def get_balance(self, account_type: AccountType) -> Dict[str, float]:
        """
        统一余额查询接口
        
        Args:
            account_type: 账户类型 (AccountType.SPOT/FUTURES/UNIFIED)
            
        Returns:
            Dict[str, float]: 余额信息
            {
                "USDT": 1000.0,
                "BTC": 0.5,
                ...
            }
        """
        pass
    
    @abstractmethod
    async def get_position(self, symbol: str = None) -> List[Dict[str, Any]]:
        """
        统一持仓查询接口
        
        Args:
            symbol: 交易对 (可选，为None时查询所有持仓)
            
        Returns:
            List[Dict[str, Any]]: 持仓信息列表
            [{
                "symbol": str,
                "side": str,
                "size": float,
                "entry_price": float,
                "mark_price": float,
                "unrealized_pnl": float
            }]
        """
        pass
    
    @abstractmethod
    async def close_position(
        self, 
        symbol: str, 
        amount: Optional[float] = None
    ) -> Dict[str, Any]:
        """
        统一平仓接口
        
        Args:
            symbol: 交易对
            amount: 平仓数量 (None表示全部平仓)
            
        Returns:
            Dict[str, Any]: 平仓结果
        """
        pass
    
    @abstractmethod
    async def set_leverage(self, symbol: str, leverage: int) -> bool:
        """
        统一杠杆设置接口
        
        Args:
            symbol: 交易对
            leverage: 杠杆倍数
            
        Returns:
            bool: 设置是否成功
        """
        pass
```

### 1.2 辅助接口

```python
    def get_stats(self) -> Dict[str, Any]:
        """获取交易所统计信息"""
        return {
            "name": self.name,
            "request_count": self.request_count,
            "error_count": self.error_count,
            "success_rate": (self.request_count - self.error_count) / max(self.request_count, 1)
        }
    
    async def _rate_limit(self) -> None:
        """请求频率限制"""
        pass
```

## 🎯 2. 统一管理器接口

### 2.1 统一开仓管理器 (UnifiedOpeningManager)

```python
# 文件位置: core/unified_opening_manager.py

class UnifiedOpeningManager:
    """统一开仓管理器 - 处理所有开仓逻辑"""
    
    async def unified_market_buy(
        self,
        symbol: str,
        quantity: float,
        exchange,
        market_type: str = "spot",
        orderbook: Optional[Dict] = None
    ) -> OpeningResult:
        """
        统一市价买入接口
        
        Args:
            symbol: 交易对
            quantity: 购买数量
            exchange: 交易所实例
            market_type: 市场类型
            orderbook: 订单簿数据 (用于深度检查)
            
        Returns:
            OpeningResult: 开仓结果
        """
        pass
    
    async def unified_market_sell_open(
        self,
        symbol: str,
        quantity: float,
        exchange,
        market_type: str = "spot",
        orderbook: Optional[Dict] = None
    ) -> OpeningResult:
        """
        统一市价卖出开仓接口
        
        Args:
            symbol: 交易对
            quantity: 卖出数量
            exchange: 交易所实例
            market_type: 市场类型
            orderbook: 订单簿数据
            
        Returns:
            OpeningResult: 开仓结果
        """
        pass
```

### 2.2 统一平仓管理器 (UnifiedClosingManager)

```python
# 文件位置: core/unified_closing_manager.py

class UnifiedClosingManager:
    """统一平仓管理器 - 处理所有平仓逻辑"""
    
    async def close_position_unified(
        self,
        symbol: str,
        exchange,
        market_type: str,
        side: str = None,
        orderbook: Dict = None
    ) -> ClosingResult:
        """
        统一平仓接口
        
        Args:
            symbol: 交易对
            exchange: 交易所实例
            market_type: 市场类型
            side: 平仓方向 (现货需要)
            orderbook: 订单簿数据
            
        Returns:
            ClosingResult: 平仓结果
        """
        pass
    
    async def emergency_close_all(
        self,
        exchange,
        symbols: List[str]
    ) -> List[ClosingResult]:
        """
        紧急平仓所有仓位 - 实际存在的方法

        Args:
            exchange: 交易所实例
            symbols: 需要平仓的交易对列表

        Returns:
            List[ClosingResult]: 平仓结果列表
        """
        pass
```

---

**📝 注意**: 本文档为第一部分，描述基础接口和统一管理器。完整接口规范请参考其他部分文档。
