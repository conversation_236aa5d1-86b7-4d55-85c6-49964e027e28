{"basic_core_tests": {"universal_token_system": {"success": true, "supported_symbols_count": 10, "test_time": 1753858424.012295}, "trading_rules_preloader": {"success": true, "cache_stats": {"trading_rules_count": 0, "hedge_quality_cache_count": 0, "contract_info_cache_count": 0, "unsupported_pairs_count": 0, "cache_hits": 0, "cache_misses": 0, "api_calls": 0, "errors": 0, "expired_cache_entries": 0, "cache_hit_rate": 0.0, "last_preload_time": 0.0, "cache_ttl_hours": 24, "total_rules_loaded": 0}, "has_test_rule": false, "step_size": null, "test_time": 1753858424.0144508}, "currency_adapter": {"success": true, "conversion_results": {"bybit_spot": {"input": "BTC-USDT", "output": "BTCUSDT", "expected": "BTCUSDT", "correct": true}, "gate_spot": {"input": "BTC-USDT", "output": "BTC_USDT", "expected": "BTC_USDT", "correct": true}, "okx_spot": {"input": "BTC-USDT", "output": "BTC-USDT", "expected": "BTC-USDT", "correct": true}, "okx_futures": {"input": "BTC-USDT", "output": "BTC-USDT-SWAP", "expected": "BTC-USDT-SWAP", "correct": true}}, "normalize_results": {"BTCUSDT": "BTC-USDT", "BTC_USDT": "BTC-USDT", "BTC-USDT": "BTC-USDT"}, "test_time": 1753858424.0144508}, "environment_config": {"success": true, "config_status": {"TARGET_SYMBOLS": {"exists": true, "value": "SPK-USDT,RESOLV-USDT,ICNT-USDT,CAKE-USDT,WIF-USDT,AI16Z-USDT,SOL-USDT,MATIC-USDT,DOT-USDT,JUP-USDT", "valid": true}, "TRADING_RULES_TTL": {"exists": true, "value": "86400", "valid": true}, "HEDGE_QUALITY_TTL": {"exists": true, "value": "10", "valid": true}, "PRECISION_CACHE_TTL": {"exists": true, "value": "3600", "valid": true}, "MIN_ORDER_AMOUNT_USD": {"exists": true, "value": "35.0", "valid": true}, "MAX_ORDER_AMOUNT_USD": {"exists": true, "value": "110.0", "valid": true}}, "test_time": 1753858424.0144508}}, "complex_system_tests": {"multi_exchange_consistency": {"success": true, "consistency_results": {"bybit_spot": {"exchange_symbol": "BTCUSDT", "is_valid": true, "has_symbol": true}, "bybit_futures": {"exchange_symbol": "BTCUSDT", "is_valid": true, "has_symbol": true}, "gate_spot": {"exchange_symbol": "BTC_USDT", "is_valid": true, "has_symbol": true}, "gate_futures": {"exchange_symbol": "BTC_USDT", "is_valid": true, "has_symbol": true}, "okx_spot": {"exchange_symbol": "BTC-USDT", "is_valid": true, "has_symbol": true}, "okx_futures": {"exchange_symbol": "BTC-USDT-SWAP", "is_valid": true, "has_symbol": true}}, "test_time": 1753858424.0144508}, "multi_token_switching": {"success": true, "switching_results": {"SPK-USDT": {"base_currency": "SPK", "quote_currency": "USDT", "min_amount": 0.0007, "valid_base": true, "valid_quote": true, "valid_min_amount": true}, "RESOLV-USDT": {"base_currency": "RESOLV", "quote_currency": "USDT", "min_amount": 0.0007, "valid_base": true, "valid_quote": true, "valid_min_amount": true}, "ICNT-USDT": {"base_currency": "ICNT", "quote_currency": "USDT", "min_amount": 0.0007, "valid_base": true, "valid_quote": true, "valid_min_amount": true}, "CAKE-USDT": {"base_currency": "CAKE", "quote_currency": "USDT", "min_amount": 0.0007, "valid_base": true, "valid_quote": true, "valid_min_amount": true}, "WIF-USDT": {"base_currency": "WIF", "quote_currency": "USDT", "min_amount": 0.0007, "valid_base": true, "valid_quote": true, "valid_min_amount": true}}, "test_symbols_count": 5, "test_time": 1753858424.0166044}, "module_interaction": {"success": false, "interaction_results": {"token_to_adapter": {"input": "BTC-USDT", "output": "BTCUSDT", "success": true}, "token_support_check": {"symbol": "BTC-USDT", "is_supported": true, "success": true}, "preloader_step_size": {"step_size": null, "success": false}}, "test_time": 1753858424.0166044}, "data_flow_integrity": {"success": true, "data_flow_results": {"env_config": {"raw_symbols": "SPK-USDT,RESOLV-USDT,ICNT-USDT,CAKE-USDT,WIF-USDT,AI16Z-USDT,SOL-USDT,MATIC-USDT,DOT-USDT,JUP-USDT", "parsed_count": 10, "success": true}, "token_system": {"supported_count": 10, "success": true}, "currency_adapter": {"normalized_symbols": ["SPK-USDT", "RESOLV-USDT", "ICNT-USDT"], "success": true}, "preloader": {"cache_stats": {"trading_rules_count": 0, "hedge_quality_cache_count": 0, "contract_info_cache_count": 0, "unsupported_pairs_count": 0, "cache_hits": 0, "cache_misses": 3, "api_calls": 0, "errors": 0, "expired_cache_entries": 0, "cache_hit_rate": 0.0, "last_preload_time": 0.0, "cache_ttl_hours": 24, "total_rules_loaded": 0}, "success": true}}, "test_time": 1753858424.0166044}}, "production_simulation_tests": {"real_api_simulation": {"success": true, "api_simulation_results": {"bybit_BTC-USDT_spot": {"has_rule": false, "step_size": null, "call_time_ms": 1.1823177337646484, "performance_ok": true}, "gate_ETH-USDT_spot": {"has_rule": false, "step_size": null, "call_time_ms": 1.0004043579101562, "performance_ok": true}, "okx_SOL-USDT_futures": {"has_rule": false, "step_size": null, "call_time_ms": 1.1227130889892578, "performance_ok": true}}, "avg_call_time_ms": 1.1018117268880208, "test_time": 1753858424.0199099}, "network_resilience": {"success": false, "resilience_results": {"timeout_1s": {"success": false, "error": "", "timeout_exceeded": true}, "timeout_3s": {"success": false, "error": "", "timeout_exceeded": true}, "timeout_5s": {"success": false, "error": "Cannot connect to host httpbin.org:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: unable to get local issuer certificate (_ssl.c:1122)')]", "timeout_exceeded": true}}, "resilience_score": 0.0, "test_time": 1753858432.0643806}, "concurrent_pressure": {"success": true, "concurrent_count": 10, "successful_tasks": 10, "success_rate": 1.0, "total_time_ms": 0.5052089691162109, "avg_task_time_ms": 0.0, "test_time": 1753858432.0648859}, "error_recovery": {"success": true, "recovery_results": {"invalid_exchange": {"handled_gracefully": true, "returned_none": true, "step_size_none": true, "no_exception": true}, "invalid_symbol": {"handled_gracefully": true, "returned_none": true, "step_size_none": true, "no_exception": true}, "invalid_market": {"handled_gracefully": true, "returned_none": true, "step_size_none": true, "no_exception": true}, "empty_symbol": {"handled_gracefully": true, "returned_none": true, "step_size_none": true, "no_exception": true}, "none_symbol": {"handled_gracefully": true, "returned_none": true, "no_exception": true}}, "recovery_score": 1.0, "test_time": 1753858432.0705466}}, "overall_summary": {"overall_score": 0.8333333333333334, "grade": "B+ (良好)", "status": "基本修复", "total_tests": 12, "passed_tests": 10, "failed_tests": 2, "test_duration_seconds": 8.387370109558105, "test_timestamp": 1753858432.0705466, "test_date": "2025-07-30 14:53:52"}}