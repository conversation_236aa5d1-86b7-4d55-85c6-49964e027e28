#!/usr/bin/env python3
"""
系统状态测试脚本
测试缓存+API策略是否正确实现
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.trading_rules_preloader import TradingRulesPreloader
import asyncio

async def test_system():
    """测试系统状态"""
    print("🚀 开始测试系统状态...")
    
    preloader = TradingRulesPreloader()
    
    # 测试RESOLV的交易规则获取
    print("\n🔍 测试RESOLV交易规则获取...")
    
    # 测试缓存优先策略
    rule = preloader.get_trading_rule('bybit', 'RESOLV-USDT', 'spot')
    if rule:
        print(f"✅ Bybit RESOLV-USDT spot: 步长={rule.qty_step}, 价格步长={rule.price_step}")
    else:
        print("❌ 无法获取Bybit RESOLV-USDT spot规则")
    
    rule = preloader.get_trading_rule('gate', 'RESOLV-USDT', 'futures')
    if rule:
        print(f"✅ Gate RESOLV-USDT futures: 步长={rule.qty_step}, 价格步长={rule.price_step}")
    else:
        print("❌ 无法获取Gate RESOLV-USDT futures规则")
    
    # 测试精度格式化
    print("\n🎯 测试精度格式化...")
    formatted = preloader.format_amount('bybit', 'RESOLV-USDT', 'spot', 173.01038)
    print(f"✅ Bybit RESOLV-USDT: 173.01038 → {formatted}")
    
    formatted = preloader.format_amount('gate', 'RESOLV-USDT', 'futures', 173.01038)
    print(f"✅ Gate RESOLV-USDT: 173.01038 → {formatted}")
    
    # 检查缓存统计
    print("\n📊 缓存统计:")
    print(f"缓存命中: {preloader.stats['cache_hits']}")
    print(f"缓存未命中: {preloader.stats['cache_misses']}")
    print(f"规则加载: {preloader.stats['rules_loaded']}")
    
    # 测试API调用策略
    print("\n🔧 测试API调用策略...")
    
    # 测试一个不存在的交易对，看是否使用默认值
    rule = preloader.get_trading_rule('bybit', 'NONEXISTENT-USDT', 'spot')
    if rule:
        print(f"✅ 不存在交易对使用默认值: 步长={rule.qty_step}")
    else:
        print("❌ 不存在交易对未返回默认值")
    
    print("\n🎉 测试完成!")

if __name__ == "__main__":
    asyncio.run(test_system())
