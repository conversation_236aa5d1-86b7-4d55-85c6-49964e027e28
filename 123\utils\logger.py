"""
日志工具模块
为整个项目提供统一的日志处理
"""

import os
import logging

# 日志级别映射
LOG_LEVELS = {
    "DEBUG": logging.DEBUG,
    "INFO": logging.INFO,
    "WARNING": logging.WARNING,
    "ERROR": logging.ERROR,
    "CRITICAL": logging.CRITICAL
}

# 确保日志目录存在
def ensure_log_dir(log_dir: str = "logs"):
    """确保日志目录存在"""
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    return log_dir

# 获取日志器 - 使用统一日志系统
def get_logger(name: str, log_level: str = None) -> logging.Logger:
    """
    获取指定名称的日志器 - 使用统一日志系统

    Args:
        name: 日志器名称
        log_level: 日志级别（已废弃，由统一日志系统管理）

    Returns:
        配置好的日志器
    """
    # 🔥 使用统一日志系统，不再创建独立的日志配置
    logger = logging.getLogger(name)
    logger.setLevel(logging.DEBUG)

    # 🔥 特殊处理：websocket_prices_only需要独立的文件处理器
    if name == 'websocket_prices_only':
        # 确保不重复添加处理器
        if not logger.handlers:
            ensure_log_dir()
            file_handler = logging.FileHandler('logs/websocket_prices.log', encoding='utf-8')
            formatter = logging.Formatter('%(asctime)s.%(msecs)03d %(message)s',
                                        datefmt='%H:%M:%S')
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)
            logger.propagate = False  # 不传播到根日志器，避免重复记录
        return logger

    # 确保日志传播到根日志器，由统一日志系统处理
    logger.propagate = True

    return logger

# 初始化日志系统 - 已废弃，使用统一日志系统
def init_logging(log_level: str = "DEBUG", log_to_file: bool = True):
    """
    🔥 已废弃的日志初始化函数 - 现在使用统一日志系统

    为了向后兼容保留此函数，但不再创建额外的日志文件
    请使用 utils.log_setup.setup_logging() 代替

    Args:
        log_level: 日志级别（已忽略，由统一日志系统管理）
        log_to_file: 是否记录到文件（已忽略，由统一日志系统管理）
    """
    # 🔥 不再创建任何日志处理器，统一由 log_setup.py 管理
    logger = logging.getLogger("logger_deprecated")
    logger.warning("⚠️ init_logging() 已废弃，请使用 utils.log_setup.setup_logging()")

    # 确保所有日志器都传播到根日志器
    common_loggers = [
        "websocket", "exchanges", "trading", "core",
        "fund_management", "monitoring"
    ]

    for logger_name in common_loggers:
        logger = logging.getLogger(logger_name)
        logger.setLevel(logging.DEBUG)
        logger.propagate = True  # 确保传播到统一日志系统

    # 设置第三方库的日志级别
    logging.getLogger("websockets").setLevel(logging.WARNING)
    logging.getLogger("asyncio").setLevel(logging.WARNING)
    logging.getLogger("urllib3").setLevel(logging.WARNING)

# 如果作为主模块运行，进行简单测试
if __name__ == "__main__":
    # 初始化日志系统
    init_logging("DEBUG", True)

    # 获取测试日志器
    logger = get_logger("test_logger")

    # 记录各级别日志
    logger.debug("这是DEBUG级别的日志消息")
    logger.info("这是INFO级别的日志消息")
    logger.warning("这是WARNING级别的日志消息")
    logger.error("这是ERROR级别的日志消息")
    logger.critical("这是CRITICAL级别的日志消息")

    print("日志测试完成，请检查logs目录中的日志文件")