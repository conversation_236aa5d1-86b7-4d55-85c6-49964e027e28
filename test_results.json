{"test_suite": "机构级别高质量测试套件", "start_time": "2025-07-31T18:47:13.684393", "end_time": "2025-07-31T18:47:13.743913", "total_tests": 9, "passed_tests": 4, "failed_tests": 5, "coverage": {"exchanges_tested": 3, "symbols_tested": 4, "market_types_tested": 2, "total_combinations": 24, "test_phases_completed": 3, "critical_functions_covered": ["get_trading_rule", "format_amount", "cache_mechanism", "api_fallback", "concurrent_processing", "extreme_values", "performance_benchmarks"]}, "performance_metrics": {}, "test_phases": {"phase_1_basic": {"status": "completed", "tests": [{"name": "参数输入输出验证", "status": "passed", "duration_ms": 3.98, "timestamp": "2025-07-31T18:47:13.689956", "details": {"rule_found": true, "qty_step": "0.1", "price_step": "0.01", "boundary_check": "passed"}}, {"name": "缓存机制验证", "status": "failed", "duration_ms": 0.0, "timestamp": "2025-07-31T18:47:13.690973", "details": {"error": "缓存命中次数不足: 0/4", "cache_hits": 0, "cache_working": false}}, {"name": "精度格式化验证", "status": "failed", "duration_ms": 0.98, "timestamp": "2025-07-31T18:47:13.691954", "details": {"format_results": {}, "error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "traceback": "Traceback (most recent call last):\n  File \"c:\\Users\\<USER>\\Desktop\\64B 异步调用缺陷和精度错误\\123\\test_comprehensive_system.py\", line 193, in phase_1_basic_core_tests\n    formatted = preloader.format_amount('bybit', 'RESOLV-USDT', 'spot', amount)\nAttributeError: 'TradingRulesPreloader' object has no attribute 'format_amount'\n"}}], "success_rate": 33.33}, "phase_2_system": {"status": "completed", "tests": [{"name": "多交易所一致性测试", "status": "passed", "duration_ms": 23.83, "timestamp": "2025-07-31T18:47:13.716809", "details": {"exchange_results": {"RESOLV-USDT": {"bybit_spot": {"rule_exists": true, "qty_step": "0.1", "price_step": "0.01"}, "bybit_futures": {"rule_exists": true, "qty_step": "0.01", "price_step": "0.01"}, "gate_spot": {"rule_exists": true, "qty_step": "0.1", "price_step": "0.01"}, "gate_futures": {"rule_exists": true, "qty_step": "0.01", "price_step": "0.01"}, "okx_spot": {"rule_exists": true, "qty_step": "0.1", "price_step": "0.01"}, "okx_futures": {"rule_exists": true, "qty_step": "0.01", "price_step": "0.01"}}, "BTC-USDT": {"bybit_spot": {"rule_exists": true, "qty_step": "0.1", "price_step": "0.01"}, "bybit_futures": {"rule_exists": true, "qty_step": "0.01", "price_step": "0.01"}, "gate_spot": {"rule_exists": true, "qty_step": "0.1", "price_step": "0.01"}, "gate_futures": {"rule_exists": true, "qty_step": "0.01", "price_step": "0.01"}, "okx_spot": {"rule_exists": true, "qty_step": "0.1", "price_step": "0.01"}, "okx_futures": {"rule_exists": true, "qty_step": "0.01", "price_step": "0.01"}}, "ETH-USDT": {"bybit_spot": {"rule_exists": true, "qty_step": "0.1", "price_step": "0.01"}, "bybit_futures": {"rule_exists": true, "qty_step": "0.01", "price_step": "0.01"}, "gate_spot": {"rule_exists": true, "qty_step": "0.1", "price_step": "0.01"}, "gate_futures": {"rule_exists": true, "qty_step": "0.01", "price_step": "0.01"}, "okx_spot": {"rule_exists": true, "qty_step": "0.1", "price_step": "0.01"}, "okx_futures": {"rule_exists": true, "qty_step": "0.01", "price_step": "0.01"}}, "SOL-USDT": {"bybit_spot": {"rule_exists": true, "qty_step": "0.1", "price_step": "0.01"}, "bybit_futures": {"rule_exists": true, "qty_step": "0.01", "price_step": "0.01"}, "gate_spot": {"rule_exists": true, "qty_step": "0.1", "price_step": "0.01"}, "gate_futures": {"rule_exists": true, "qty_step": "0.01", "price_step": "0.01"}, "okx_spot": {"rule_exists": true, "qty_step": "0.1", "price_step": "0.01"}, "okx_futures": {"rule_exists": true, "qty_step": "0.01", "price_step": "0.01"}}}}}, {"name": "多币种切换测试", "status": "passed", "duration_ms": 0.0, "timestamp": "2025-07-31T18:47:13.717806", "details": {"switch_results": [{"iteration": 1, "exchange": "bybit", "symbol": "RESOLV-USDT", "market_type": "spot", "success": true, "duration_ms": 0.0}, {"iteration": 2, "exchange": "gate", "symbol": "BTC-USDT", "market_type": "futures", "success": true, "duration_ms": 0.0}, {"iteration": 3, "exchange": "okx", "symbol": "ETH-USDT", "market_type": "spot", "success": true, "duration_ms": 0.0}, {"iteration": 4, "exchange": "bybit", "symbol": "SOL-USDT", "market_type": "futures", "success": true, "duration_ms": 0.0}, {"iteration": 5, "exchange": "gate", "symbol": "RESOLV-USDT", "market_type": "spot", "success": true, "duration_ms": 0.0}, {"iteration": 6, "exchange": "okx", "symbol": "BTC-USDT", "market_type": "futures", "success": true, "duration_ms": 0.0}, {"iteration": 7, "exchange": "bybit", "symbol": "ETH-USDT", "market_type": "spot", "success": true, "duration_ms": 0.0}, {"iteration": 8, "exchange": "gate", "symbol": "SOL-USDT", "market_type": "futures", "success": true, "duration_ms": 0.0}, {"iteration": 9, "exchange": "okx", "symbol": "RESOLV-USDT", "market_type": "spot", "success": true, "duration_ms": 0.0}, {"iteration": 10, "exchange": "bybit", "symbol": "BTC-USDT", "market_type": "futures", "success": true, "duration_ms": 0.0}, {"iteration": 11, "exchange": "gate", "symbol": "ETH-USDT", "market_type": "spot", "success": true, "duration_ms": 0.0}, {"iteration": 12, "exchange": "okx", "symbol": "SOL-USDT", "market_type": "futures", "success": true, "duration_ms": 0.0}, {"iteration": 13, "exchange": "bybit", "symbol": "RESOLV-USDT", "market_type": "spot", "success": true, "duration_ms": 0.0}, {"iteration": 14, "exchange": "gate", "symbol": "BTC-USDT", "market_type": "futures", "success": true, "duration_ms": 0.0}, {"iteration": 15, "exchange": "okx", "symbol": "ETH-USDT", "market_type": "spot", "success": true, "duration_ms": 0.0}, {"iteration": 16, "exchange": "bybit", "symbol": "SOL-USDT", "market_type": "futures", "success": true, "duration_ms": 0.0}, {"iteration": 17, "exchange": "gate", "symbol": "RESOLV-USDT", "market_type": "spot", "success": true, "duration_ms": 0.0}, {"iteration": 18, "exchange": "okx", "symbol": "BTC-USDT", "market_type": "futures", "success": true, "duration_ms": 0.0}, {"iteration": 19, "exchange": "bybit", "symbol": "ETH-USDT", "market_type": "spot", "success": true, "duration_ms": 0.0}, {"iteration": 20, "exchange": "gate", "symbol": "SOL-USDT", "market_type": "futures", "success": true, "duration_ms": 0.0}], "average_switch_time_ms": 0.0}}, {"name": "状态联动测试", "status": "failed", "duration_ms": 0.0, "timestamp": "2025-07-31T18:47:13.719784", "details": {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "traceback": "Traceback (most recent call last):\n  File \"c:\\Users\\<USER>\\Desktop\\64B 异步调用缺陷和精度错误\\123\\test_comprehensive_system.py\", line 350, in phase_2_system_integration_tests\n    formatted = preloader.format_amount('bybit', 'RESOLV-USDT', 'spot', 100.0)\nAttributeError: 'TradingRulesPreloader' object has no attribute 'format_amount'\n"}}], "success_rate": 66.67}, "phase_3_production": {"status": "completed", "tests": [{"name": "并发压力测试", "status": "failed", "duration_ms": 13.24, "timestamp": "2025-07-31T18:47:13.735022", "details": {"concurrent_results": [{"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}, {"error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "success": false}], "concurrent_success_rate": 0.0, "average_duration_ms": 0.0, "total_requests": 100, "successful_requests": 0, "error": "并发成功率过低: 0.00%"}}, {"name": "极限数值测试", "status": "passed", "duration_ms": 0.0, "timestamp": "2025-07-31T18:47:13.738242", "details": {"extreme_value_results": {"1e-06": {"input": 1e-06, "error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "exception_handled": true}, "0.999999": {"input": 0.999999, "error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "exception_handled": true}, "1.000001": {"input": 1.000001, "error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "exception_handled": true}, "999999.999999": {"input": 999999.999999, "error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "exception_handled": true}, "173.01038": {"input": 173.01038, "error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "exception_handled": true}, "0.1": {"input": 0.1, "error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "exception_handled": true}, "inf": {"input": Infinity, "error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "exception_handled": true}, "-1.0": {"input": -1.0, "error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "exception_handled": true}}}}, {"name": "性能基准测试", "status": "failed", "duration_ms": 0.0, "timestamp": "2025-07-31T18:47:13.740891", "details": {"performance_metrics": {}, "error": "'TradingRulesPreloader' object has no attribute 'format_amount'", "traceback": "Traceback (most recent call last):\n  File \"c:\\Users\\<USER>\\Desktop\\64B 异步调用缺陷和精度错误\\123\\test_comprehensive_system.py\", line 572, in phase_3_production_simulation_tests\n    formatted = preloader.format_amount('bybit', 'RESOLV-USDT', 'spot', 173.01038)\nAttributeError: 'TradingRulesPreloader' object has no attribute 'format_amount'\n"}}], "success_rate": 33.33}}, "critical_issues": [], "warnings": [], "final_status": "failed", "overall_success_rate": 44.44}