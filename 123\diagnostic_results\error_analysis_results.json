{"trading_rules": {"total_errors": 53, "error_symbols": {"SPK-USDT": 6, "RESOLV-USDT": 6, "ICNT-USDT": 6, "CAKE-USDT": 6, "WIF-USDT": 6, "AI16Z-USDT": 6, "SOL-USDT": 6, "MATIC-USDT": 6, "okx": 5}, "error_exchanges": {"gate": 16, "bybit": 16, "okx": 16, "ICNT-USDT": 2, "CAKE-USDT": 2, "AI16Z-USDT": 1}, "error_markets": {"spot": 27, "futures": 26}, "error_combinations": {"gate_spot": 8, "gate_futures": 8, "bybit_spot": 8, "bybit_futures": 8, "okx_spot": 8, "okx_futures": 8, "ICNT-USDT_spot": 1, "ICNT-USDT_futures": 1, "CAKE-USDT_spot": 1, "CAKE-USDT_futures": 1, "AI16Z-USDT_spot": 1}, "most_problematic_symbol": ["SPK-USDT", 6], "most_problematic_exchange": ["gate", 16]}, "margin_calculator": {"total_errors": 2, "okx_errors": 2, "okx_symbols": ["ICNT-USDT", "CAKE-USDT"], "other_errors": 0}, "api_errors": {"total_errors": 0, "error_types": {}}, "root_causes": ["OKX保证金计算问题: 2个错误"], "component_status": {"env_config": {"status": "ERROR", "missing_vars": ["TARGET_SYMBOLS", "TRADING_RULES_TTL", "HEDGE_QUALITY_TTL", "PRECISION_CACHE_TTL", "MIN_ORDER_AMOUNT_USD", "MAX_ORDER_AMOUNT_USD"]}, "target_symbols": {"status": "ERROR", "message": "TARGET_SYMBOLS未配置"}, "universal_token_system": {"status": "OK", "supported_symbols": 10}, "trading_rules_preloader": {"status": "OK", "cache_stats": {"trading_rules_count": 0, "hedge_quality_cache_count": 0, "contract_info_cache_count": 0, "unsupported_pairs_count": 0, "cache_hits": 0, "cache_misses": 0, "api_calls": 0, "errors": 0, "expired_cache_entries": 0, "cache_hit_rate": 0.0, "last_preload_time": 0.0, "cache_ttl_hours": 24, "total_rules_loaded": 0}}}, "recommendations": [{"priority": "MEDIUM", "category": "保证金计算", "action": "修复OKX保证金计算逻辑", "details": "检查OKX合约信息获取API，增加错误处理"}, {"priority": "CRITICAL", "category": "环境配置", "action": "配置缺失的环境变量", "details": "添加缺失的环境变量: ['TARGET_SYMBOLS', 'TRADING_RULES_TTL', 'HEDGE_QUALITY_TTL', 'PRECISION_CACHE_TTL', 'MIN_ORDER_AMOUNT_USD', 'MAX_ORDER_AMOUNT_USD']"}, {"priority": "MEDIUM", "category": "系统优化", "action": "实施交易对支持性预检查", "details": "在系统启动时检查所有交易对在各交易所的支持情况"}, {"priority": "LOW", "category": "监控优化", "action": "增强错误监控和告警", "details": "添加更详细的错误分类和自动告警机制"}], "metadata": {"analysis_time": 1753857506.6575618, "analysis_date": "2025-07-30 14:38:26", "version": "1.0"}}