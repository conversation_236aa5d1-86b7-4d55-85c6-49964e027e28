#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
订单配对管理器 - 负责现货和期货订单的逐笔配对管理
从ExecutionEngine中分离出来的专门模块
"""

import time
import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field

from utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class ExecutionOrder:
    """执行订单数据 - 🔥 修复：避免循环导入，在此处定义"""
    order_id: str
    exchange: str
    symbol: str
    side: str  # buy/sell
    amount: float
    price: float
    order_type: str  # market（专注市价单系统）
    status: str = "pending"
    filled: float = 0.0
    average_price: float = 0.0
    timestamp: float = field(default_factory=time.time)
    error: Optional[str] = None


@dataclass
class OrderPair:
    """订单配对数据类"""
    pair_id: str  # 配对ID
    spot_order: ExecutionOrder  # 现货订单
    futures_order: Optional[ExecutionOrder] = None  # 期货订单（可能为None）
    is_matched: bool = False  # 是否配对成功
    quantity_diff: float = 0.0  # 数量差异
    amount_diff: float = 0.0  # 金额差异
    pair_status: str = "pending"  # 配对状态：pending, matched, failed
    created_time: float = field(default_factory=time.time)


class OrderPairingManager:
    """🔥 新增：订单配对管理器 - 专门处理逐笔配对逻辑"""

    def __init__(self, pair_tolerance: float = 0.05):
        self.logger = get_logger(self.__class__.__name__)
        
        # 配对管理
        self.order_pairs: List[OrderPair] = []  # 当前会话的订单配对列表
        self.pair_counter = 0  # 配对计数器
        
        # 配置参数
        self.pair_tolerance = pair_tolerance  # 5%配对容差
        self.pair_timeout = 10.0  # 10秒配对超时

        self.logger.info(f"OrderPairingManager initialized with tolerance={pair_tolerance}")

    def create_order_pair(self, spot_order: ExecutionOrder) -> OrderPair:
        """创建订单配对"""
        try:
            self.pair_counter += 1
            pair_id = f"pair_{self.pair_counter}_{int(time.time())}"
            
            order_pair = OrderPair(
                pair_id=pair_id,
                spot_order=spot_order,
                pair_status="pending"
            )
            
            self.order_pairs.append(order_pair)
            self.logger.info(f"📝 创建订单配对: {pair_id}, 现货订单: {spot_order.order_id}")
            return order_pair
            
        except Exception as e:
            self.logger.error(f"创建订单配对失败: {e}")
            raise

    async def match_futures_order(self, futures_order: ExecutionOrder) -> bool:
        """匹配期货订单到配对"""
        try:
            # 寻找未匹配的现货订单配对
            unmatched_pairs = [pair for pair in self.order_pairs 
                             if pair.futures_order is None and pair.pair_status == "pending"]
            
            if not unmatched_pairs:
                self.logger.warning(f"没有可匹配的现货订单配对: {futures_order.order_id}")
                return False
            
            # 选择最佳匹配（按数量最接近）
            best_pair = None
            min_diff = float('inf')
            
            for pair in unmatched_pairs:
                quantity_diff = abs(pair.spot_order.filled - futures_order.filled)
                if quantity_diff < min_diff:
                    min_diff = quantity_diff
                    best_pair = pair
            
            if best_pair:
                # 执行配对
                best_pair.futures_order = futures_order
                best_pair.quantity_diff = abs(best_pair.spot_order.filled - futures_order.filled)
                best_pair.amount_diff = abs(
                    best_pair.spot_order.filled * best_pair.spot_order.average_price -
                    futures_order.filled * futures_order.average_price
                )
                
                # 验证配对质量
                spot_qty = best_pair.spot_order.filled
                futures_qty = futures_order.filled
                
                if spot_qty > 0:
                    qty_diff_ratio = best_pair.quantity_diff / spot_qty
                    if qty_diff_ratio <= self.pair_tolerance:
                        best_pair.is_matched = True
                        best_pair.pair_status = "matched"
                        self.logger.info(f"✅ 订单配对成功: {best_pair.pair_id} "
                                       f"现货{spot_qty:.6f} ↔️ 期货{futures_qty:.6f}, "
                                       f"差异{qty_diff_ratio:.4f}")
                    else:
                        best_pair.pair_status = "failed"
                        self.logger.warning(f"⚠️ 订单配对质量不佳: {best_pair.pair_id} "
                                          f"差异率{qty_diff_ratio:.4f} > {self.pair_tolerance}")
                else:
                    best_pair.pair_status = "failed"
                    self.logger.error(f"❌ 现货订单数量为0，配对失败: {best_pair.pair_id}")
                
                return best_pair.is_matched
            else:
                self.logger.error(f"无法找到匹配的现货订单: {futures_order.order_id}")
                return False
                
        except Exception as e:
            self.logger.error(f"期货订单配对失败: {e}")
            return False

    async def verify_pairs_completion(self) -> bool:
        """验证订单配对完成度"""
        try:
            if not self.order_pairs:
                self.logger.warning("没有订单配对记录")
                return True  # 没有配对则认为通过
            
            total_pairs = len(self.order_pairs)
            matched_pairs = len([pair for pair in self.order_pairs if pair.is_matched])
            failed_pairs = len([pair for pair in self.order_pairs if pair.pair_status == "failed"])
            pending_pairs = total_pairs - matched_pairs - failed_pairs
            
            match_rate = matched_pairs / total_pairs if total_pairs > 0 else 0
            
            self.logger.info(f"📊 订单配对统计: 总数{total_pairs}, "
                           f"匹配{matched_pairs}, 失败{failed_pairs}, 待定{pending_pairs}, "
                           f"匹配率{match_rate:.2%}")
            
            # 配对成功率阈值
            required_match_rate = 0.8  # 80%
            
            if match_rate >= required_match_rate:
                self.logger.info(f"✅ 订单配对验证通过: {match_rate:.2%} >= {required_match_rate:.2%}")
                return True
            else:
                self.logger.warning(f"⚠️ 订单配对质量不佳: {match_rate:.2%} < {required_match_rate:.2%}")
                return False
                
        except Exception as e:
            self.logger.error(f"订单配对验证异常: {e}")
            return False

    def get_pair_statistics(self) -> Dict:
        """获取配对统计信息"""
        try:
            if not self.order_pairs:
                return {"total": 0, "matched": 0, "failed": 0, "pending": 0, "match_rate": 0.0}
            
            total = len(self.order_pairs)
            matched = len([pair for pair in self.order_pairs if pair.is_matched])
            failed = len([pair for pair in self.order_pairs if pair.pair_status == "failed"])
            pending = total - matched - failed
            match_rate = matched / total if total > 0 else 0.0
            
            return {
                "total": total,
                "matched": matched,
                "failed": failed,
                "pending": pending,
                "match_rate": match_rate,
                "pairs": [
                    {
                        "pair_id": pair.pair_id,
                        "spot_order_id": pair.spot_order.order_id,
                        "futures_order_id": pair.futures_order.order_id if pair.futures_order else None,
                        "status": pair.pair_status,
                        "quantity_diff": pair.quantity_diff,
                        "amount_diff": pair.amount_diff
                    } for pair in self.order_pairs
                ]
            }
            
        except Exception as e:
            self.logger.error(f"获取配对统计失败: {e}")
            return {"error": str(e)}

    def cleanup(self):
        """清理配对状态"""
        try:
            if self.order_pairs:
                pair_stats = self.get_pair_statistics()
                self.logger.info(f"📊 最终配对统计: {pair_stats}")
                
                # 清理配对列表
                self.order_pairs.clear()
                self.pair_counter = 0
                self.logger.info("🧹 订单配对状态已清理")
                
        except Exception as e:
            self.logger.error(f"清理配对状态失败: {e}")

    def export_pair_records(self) -> List[Dict]:
        """导出配对记录，用于ExecutionResult"""
        try:
            return [
                {
                    "pair_id": pair.pair_id,
                    "spot_order_id": pair.spot_order.order_id,
                    "futures_order_id": pair.futures_order.order_id if pair.futures_order else None,
                    "spot_filled": pair.spot_order.filled,
                    "futures_filled": pair.futures_order.filled if pair.futures_order else 0,
                    "quantity_diff": pair.quantity_diff,
                    "amount_diff": pair.amount_diff,
                    "is_matched": pair.is_matched,
                    "status": pair.pair_status
                } for pair in self.order_pairs
            ]
        except Exception as e:
            self.logger.error(f"导出配对记录失败: {e}")
            return []


if __name__ == '__main__':
    """单独运行测试"""
    import asyncio

    async def test_order_pairing():
        print("🧪 测试订单配对管理器...")
        
        manager = OrderPairingManager()
        
        # 创建测试订单
        from core.execution_engine import ExecutionOrder
        
        # 🔥 修复：使用通用代币系统，删除硬编码
        from core.universal_token_system import get_universal_token_system
        token_system = get_universal_token_system()
        test_symbol = token_system.get_supported_symbols()[0] if token_system.get_supported_symbols() else "ADA-USDT"

        spot_order = ExecutionOrder(
            order_id="spot_001",
            exchange="bybit",
            symbol=test_symbol,
            side="buy",
            amount=0.001,
            price=50000.0,
            order_type="market",
            status="filled",
            filled=0.001,
            average_price=50000.0
        )
        
        futures_order = ExecutionOrder(
            order_id="futures_001",
            exchange="gate",
            symbol=test_symbol,  # 使用相同的通用代币
            side="sell",
            amount=0.001,
            price=50100.0,
            order_type="market",
            status="filled",
            filled=0.001,
            average_price=50100.0
        )
        
        # 测试配对流程
        pair = manager.create_order_pair(spot_order)
        print(f"✅ 创建配对: {pair.pair_id}")
        
        match_success = await manager.match_futures_order(futures_order)
        print(f"✅ 配对匹配: {match_success}")
        
        verification = await manager.verify_pairs_completion()
        print(f"✅ 配对验证: {verification}")
        
        stats = manager.get_pair_statistics()
        print(f"📊 配对统计: {stats}")
        
        manager.cleanup()
        print("🧹 清理完成")

    asyncio.run(test_order_pairing()) 