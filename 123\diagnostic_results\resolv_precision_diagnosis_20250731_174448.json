{"timestamp": "2025-07-31T17:44:48.210163", "symbol": "RESOLV-USDT", "exchange": "bybit", "diagnosis": {"api_error": "__init__() missing 2 required positional arguments: 'api_key' and 'api_secret'", "parsing_error": "'TradingRulesPreloader' object has no attribute '_get_bybit_trading_rule'", "root_cause": {"api_vs_system_spot": {"api_basePrecision": null, "system_step_size": null, "match": false}, "api_vs_system_futures": {"api_qtyStep": null, "system_step_size": null, "match": false}}}}