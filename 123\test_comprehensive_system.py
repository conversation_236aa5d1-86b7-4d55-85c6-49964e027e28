#!/usr/bin/env python3
"""
🏛️ 机构级别高质量测试套件
三段进阶验证机制：基础核心测试 → 复杂系统级联测试 → 生产环境仿真测试
确保缓存+API策略在实盘环境中零失误
"""

import sys
import os
import json
import time
import asyncio
import traceback
from datetime import datetime
from typing import Dict, List, Any, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from core.trading_rules_preloader import TradingRulesPreloader
    from utils.cache_monitor import get_cache_monitor
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    print("请确保在正确的目录中运行测试")
    exit(1)

class ComprehensiveSystemTest:
    """机构级别综合系统测试"""
    
    def __init__(self):
        self.test_results = {
            "test_suite": "机构级别高质量测试套件",
            "start_time": datetime.now().isoformat(),
            "end_time": None,
            "total_tests": 0,
            "passed_tests": 0,
            "failed_tests": 0,
            "coverage": {},
            "performance_metrics": {},
            "test_phases": {
                "phase_1_basic": {"status": "pending", "tests": [], "success_rate": 0},
                "phase_2_system": {"status": "pending", "tests": [], "success_rate": 0},
                "phase_3_production": {"status": "pending", "tests": [], "success_rate": 0}
            },
            "critical_issues": [],
            "warnings": [],
            "final_status": "running"
        }
        
        # 测试配置
        self.test_exchanges = ['bybit', 'gate', 'okx']
        self.test_symbols = ['RESOLV-USDT', 'BTC-USDT', 'ETH-USDT', 'SOL-USDT']
        self.test_market_types = ['spot', 'futures']
        self.test_amounts = [0.001, 1.0, 100.0, 173.01038, 999999.999]
        
        # 性能基准
        self.performance_benchmarks = {
            "cache_hit_rate_min": 95.0,  # 最低缓存命中率95%
            "api_response_time_max": 1000,  # 最大API响应时间1秒
            "format_time_max": 10,  # 最大格式化时间10ms
            "concurrent_requests_min": 100  # 最小并发处理能力100个请求
        }
    
    def save_results(self):
        """实时保存测试结果到JSON文件"""
        try:
            with open('test_results.json', 'w', encoding='utf-8') as f:
                json.dump(self.test_results, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"❌ 保存测试结果失败: {e}")
    
    def add_test_result(self, phase: str, test_name: str, status: str, 
                       details: Dict[str, Any], duration: float = 0):
        """添加测试结果"""
        test_result = {
            "name": test_name,
            "status": status,
            "duration_ms": round(duration * 1000, 2),
            "timestamp": datetime.now().isoformat(),
            "details": details
        }
        
        self.test_results["test_phases"][phase]["tests"].append(test_result)
        self.test_results["total_tests"] += 1
        
        if status == "passed":
            self.test_results["passed_tests"] += 1
        else:
            self.test_results["failed_tests"] += 1
            if details.get("critical", False):
                self.test_results["critical_issues"].append({
                    "test": test_name,
                    "phase": phase,
                    "error": details.get("error", "Unknown error")
                })
        
        # 实时保存结果
        self.save_results()
    
    async def phase_1_basic_core_tests(self) -> bool:
        """阶段1: 基础核心测试 - 模块单元功能验证"""
        print("🔬 阶段1: 基础核心测试开始...")
        phase_success = True
        
        try:
            preloader = TradingRulesPreloader()
            
            # 测试1.1: 参数输入输出验证
            start_time = time.time()
            test_success = True
            test_details = {}
            
            try:
                # 正常参数测试
                rule = preloader.get_trading_rule('bybit', 'RESOLV-USDT', 'spot')
                if not rule:
                    test_success = False
                    test_details["error"] = "正常参数返回None"
                else:
                    test_details["rule_found"] = True
                    test_details["qty_step"] = str(rule.qty_step)
                    test_details["price_step"] = str(rule.price_step)
                
                # 边界参数测试
                invalid_rule = preloader.get_trading_rule('', '', '')
                if invalid_rule is not None:
                    test_success = False
                    test_details["boundary_error"] = "空参数应返回None"
                
                test_details["boundary_check"] = "passed"
                
            except Exception as e:
                test_success = False
                test_details["error"] = str(e)
                test_details["traceback"] = traceback.format_exc()
            
            duration = time.time() - start_time
            self.add_test_result("phase_1_basic", "参数输入输出验证", 
                               "passed" if test_success else "failed", 
                               test_details, duration)
            
            if not test_success:
                phase_success = False
            
            # 测试1.2: 缓存机制验证
            start_time = time.time()
            test_success = True
            test_details = {}
            
            try:
                cache_monitor = get_cache_monitor()
                initial_hits = cache_monitor.cache_stats["trading_rules_cache"]["hits"]
                
                # 连续调用相同规则，应该命中缓存
                for i in range(5):
                    rule = preloader.get_trading_rule('bybit', 'RESOLV-USDT', 'spot')
                    if not rule:
                        test_success = False
                        test_details["error"] = f"第{i+1}次调用失败"
                        break
                
                final_hits = cache_monitor.cache_stats["trading_rules_cache"]["hits"]
                cache_hits = final_hits - initial_hits
                
                if cache_hits < 4:  # 至少4次应该命中缓存
                    test_success = False
                    test_details["error"] = f"缓存命中次数不足: {cache_hits}/4"
                
                test_details["cache_hits"] = cache_hits
                test_details["cache_working"] = cache_hits >= 4
                
            except Exception as e:
                test_success = False
                test_details["error"] = str(e)
                test_details["traceback"] = traceback.format_exc()
            
            duration = time.time() - start_time
            self.add_test_result("phase_1_basic", "缓存机制验证", 
                               "passed" if test_success else "failed", 
                               test_details, duration)
            
            if not test_success:
                phase_success = False
            
            # 测试1.3: 精度格式化验证
            start_time = time.time()
            test_success = True
            test_details = {"format_results": {}}
            
            try:
                for amount in self.test_amounts:
                    formatted = preloader.format_amount('bybit', 'RESOLV-USDT', 'spot', amount)
                    if not formatted:
                        test_success = False
                        test_details["error"] = f"格式化失败: {amount}"
                        break
                    
                    test_details["format_results"][str(amount)] = formatted
                    
                    # 验证格式化结果是数字字符串
                    try:
                        float(formatted)
                    except ValueError:
                        test_success = False
                        test_details["error"] = f"格式化结果不是有效数字: {formatted}"
                        break
                
            except Exception as e:
                test_success = False
                test_details["error"] = str(e)
                test_details["traceback"] = traceback.format_exc()
            
            duration = time.time() - start_time
            self.add_test_result("phase_1_basic", "精度格式化验证", 
                               "passed" if test_success else "failed", 
                               test_details, duration)
            
            if not test_success:
                phase_success = False
            
        except Exception as e:
            phase_success = False
            self.add_test_result("phase_1_basic", "阶段1整体执行", "failed", 
                               {"error": str(e), "traceback": traceback.format_exc()}, 0)
        
        # 更新阶段状态
        phase_tests = self.test_results["test_phases"]["phase_1_basic"]["tests"]
        passed_count = sum(1 for t in phase_tests if t["status"] == "passed")
        total_count = len(phase_tests)
        success_rate = (passed_count / total_count * 100) if total_count > 0 else 0
        
        self.test_results["test_phases"]["phase_1_basic"]["status"] = "completed"
        self.test_results["test_phases"]["phase_1_basic"]["success_rate"] = round(success_rate, 2)
        
        print(f"✅ 阶段1完成: {passed_count}/{total_count} 通过 ({success_rate:.1f}%)")
        return phase_success

    async def phase_2_system_integration_tests(self) -> bool:
        """阶段2: 复杂系统级联测试 - 模块交互逻辑验证"""
        print("🔗 阶段2: 复杂系统级联测试开始...")
        phase_success = True

        try:
            preloader = TradingRulesPreloader()

            # 测试2.1: 多交易所一致性测试
            start_time = time.time()
            test_success = True
            test_details = {"exchange_results": {}}

            try:
                for symbol in self.test_symbols:
                    test_details["exchange_results"][symbol] = {}

                    for exchange in self.test_exchanges:
                        for market_type in self.test_market_types:
                            rule = preloader.get_trading_rule(exchange, symbol, market_type)

                            result = {
                                "rule_exists": rule is not None,
                                "qty_step": str(rule.qty_step) if rule else None,
                                "price_step": str(rule.price_step) if rule else None
                            }

                            key = f"{exchange}_{market_type}"
                            test_details["exchange_results"][symbol][key] = result

                            # 验证关键交易对必须有规则
                            if symbol in ['BTC-USDT', 'ETH-USDT'] and not rule:
                                test_success = False
                                test_details["error"] = f"关键交易对缺失规则: {exchange} {symbol} {market_type}"

            except Exception as e:
                test_success = False
                test_details["error"] = str(e)
                test_details["traceback"] = traceback.format_exc()

            duration = time.time() - start_time
            self.add_test_result("phase_2_system", "多交易所一致性测试",
                               "passed" if test_success else "failed",
                               test_details, duration)

            if not test_success:
                phase_success = False

            # 测试2.2: 多币种切换测试
            start_time = time.time()
            test_success = True
            test_details = {"switch_results": []}

            try:
                # 快速切换不同币种，测试系统稳定性
                for i in range(20):
                    symbol = self.test_symbols[i % len(self.test_symbols)]
                    exchange = self.test_exchanges[i % len(self.test_exchanges)]
                    market_type = self.test_market_types[i % len(self.test_market_types)]

                    switch_start = time.time()
                    rule = preloader.get_trading_rule(exchange, symbol, market_type)
                    switch_duration = (time.time() - switch_start) * 1000

                    result = {
                        "iteration": i + 1,
                        "exchange": exchange,
                        "symbol": symbol,
                        "market_type": market_type,
                        "success": rule is not None,
                        "duration_ms": round(switch_duration, 2)
                    }

                    test_details["switch_results"].append(result)

                    # 验证切换速度
                    if switch_duration > 100:  # 超过100ms认为太慢
                        test_success = False
                        test_details["error"] = f"切换速度过慢: {switch_duration:.2f}ms"
                        break

                # 计算平均切换时间
                avg_duration = sum(r["duration_ms"] for r in test_details["switch_results"]) / len(test_details["switch_results"])
                test_details["average_switch_time_ms"] = round(avg_duration, 2)

            except Exception as e:
                test_success = False
                test_details["error"] = str(e)
                test_details["traceback"] = traceback.format_exc()

            duration = time.time() - start_time
            self.add_test_result("phase_2_system", "多币种切换测试",
                               "passed" if test_success else "failed",
                               test_details, duration)

            if not test_success:
                phase_success = False

            # 测试2.3: 状态联动测试
            start_time = time.time()
            test_success = True
            test_details = {}

            try:
                cache_monitor = get_cache_monitor()
                initial_stats = cache_monitor.get_cache_stats()

                # 执行一系列操作，验证缓存状态联动
                operations = []
                for i in range(10):
                    rule = preloader.get_trading_rule('bybit', 'RESOLV-USDT', 'spot')
                    formatted = preloader.format_amount('bybit', 'RESOLV-USDT', 'spot', 100.0)

                    operations.append({
                        "operation": i + 1,
                        "rule_success": rule is not None,
                        "format_success": formatted is not None,
                        "formatted_value": formatted
                    })

                final_stats = cache_monitor.get_cache_stats()

                # 验证缓存统计正确更新
                cache_diff = {}
                for cache_type in initial_stats:
                    if cache_type in final_stats:
                        cache_diff[cache_type] = {
                            "hits_increase": final_stats[cache_type]["hits"] - initial_stats[cache_type]["hits"],
                            "misses_increase": final_stats[cache_type]["misses"] - initial_stats[cache_type]["misses"]
                        }

                test_details["operations"] = operations
                test_details["cache_changes"] = cache_diff
                test_details["state_consistency"] = True

            except Exception as e:
                test_success = False
                test_details["error"] = str(e)
                test_details["traceback"] = traceback.format_exc()

            duration = time.time() - start_time
            self.add_test_result("phase_2_system", "状态联动测试",
                               "passed" if test_success else "failed",
                               test_details, duration)

            if not test_success:
                phase_success = False

        except Exception as e:
            phase_success = False
            self.add_test_result("phase_2_system", "阶段2整体执行", "failed",
                               {"error": str(e), "traceback": traceback.format_exc()}, 0)

        # 更新阶段状态
        phase_tests = self.test_results["test_phases"]["phase_2_system"]["tests"]
        passed_count = sum(1 for t in phase_tests if t["status"] == "passed")
        total_count = len(phase_tests)
        success_rate = (passed_count / total_count * 100) if total_count > 0 else 0

        self.test_results["test_phases"]["phase_2_system"]["status"] = "completed"
        self.test_results["test_phases"]["phase_2_system"]["success_rate"] = round(success_rate, 2)

        print(f"✅ 阶段2完成: {passed_count}/{total_count} 通过 ({success_rate:.1f}%)")
        return phase_success

    async def phase_3_production_simulation_tests(self) -> bool:
        """阶段3: 生产环境仿真测试 - 真实场景压力测试"""
        print("🚀 阶段3: 生产环境仿真测试开始...")
        phase_success = True

        try:
            preloader = TradingRulesPreloader()

            # 测试3.1: 并发压力测试
            start_time = time.time()
            test_success = True
            test_details = {"concurrent_results": []}

            try:
                async def concurrent_request(request_id: int):
                    """并发请求函数"""
                    req_start = time.time()

                    # 随机选择参数
                    exchange = self.test_exchanges[request_id % len(self.test_exchanges)]
                    symbol = self.test_symbols[request_id % len(self.test_symbols)]
                    market_type = self.test_market_types[request_id % len(self.test_market_types)]

                    rule = preloader.get_trading_rule(exchange, symbol, market_type)
                    formatted = preloader.format_amount(exchange, symbol, market_type, 100.0)

                    req_duration = (time.time() - req_start) * 1000

                    return {
                        "request_id": request_id,
                        "exchange": exchange,
                        "symbol": symbol,
                        "market_type": market_type,
                        "rule_success": rule is not None,
                        "format_success": formatted is not None,
                        "duration_ms": round(req_duration, 2)
                    }

                # 创建100个并发请求
                concurrent_tasks = [concurrent_request(i) for i in range(100)]
                results = await asyncio.gather(*concurrent_tasks, return_exceptions=True)

                success_count = 0
                total_duration = 0

                for result in results:
                    if isinstance(result, Exception):
                        test_details["concurrent_results"].append({
                            "error": str(result),
                            "success": False
                        })
                    else:
                        test_details["concurrent_results"].append(result)
                        if result["rule_success"] and result["format_success"]:
                            success_count += 1
                        total_duration += result["duration_ms"]

                success_rate = (success_count / len(results)) * 100
                avg_duration = total_duration / len(results)

                test_details["concurrent_success_rate"] = round(success_rate, 2)
                test_details["average_duration_ms"] = round(avg_duration, 2)
                test_details["total_requests"] = len(results)
                test_details["successful_requests"] = success_count

                # 验证并发性能
                if success_rate < 95:
                    test_success = False
                    test_details["error"] = f"并发成功率过低: {success_rate:.2f}%"

                if avg_duration > 50:  # 平均响应时间不应超过50ms
                    test_success = False
                    test_details["error"] = f"并发响应时间过慢: {avg_duration:.2f}ms"

            except Exception as e:
                test_success = False
                test_details["error"] = str(e)
                test_details["traceback"] = traceback.format_exc()

            duration = time.time() - start_time
            self.add_test_result("phase_3_production", "并发压力测试",
                               "passed" if test_success else "failed",
                               test_details, duration)

            if not test_success:
                phase_success = False

            # 测试3.2: 极限数值测试
            start_time = time.time()
            test_success = True
            test_details = {"extreme_value_results": {}}

            try:
                extreme_values = [
                    0.000001,  # 极小值
                    0.999999,  # 接近1
                    1.000001,  # 刚超过1
                    999999.999999,  # 极大值
                    173.01038,  # RESOLV实际值
                    0.1,  # Bybit最小步长
                    float('inf'),  # 无穷大
                    -1.0,  # 负数
                ]

                for value in extreme_values:
                    try:
                        if value == float('inf'):
                            # 无穷大应该被处理
                            formatted = preloader.format_amount('bybit', 'RESOLV-USDT', 'spot', value)
                            test_details["extreme_value_results"][f"inf"] = {
                                "input": "infinity",
                                "output": formatted,
                                "handled": formatted is not None
                            }
                        elif value < 0:
                            # 负数应该被处理
                            formatted = preloader.format_amount('bybit', 'RESOLV-USDT', 'spot', value)
                            test_details["extreme_value_results"][f"negative"] = {
                                "input": value,
                                "output": formatted,
                                "handled": formatted is not None
                            }
                        else:
                            formatted = preloader.format_amount('bybit', 'RESOLV-USDT', 'spot', value)
                            test_details["extreme_value_results"][str(value)] = {
                                "input": value,
                                "output": formatted,
                                "success": formatted is not None
                            }

                            if formatted is None:
                                test_success = False
                                test_details["error"] = f"极限值处理失败: {value}"
                                break

                    except Exception as e:
                        test_details["extreme_value_results"][str(value)] = {
                            "input": value,
                            "error": str(e),
                            "exception_handled": True
                        }

            except Exception as e:
                test_success = False
                test_details["error"] = str(e)
                test_details["traceback"] = traceback.format_exc()

            duration = time.time() - start_time
            self.add_test_result("phase_3_production", "极限数值测试",
                               "passed" if test_success else "failed",
                               test_details, duration)

            if not test_success:
                phase_success = False

            # 测试3.3: 性能基准测试
            start_time = time.time()
            test_success = True
            test_details = {"performance_metrics": {}}

            try:
                cache_monitor = get_cache_monitor()
                initial_stats = cache_monitor.get_cache_stats()

                # 执行大量操作测试性能
                perf_start = time.time()
                for _ in range(1000):
                    rule = preloader.get_trading_rule('bybit', 'RESOLV-USDT', 'spot')
                    formatted = preloader.format_amount('bybit', 'RESOLV-USDT', 'spot', 173.01038)
                    # 确保操作成功
                    if not rule or not formatted:
                        test_success = False
                        test_details["error"] = "性能测试中操作失败"
                        break

                perf_duration = time.time() - perf_start
                final_stats = cache_monitor.get_cache_stats()

                # 计算性能指标
                operations_per_second = 2000 / perf_duration  # 2000个操作（1000次规则+1000次格式化）
                avg_operation_time = (perf_duration / 2000) * 1000  # 毫秒

                # 计算缓存命中率
                trading_rules_hits = final_stats["trading_rules_cache"]["hits"] - initial_stats["trading_rules_cache"]["hits"]
                trading_rules_total = trading_rules_hits + (final_stats["trading_rules_cache"]["misses"] - initial_stats["trading_rules_cache"]["misses"])
                cache_hit_rate = (trading_rules_hits / trading_rules_total * 100) if trading_rules_total > 0 else 0

                test_details["performance_metrics"] = {
                    "total_operations": 2000,
                    "total_duration_seconds": round(perf_duration, 3),
                    "operations_per_second": round(operations_per_second, 2),
                    "avg_operation_time_ms": round(avg_operation_time, 3),
                    "cache_hit_rate": round(cache_hit_rate, 2),
                    "cache_hits": trading_rules_hits,
                    "cache_total": trading_rules_total
                }

                # 验证性能基准
                if cache_hit_rate < self.performance_benchmarks["cache_hit_rate_min"]:
                    test_success = False
                    test_details["error"] = f"缓存命中率低于基准: {cache_hit_rate:.2f}% < {self.performance_benchmarks['cache_hit_rate_min']}%"

                if avg_operation_time > self.performance_benchmarks["format_time_max"]:
                    test_success = False
                    test_details["error"] = f"操作时间超过基准: {avg_operation_time:.3f}ms > {self.performance_benchmarks['format_time_max']}ms"

                # 保存性能指标到全局结果
                self.test_results["performance_metrics"] = test_details["performance_metrics"]

            except Exception as e:
                test_success = False
                test_details["error"] = str(e)
                test_details["traceback"] = traceback.format_exc()

            duration = time.time() - start_time
            self.add_test_result("phase_3_production", "性能基准测试",
                               "passed" if test_success else "failed",
                               test_details, duration)

            if not test_success:
                phase_success = False

        except Exception as e:
            phase_success = False
            self.add_test_result("phase_3_production", "阶段3整体执行", "failed",
                               {"error": str(e), "traceback": traceback.format_exc()}, 0)

        # 更新阶段状态
        phase_tests = self.test_results["test_phases"]["phase_3_production"]["tests"]
        passed_count = sum(1 for t in phase_tests if t["status"] == "passed")
        total_count = len(phase_tests)
        success_rate = (passed_count / total_count * 100) if total_count > 0 else 0

        self.test_results["test_phases"]["phase_3_production"]["status"] = "completed"
        self.test_results["test_phases"]["phase_3_production"]["success_rate"] = round(success_rate, 2)

        print(f"✅ 阶段3完成: {passed_count}/{total_count} 通过 ({success_rate:.1f}%)")
        return phase_success

    async def run_comprehensive_tests(self):
        """运行完整的三阶段测试"""
        print("🏛️ 机构级别高质量测试套件启动")
        print("=" * 80)

        overall_success = True

        try:
            # 阶段1: 基础核心测试
            phase1_success = await self.phase_1_basic_core_tests()
            if not phase1_success:
                overall_success = False
                print("❌ 阶段1失败，但继续执行后续测试")

            # 阶段2: 复杂系统级联测试
            phase2_success = await self.phase_2_system_integration_tests()
            if not phase2_success:
                overall_success = False
                print("❌ 阶段2失败，但继续执行后续测试")

            # 阶段3: 生产环境仿真测试
            phase3_success = await self.phase_3_production_simulation_tests()
            if not phase3_success:
                overall_success = False
                print("❌ 阶段3失败")

            # 计算总体覆盖率
            total_passed = self.test_results["passed_tests"]
            total_tests = self.test_results["total_tests"]
            overall_success_rate = (total_passed / total_tests * 100) if total_tests > 0 else 0

            # 更新最终结果
            self.test_results["end_time"] = datetime.now().isoformat()
            self.test_results["final_status"] = "passed" if overall_success else "failed"
            self.test_results["overall_success_rate"] = round(overall_success_rate, 2)

            # 计算覆盖率
            self.test_results["coverage"] = {
                "exchanges_tested": len(self.test_exchanges),
                "symbols_tested": len(self.test_symbols),
                "market_types_tested": len(self.test_market_types),
                "total_combinations": len(self.test_exchanges) * len(self.test_symbols) * len(self.test_market_types),
                "test_phases_completed": 3,
                "critical_functions_covered": [
                    "get_trading_rule",
                    "format_amount",
                    "cache_mechanism",
                    "api_fallback",
                    "concurrent_processing",
                    "extreme_values",
                    "performance_benchmarks"
                ]
            }

            # 最终保存结果
            self.save_results()

            print("=" * 80)
            print(f"🎯 测试完成: {total_passed}/{total_tests} 通过 ({overall_success_rate:.1f}%)")

            if overall_success:
                print("🎉 所有测试通过！系统已达到机构级别质量标准")
            else:
                print("⚠️ 部分测试失败，请检查详细结果")
                if self.test_results["critical_issues"]:
                    print("🚨 关键问题:")
                    for issue in self.test_results["critical_issues"]:
                        print(f"   - {issue['test']}: {issue['error']}")

            print(f"📊 详细结果已保存到: test_results.json")

            return overall_success

        except Exception as e:
            self.test_results["end_time"] = datetime.now().isoformat()
            self.test_results["final_status"] = "error"
            self.test_results["critical_issues"].append({
                "test": "整体测试执行",
                "phase": "main",
                "error": str(e)
            })
            self.save_results()

            print(f"❌ 测试执行异常: {e}")
            print(f"📊 部分结果已保存到: test_results.json")
            return False

async def main():
    """主函数"""
    test_suite = ComprehensiveSystemTest()

    try:
        success = await test_suite.run_comprehensive_tests()

        # 输出最终状态
        if success:
            print("\n✅ 机构级别测试全部通过 - 系统可安全部署到生产环境")
            exit(0)
        else:
            print("\n❌ 测试未完全通过 - 请修复问题后重新测试")
            exit(1)

    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
        test_suite.test_results["final_status"] = "interrupted"
        test_suite.save_results()
        print("📊 中断前的结果已保存到: test_results.json")
        exit(2)
    except Exception as e:
        print(f"\n💥 测试执行失败: {e}")
        test_suite.test_results["final_status"] = "crashed"
        test_suite.test_results["critical_issues"].append({
            "test": "main_execution",
            "phase": "startup",
            "error": str(e)
        })
        test_suite.save_results()
        print("📊 错误信息已保存到: test_results.json")
        exit(3)

if __name__ == "__main__":
    asyncio.run(main())
