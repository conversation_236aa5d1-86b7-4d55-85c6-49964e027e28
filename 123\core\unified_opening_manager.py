# -*- coding: utf-8 -*-
"""
🔥 统一开仓管理器 - 按照开仓统一.md设计

核心原则：
1. 动态拉取交易规则 → 提取stepSize和tickSize → 精度用步长推导 → 严格截断参数 → 下单请求
2. 使用现有precision_config.py，避免重复实现
3. 统一接口，最高速度，不影响差价锁定
4. 删除所有硬编码精度
"""

import logging
import time
from typing import Dict, Any, Optional
from dataclasses import dataclass

# 🔥 删除冗余导入：不再需要Decimal, ROUND_DOWN, Tuple, get_calculator

logger = logging.getLogger(__name__)

@dataclass
class OpeningOrderParams:
    """开仓订单参数"""
    symbol: str
    side: str
    order_type: str
    quantity: str  # 格式化后的数量字符串
    price: Optional[str] = None  # 格式化后的价格字符串
    market_type: str = "spot"
    
    # 调试信息
    original_quantity: float = 0.0
    original_price: Optional[float] = None
    step_size: Optional[str] = None
    price_step: Optional[str] = None
    exchange_name: str = ""

@dataclass
class OpeningResult:
    """开仓结果"""
    success: bool
    order_id: Optional[str] = None
    executed_quantity: float = 0.0
    executed_price: float = 0.0
    error_message: Optional[str] = None
    execution_time_ms: float = 0.0
    
    # 调试信息
    params_used: Optional[OpeningOrderParams] = None

class UnifiedOpeningManager:
    """🔥 统一开仓管理器 - 使用预加载交易规则，最高速度"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

        # 🔥 使用交易规则预加载器
        from core.trading_rules_preloader import get_trading_rules_preloader
        self.rules_preloader = get_trading_rules_preloader()

        # 🔥 删除冗余：不再需要precision_calculator和缓存，使用预加载器

        # 性能统计
        self.stats = {
            "total_openings": 0,
            "successful_openings": 0,
            "avg_preparation_time_ms": 0.0,
            "avg_execution_time_ms": 0.0
        }
        
        self.logger.info("✅ 统一开仓管理器初始化完成")
        self.logger.info("   🔥 使用API动态精度，删除硬编码")
        self.logger.info("   🔥 步长缓存机制，最高速度")
        self.logger.info("   🔥 严格截断，不四舍五入")
    
    # 🔥 删除冗余方法：step_to_precision, truncate_by_step, get_trading_rules
    # 这些功能现在由预加载器提供，避免重复实现
    
    async def prepare_opening_params(self,
                                   symbol: str,
                                   side: str,
                                   quantity: float,
                                   price: Optional[float],
                                   exchange,
                                   market_type: str = "spot",
                                   order_type: str = "market") -> Optional[OpeningOrderParams]:
        """
        🔥 核心方法：准备开仓参数 - 使用预加载交易规则，最高速度

        开仓：API步长+精度+高效智能缓存
        """
        start_time = time.time()

        try:
            # 🔥 修复：使用统一的TradingRulesPreloader进行精度处理
            # 避免在UnifiedOpeningManager中重复实现精度逻辑
            exchange_name = self._get_exchange_name(exchange)

            # 🔥 使用实例变量，避免重复调用和作用域问题
            # 🔥 统一精度处理
            order_details = self.rules_preloader.prepare_opening_order(
                exchange_name, symbol, market_type, quantity, price
            )

            if not order_details:
                self.logger.error(f"❌ 无法准备开仓订单: {exchange_name} {symbol} {market_type}")
                # 🔥 修复：提供更详细的错误信息
                self.logger.error(f"❌ 交易所: {exchange_name}, 类型: {type(exchange)}")
                self.logger.error(f"❌ 参数: symbol={symbol}, market_type={market_type}, quantity={quantity}")
                return None

            self.logger.info(f"✅ 开仓订单数据准备成功: {exchange_name} {symbol}")

            # 🔥 创建开仓参数
            params = OpeningOrderParams(
                symbol=symbol,
                side=side,
                order_type=order_type,
                quantity=order_details["quantity"],
                price=order_details["price"],
                market_type=market_type,
                original_quantity=quantity,
                original_price=price,
                step_size=str(order_details["rule"].qty_step),
                price_step=str(order_details["rule"].price_step),
                exchange_name=exchange_name
            )

            # 🔥 性能统计
            preparation_time = (time.time() - start_time) * 1000
            self.stats["avg_preparation_time_ms"] = (
                self.stats["avg_preparation_time_ms"] * self.stats["total_openings"] + preparation_time
            ) / (self.stats["total_openings"] + 1)

            self.logger.info(f"✅ 开仓参数准备完成: {exchange_name} {symbol}")
            self.logger.info(f"   数量: {quantity:.6f} → {order_details['quantity']} (步长: {order_details['rule'].qty_step})")
            if order_details["price"]:
                self.logger.info(f"   价格: {price:.6f} → {order_details['price']} (步长: {order_details['rule'].price_step})")
            self.logger.info(f"   准备耗时: {preparation_time:.2f}ms (预加载缓存)")

            return params

        except Exception as e:
            self.logger.error(f"准备开仓参数失败: {symbol} {side} {quantity} - {e}")
            return None
    
    async def execute_opening_order(self,
                                  params: OpeningOrderParams,
                                  exchange,
                                  orderbook: Optional[Dict] = None) -> OpeningResult:
        """
        🔥 执行开仓订单 - 统一接口
        """
        start_time = time.time()

        try:
            self.stats["total_openings"] += 1

            # 🔥 修复：验证参数完整性
            if not params or not params.symbol:
                error_msg = f"❌ 开仓参数无效: params={params}"
                self.logger.error(error_msg)
                return OpeningResult(
                    success=False,
                    error_message=error_msg,
                    execution_time_ms=(time.time() - start_time) * 1000
                )

            # 🔥 修复：Bybit期货最小订单值检查已移至ExecutionEngine智能协调前处理
            # 此处仅保留基础验证，避免重复检查
            if params.market_type == "futures" and params.exchange_name == "bybit":
                quantity_float = float(params.quantity)

                # 基础数量验证
                if quantity_float <= 0:
                    error_msg = f"❌ Bybit期货订单数量无效: {quantity_float}"
                    self.logger.error(error_msg)
                    return OpeningResult(
                        success=False,
                        error_message=error_msg,
                        execution_time_ms=(time.time() - start_time) * 1000
                    )

                self.logger.info(f"✅ Bybit期货订单基础验证通过: 数量={quantity_float:.6f}")

            # 🔥 构造开仓请求（通用）- 按照开仓统一.md设计
            order_payload = {
                "symbol": params.symbol,
                "side": params.side,
                "order_type": params.order_type,
                "amount": float(params.quantity),  # 交易所接口需要float
                "market_type": params.market_type
            }

            if params.price:
                order_payload["price"] = float(params.price)

            self.logger.info(f"🚀 执行开仓订单: {params.exchange_name} {params.symbol}")
            self.logger.info(f"   参数: {order_payload}")
            self.logger.info(f"   orderbook可用: {orderbook is not None}")

            # 🔥 修复：使用ExecutionEngine中已创建的交易器实例，避免重复创建
            if params.market_type == "spot":
                # 🔥 关键修复：从ExecutionEngine获取已创建的SpotTrader实例
                trader = self._get_spot_trader(exchange)
                if not trader:
                    # 如果无法获取已创建的交易器，则创建新的（兼容性保留）
                    from trading.spot_trader import SpotTrader
                    trader = SpotTrader(exchange)
                    self.logger.warning(f"⚠️ 无法获取已创建的现货交易器，创建新实例")

                if params.side == "buy":
                    order_result = await trader.market_buy(
                        symbol=params.symbol,
                        amount=float(params.quantity),
                        orderbook=orderbook,  # 🔥 修复：传递orderbook参数
                        disable_split=True  # 统一管理器强制禁用拆单
                    )
                else:
                    order_result = await trader.market_sell(
                        symbol=params.symbol,
                        amount=float(params.quantity),
                        orderbook=orderbook,  # 🔥 修复：传递orderbook参数
                        disable_split=True  # 统一管理器强制禁用拆单
                    )
            else:  # futures
                # 🔥 关键修复：从ExecutionEngine获取已创建的FuturesTrader实例
                trader = self._get_futures_trader(exchange)
                if not trader:
                    # 如果无法获取已创建的交易器，则创建新的（兼容性保留）
                    from trading.futures_trader import FuturesTrader
                    trader = FuturesTrader(exchange)
                    self.logger.warning(f"⚠️ 无法获取已创建的期货交易器，创建新实例")

                if params.side == "buy":
                    order_result = await trader.market_buy(
                        symbol=params.symbol,
                        amount=float(params.quantity),
                        orderbook=orderbook,  # 🔥 修复：传递orderbook参数
                        disable_split=True  # 统一管理器强制禁用拆单
                    )
                else:
                    order_result = await trader.market_sell(
                        symbol=params.symbol,
                        amount=float(params.quantity),
                        orderbook=orderbook,  # 🔥 修复：传递orderbook参数
                        disable_split=True  # 统一管理器强制禁用拆单
                    )
            
            # 🔥 解析结果 - 修复：支持多种交易所的订单ID字段名
            order_id = (order_result.get("order_id") or
                       order_result.get("id") or
                       order_result.get("orderId") or
                       order_result.get("order_link_id") or
                       order_result.get("orderLinkId"))

            # 🔥 关键修复：正确处理SpotTrader/FuturesTrader返回的状态
            order_status = order_result.get("status", "")

            # 🔥 修复：明确的成功状态列表
            success_statuses = ["filled", "open", "partially_filled", "new", "success"]
            failed_statuses = ["failed", "rejected", "cancelled", "canceled", "error"]

            # 🔥 关键修复：正确的成功判断逻辑
            is_success = False

            if order_result and order_id:
                if order_status in success_statuses:
                    # 明确的成功状态
                    is_success = True
                    self.logger.info(f"✅ 订单状态明确成功: {order_status}, order_id: {order_id}")
                elif order_status in failed_statuses:
                    # 明确的失败状态
                    is_success = False
                    self.logger.error(f"❌ 订单状态明确失败: {order_status}, order_id: {order_id}")
                elif not order_status:
                    # 没有状态但有订单ID，按成功处理（某些交易所不返回status）
                    is_success = True
                    self.logger.info(f"✅ 无状态但有订单ID，按成功处理: order_id: {order_id}")
                elif order_result.get("success") == True:
                    # 显式的success字段
                    is_success = True
                    self.logger.info(f"✅ 显式success字段为True: order_id: {order_id}")
                else:
                    # 未知状态，按失败处理
                    is_success = False
                    self.logger.warning(f"⚠️ 未知订单状态，按失败处理: {order_status}, order_id: {order_id}")
            else:
                # 没有订单ID或结果为空
                is_success = False
                self.logger.error(f"❌ 缺少订单ID或结果为空: order_result={order_result}")

            if is_success:
                execution_time = (time.time() - start_time) * 1000

                # 🔥 修复：日志记录字段选择逻辑 - 优先选择币数量字段，避免USDT金额字段
                # 字段优先级：filled > executed_quantity > qty > size，避免amount(可能是USDT金额)
                executed_quantity = None
                quantity_fields = ['filled', 'executed_quantity', 'qty', 'size']
                
                for field in quantity_fields:
                    field_value = order_result.get(field)
                    if field_value is not None and field_value > 0:
                        # 🔥 合理性检查：确保是币数量而不是USDT金额
                        test_qty = float(field_value)
                        expected_qty = float(params.quantity)
                        
                        # 如果字段值在合理范围内（±50%），则选择该字段
                        if 0.5 * expected_qty <= test_qty <= 2.0 * expected_qty:
                            executed_quantity = test_qty
                            self.logger.debug(f"🔧 选择数量字段: {field}={executed_quantity:.8f}")
                            break
                        else:
                            self.logger.debug(f"⚠️ 字段{field}={test_qty:.8f}超出合理范围[{0.5*expected_qty:.8f}, {2.0*expected_qty:.8f}]")
                
                # 🔥 兜底：如果所有字段都不合理，使用请求数量
                if executed_quantity is None:
                    executed_quantity = float(params.quantity)
                    self.logger.warning(f"⚠️ 所有数量字段都不合理，使用请求数量: {executed_quantity:.8f}")

                executed_price = (order_result.get("price") or
                                order_result.get("average") or
                                order_result.get("avg_price") or
                                order_result.get("fill_price") or
                                float(params.price or 0))

                result = OpeningResult(
                    success=True,
                    order_id=str(order_id),
                    executed_quantity=float(executed_quantity),
                    executed_price=float(executed_price),
                    execution_time_ms=execution_time,
                    params_used=params
                )
                
                self.stats["successful_openings"] += 1
                self.stats["avg_execution_time_ms"] = (
                    self.stats["avg_execution_time_ms"] * (self.stats["total_openings"] - 1) + execution_time
                ) / self.stats["total_openings"]
                
                self.logger.info(f"✅ 开仓成功: {result.order_id}")
                self.logger.info(f"   执行数量: {result.executed_quantity}")
                self.logger.info(f"   执行价格: {result.executed_price}")
                self.logger.info(f"   执行耗时: {execution_time:.2f}ms")
                
                return result
            else:
                error_msg = f"开仓失败: 无效的订单结果 - {order_result}"
                self.logger.error(error_msg)
                return OpeningResult(
                    success=False,
                    error_message=error_msg,
                    execution_time_ms=(time.time() - start_time) * 1000,
                    params_used=params
                )
                
        except Exception as e:
            error_msg = f"开仓执行异常: {e}"
            self.logger.error(error_msg)
            return OpeningResult(
                success=False,
                error_message=error_msg,
                execution_time_ms=(time.time() - start_time) * 1000,
                params_used=params
            )
    
    async def unified_market_buy(self,
                                    symbol: str,
                                    quantity: float,
                                    exchange,
                                    market_type: str = "spot",
                                    orderbook: Optional[Dict] = None) -> OpeningResult:
        """🔥 统一市价买入接口 - 正确传递orderbook参数"""

        # 🔥 删除：对冲质量检查已在ExecutionEngine中处理，避免重复

        params = await self.prepare_opening_params(
            symbol=symbol,
            side="buy",
            quantity=quantity,
            price=None,
            exchange=exchange,
            market_type=market_type,
            order_type="market"
        )

        if not params:
            return OpeningResult(
                success=False,
                error_message="参数准备失败"
            )

        # 🔥 修复：正确传递orderbook参数给执行方法
        return await self.execute_opening_order(params, exchange, orderbook)

    async def unified_market_sell(self,
                                    symbol: str,
                                    quantity: float,
                                    exchange,
                                    market_type: str = "spot",
                                    orderbook: Optional[Dict] = None) -> OpeningResult:
        """🔥 统一市价卖出接口 - 正确传递orderbook参数"""
        params = await self.prepare_opening_params(
            symbol=symbol,
            side="sell",
            quantity=quantity,
            price=None,
            exchange=exchange,
            market_type=market_type,
            order_type="market"
        )

        if not params:
            return OpeningResult(
                success=False,
                error_message="参数准备失败"
            )

        # 🔥 修复：正确传递orderbook参数给执行方法
        return await self.execute_opening_order(params, exchange, orderbook)

    async def unified_market_buy_open(self,
                                    symbol: str,
                                    quantity: float,
                                    exchange,
                                    market_type: str = "spot",
                                    orderbook: Optional[Dict] = None) -> OpeningResult:
        """🔥 统一市价买入开仓接口 - 修复：正确传递orderbook参数"""
        return await self.unified_market_buy(symbol, quantity, exchange, market_type, orderbook)
    
    async def unified_market_sell_open(self,
                                     symbol: str,
                                     quantity: float,
                                     exchange,
                                     market_type: str = "spot",
                                     orderbook: Optional[Dict] = None) -> OpeningResult:
        """🔥 统一市价卖出开仓接口 - 修复：正确传递orderbook参数"""
        params = await self.prepare_opening_params(
            symbol=symbol,
            side="sell",
            quantity=quantity,
            price=None,
            exchange=exchange,
            market_type=market_type,
            order_type="market"
        )
        
        if not params:
            return OpeningResult(
                success=False,
                error_message="参数准备失败"
            )
        
        # 🔥 修复：正确传递orderbook参数给执行方法
        return await self.execute_opening_order(params, exchange, orderbook)
    
    def _get_spot_trader(self, exchange):
        """
        🔥 获取已创建的现货交易器实例
        避免重复创建SpotTrader
        """
        try:
            # 尝试从ExecutionEngine获取已创建的交易器
            from core.execution_engine import get_execution_engine
            execution_engine = get_execution_engine()

            if hasattr(execution_engine, 'spot_traders') and execution_engine.spot_traders:
                # 🔥 修复：更安全的交易所名称获取方式
                exchange_name = self._get_exchange_name(exchange)
                self.logger.debug(f"🔍 查找现货交易器: {exchange_name}")
                self.logger.debug(f"🔍 可用的现货交易器: {list(execution_engine.spot_traders.keys())}")

                trader = execution_engine.spot_traders.get(exchange_name)
                if trader:
                    self.logger.debug(f"✅ 使用已创建的现货交易器: {exchange_name}")
                    return trader

            self.logger.debug(f"⚠️ 未找到已创建的现货交易器")
            return None

        except Exception as e:
            self.logger.warning(f"⚠️ 获取现货交易器失败: {e}")
            import traceback
            self.logger.debug(f"详细错误: {traceback.format_exc()}")
            return None

    def _get_futures_trader(self, exchange):
        """
        🔥 获取已创建的期货交易器实例
        避免重复创建FuturesTrader
        """
        try:
            # 尝试从ExecutionEngine获取已创建的交易器
            from core.execution_engine import get_execution_engine
            execution_engine = get_execution_engine()

            if hasattr(execution_engine, 'futures_traders') and execution_engine.futures_traders:
                # 🔥 修复：更安全的交易所名称获取方式
                exchange_name = self._get_exchange_name(exchange)
                self.logger.debug(f"🔍 查找期货交易器: {exchange_name}")
                self.logger.debug(f"🔍 可用的期货交易器: {list(execution_engine.futures_traders.keys())}")

                trader = execution_engine.futures_traders.get(exchange_name)
                if trader:
                    self.logger.debug(f"✅ 使用已创建的期货交易器: {exchange_name}")
                    return trader

            self.logger.debug(f"⚠️ 未找到已创建的期货交易器")
            return None

        except Exception as e:
            self.logger.warning(f"⚠️ 获取期货交易器失败: {e}")
            import traceback
            self.logger.debug(f"详细错误: {traceback.format_exc()}")
            return None

    def _get_exchange_name(self, exchange):
        """🔥 使用统一的交易所名称获取函数"""
        from exchanges.exchanges_base import get_exchange_name
        return get_exchange_name(exchange)

    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        success_rate = (self.stats["successful_openings"] / self.stats["total_openings"] * 100) if self.stats["total_openings"] > 0 else 0

        return {
            **self.stats,
            "success_rate": success_rate
        }

# 🔥 全局实例
_opening_manager = None

def get_opening_manager() -> UnifiedOpeningManager:
    """获取统一开仓管理器实例"""
    global _opening_manager
    if _opening_manager is None:
        _opening_manager = UnifiedOpeningManager()
    return _opening_manager
