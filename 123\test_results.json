{"test_suite": "机构级别高质量测试套件", "start_time": "2025-07-31T18:49:06.015312", "end_time": "2025-07-31T18:49:06.269915", "total_tests": 9, "passed_tests": 7, "failed_tests": 2, "coverage": {"exchanges_tested": 3, "symbols_tested": 4, "market_types_tested": 2, "total_combinations": 24, "test_phases_completed": 3, "critical_functions_covered": ["get_trading_rule", "format_amount", "cache_mechanism", "api_fallback", "concurrent_processing", "extreme_values", "performance_benchmarks"]}, "performance_metrics": {"total_operations": 2000, "total_duration_seconds": 0.16, "operations_per_second": 12484.46, "avg_operation_time_ms": 0.08, "cache_hit_rate": 0, "cache_hits": 0, "cache_total": 0}, "test_phases": {"phase_1_basic": {"status": "completed", "tests": [{"name": "参数输入输出验证", "status": "passed", "duration_ms": 4.94, "timestamp": "2025-07-31T18:49:06.022217", "details": {"rule_found": true, "qty_step": "0.1", "price_step": "0.01", "boundary_check": "passed"}}, {"name": "缓存机制验证", "status": "failed", "duration_ms": 0.0, "timestamp": "2025-07-31T18:49:06.023249", "details": {"error": "缓存命中次数不足: 0/4", "cache_hits": 0, "cache_working": false}}, {"name": "精度格式化验证", "status": "passed", "duration_ms": 1.0, "timestamp": "2025-07-31T18:49:06.024244", "details": {"format_results": {"0.001": "0", "1.0": "1", "100.0": "100", "173.01038": "173", "999999.999": "999999.9"}}}], "success_rate": 66.67}, "phase_2_system": {"status": "completed", "tests": [{"name": "多交易所一致性测试", "status": "passed", "duration_ms": 30.52, "timestamp": "2025-07-31T18:49:06.056731", "details": {"exchange_results": {"RESOLV-USDT": {"bybit_spot": {"rule_exists": true, "qty_step": "0.1", "price_step": "0.01"}, "bybit_futures": {"rule_exists": true, "qty_step": "0.01", "price_step": "0.01"}, "gate_spot": {"rule_exists": true, "qty_step": "0.1", "price_step": "0.01"}, "gate_futures": {"rule_exists": true, "qty_step": "0.01", "price_step": "0.01"}, "okx_spot": {"rule_exists": true, "qty_step": "0.1", "price_step": "0.01"}, "okx_futures": {"rule_exists": true, "qty_step": "0.01", "price_step": "0.01"}}, "BTC-USDT": {"bybit_spot": {"rule_exists": true, "qty_step": "0.1", "price_step": "0.01"}, "bybit_futures": {"rule_exists": true, "qty_step": "0.01", "price_step": "0.01"}, "gate_spot": {"rule_exists": true, "qty_step": "0.1", "price_step": "0.01"}, "gate_futures": {"rule_exists": true, "qty_step": "0.01", "price_step": "0.01"}, "okx_spot": {"rule_exists": true, "qty_step": "0.1", "price_step": "0.01"}, "okx_futures": {"rule_exists": true, "qty_step": "0.01", "price_step": "0.01"}}, "ETH-USDT": {"bybit_spot": {"rule_exists": true, "qty_step": "0.1", "price_step": "0.01"}, "bybit_futures": {"rule_exists": true, "qty_step": "0.01", "price_step": "0.01"}, "gate_spot": {"rule_exists": true, "qty_step": "0.1", "price_step": "0.01"}, "gate_futures": {"rule_exists": true, "qty_step": "0.01", "price_step": "0.01"}, "okx_spot": {"rule_exists": true, "qty_step": "0.1", "price_step": "0.01"}, "okx_futures": {"rule_exists": true, "qty_step": "0.01", "price_step": "0.01"}}, "SOL-USDT": {"bybit_spot": {"rule_exists": true, "qty_step": "0.1", "price_step": "0.01"}, "bybit_futures": {"rule_exists": true, "qty_step": "0.01", "price_step": "0.01"}, "gate_spot": {"rule_exists": true, "qty_step": "0.1", "price_step": "0.01"}, "gate_futures": {"rule_exists": true, "qty_step": "0.01", "price_step": "0.01"}, "okx_spot": {"rule_exists": true, "qty_step": "0.1", "price_step": "0.01"}, "okx_futures": {"rule_exists": true, "qty_step": "0.01", "price_step": "0.01"}}}}}, {"name": "多币种切换测试", "status": "passed", "duration_ms": 0.52, "timestamp": "2025-07-31T18:49:06.058282", "details": {"switch_results": [{"iteration": 1, "exchange": "bybit", "symbol": "RESOLV-USDT", "market_type": "spot", "success": true, "duration_ms": 0.0}, {"iteration": 2, "exchange": "gate", "symbol": "BTC-USDT", "market_type": "futures", "success": true, "duration_ms": 0.0}, {"iteration": 3, "exchange": "okx", "symbol": "ETH-USDT", "market_type": "spot", "success": true, "duration_ms": 0.0}, {"iteration": 4, "exchange": "bybit", "symbol": "SOL-USDT", "market_type": "futures", "success": true, "duration_ms": 0.0}, {"iteration": 5, "exchange": "gate", "symbol": "RESOLV-USDT", "market_type": "spot", "success": true, "duration_ms": 0.0}, {"iteration": 6, "exchange": "okx", "symbol": "BTC-USDT", "market_type": "futures", "success": true, "duration_ms": 0.52}, {"iteration": 7, "exchange": "bybit", "symbol": "ETH-USDT", "market_type": "spot", "success": true, "duration_ms": 0.0}, {"iteration": 8, "exchange": "gate", "symbol": "SOL-USDT", "market_type": "futures", "success": true, "duration_ms": 0.0}, {"iteration": 9, "exchange": "okx", "symbol": "RESOLV-USDT", "market_type": "spot", "success": true, "duration_ms": 0.0}, {"iteration": 10, "exchange": "bybit", "symbol": "BTC-USDT", "market_type": "futures", "success": true, "duration_ms": 0.0}, {"iteration": 11, "exchange": "gate", "symbol": "ETH-USDT", "market_type": "spot", "success": true, "duration_ms": 0.0}, {"iteration": 12, "exchange": "okx", "symbol": "SOL-USDT", "market_type": "futures", "success": true, "duration_ms": 0.0}, {"iteration": 13, "exchange": "bybit", "symbol": "RESOLV-USDT", "market_type": "spot", "success": true, "duration_ms": 0.0}, {"iteration": 14, "exchange": "gate", "symbol": "BTC-USDT", "market_type": "futures", "success": true, "duration_ms": 0.0}, {"iteration": 15, "exchange": "okx", "symbol": "ETH-USDT", "market_type": "spot", "success": true, "duration_ms": 0.0}, {"iteration": 16, "exchange": "bybit", "symbol": "SOL-USDT", "market_type": "futures", "success": true, "duration_ms": 0.0}, {"iteration": 17, "exchange": "gate", "symbol": "RESOLV-USDT", "market_type": "spot", "success": true, "duration_ms": 0.0}, {"iteration": 18, "exchange": "okx", "symbol": "BTC-USDT", "market_type": "futures", "success": true, "duration_ms": 0.0}, {"iteration": 19, "exchange": "bybit", "symbol": "ETH-USDT", "market_type": "spot", "success": true, "duration_ms": 0.0}, {"iteration": 20, "exchange": "gate", "symbol": "SOL-USDT", "market_type": "futures", "success": true, "duration_ms": 0.0}], "average_switch_time_ms": 0.03}}, {"name": "状态联动测试", "status": "passed", "duration_ms": 2.04, "timestamp": "2025-07-31T18:49:06.062001", "details": {"operations": [{"operation": 1, "rule_success": true, "format_success": true, "formatted_value": "100"}, {"operation": 2, "rule_success": true, "format_success": true, "formatted_value": "100"}, {"operation": 3, "rule_success": true, "format_success": true, "formatted_value": "100"}, {"operation": 4, "rule_success": true, "format_success": true, "formatted_value": "100"}, {"operation": 5, "rule_success": true, "format_success": true, "formatted_value": "100"}, {"operation": 6, "rule_success": true, "format_success": true, "formatted_value": "100"}, {"operation": 7, "rule_success": true, "format_success": true, "formatted_value": "100"}, {"operation": 8, "rule_success": true, "format_success": true, "formatted_value": "100"}, {"operation": 9, "rule_success": true, "format_success": true, "formatted_value": "100"}, {"operation": 10, "rule_success": true, "format_success": true, "formatted_value": "100"}], "cache_changes": {"balance_cache": {"hits_increase": 0, "misses_increase": 0}, "margin_cache": {"hits_increase": 0, "misses_increase": 0}, "trading_rules_cache": {"hits_increase": 0, "misses_increase": 0}, "hedge_quality_cache": {"hits_increase": 0, "misses_increase": 0}, "precision_cache": {"hits_increase": 0, "misses_increase": 0}}, "state_consistency": true}}], "success_rate": 100.0}, "phase_3_production": {"status": "completed", "tests": [{"name": "并发压力测试", "status": "passed", "duration_ms": 31.66, "timestamp": "2025-07-31T18:49:06.095696", "details": {"concurrent_results": [{"request_id": 0, "exchange": "bybit", "symbol": "RESOLV-USDT", "market_type": "spot", "rule_success": true, "format_success": true, "duration_ms": 0.98}, {"request_id": 1, "exchange": "gate", "symbol": "BTC-USDT", "market_type": "futures", "rule_success": true, "format_success": true, "duration_ms": 1.0}, {"request_id": 2, "exchange": "okx", "symbol": "ETH-USDT", "market_type": "spot", "rule_success": true, "format_success": true, "duration_ms": 2.0}, {"request_id": 3, "exchange": "bybit", "symbol": "SOL-USDT", "market_type": "futures", "rule_success": true, "format_success": true, "duration_ms": 1.0}, {"request_id": 4, "exchange": "gate", "symbol": "RESOLV-USDT", "market_type": "spot", "rule_success": true, "format_success": true, "duration_ms": 1.0}, {"request_id": 5, "exchange": "okx", "symbol": "BTC-USDT", "market_type": "futures", "rule_success": true, "format_success": true, "duration_ms": 1.0}, {"request_id": 6, "exchange": "bybit", "symbol": "ETH-USDT", "market_type": "spot", "rule_success": true, "format_success": true, "duration_ms": 1.0}, {"request_id": 7, "exchange": "gate", "symbol": "SOL-USDT", "market_type": "futures", "rule_success": true, "format_success": true, "duration_ms": 1.0}, {"request_id": 8, "exchange": "okx", "symbol": "RESOLV-USDT", "market_type": "spot", "rule_success": true, "format_success": true, "duration_ms": 1.0}, {"request_id": 9, "exchange": "bybit", "symbol": "BTC-USDT", "market_type": "futures", "rule_success": true, "format_success": true, "duration_ms": 2.0}, {"request_id": 10, "exchange": "gate", "symbol": "ETH-USDT", "market_type": "spot", "rule_success": true, "format_success": true, "duration_ms": 1.0}, {"request_id": 11, "exchange": "okx", "symbol": "SOL-USDT", "market_type": "futures", "rule_success": true, "format_success": true, "duration_ms": 1.0}, {"request_id": 12, "exchange": "bybit", "symbol": "RESOLV-USDT", "market_type": "spot", "rule_success": true, "format_success": true, "duration_ms": 0.0}, {"request_id": 13, "exchange": "gate", "symbol": "BTC-USDT", "market_type": "futures", "rule_success": true, "format_success": true, "duration_ms": 0.99}, {"request_id": 14, "exchange": "okx", "symbol": "ETH-USDT", "market_type": "spot", "rule_success": true, "format_success": true, "duration_ms": 0.0}, {"request_id": 15, "exchange": "bybit", "symbol": "SOL-USDT", "market_type": "futures", "rule_success": true, "format_success": true, "duration_ms": 0.0}, {"request_id": 16, "exchange": "gate", "symbol": "RESOLV-USDT", "market_type": "spot", "rule_success": true, "format_success": true, "duration_ms": 0.0}, {"request_id": 17, "exchange": "okx", "symbol": "BTC-USDT", "market_type": "futures", "rule_success": true, "format_success": true, "duration_ms": 0.0}, {"request_id": 18, "exchange": "bybit", "symbol": "ETH-USDT", "market_type": "spot", "rule_success": true, "format_success": true, "duration_ms": 0.0}, {"request_id": 19, "exchange": "gate", "symbol": "SOL-USDT", "market_type": "futures", "rule_success": true, "format_success": true, "duration_ms": 1.0}, {"request_id": 20, "exchange": "okx", "symbol": "RESOLV-USDT", "market_type": "spot", "rule_success": true, "format_success": true, "duration_ms": 0.0}, {"request_id": 21, "exchange": "bybit", "symbol": "BTC-USDT", "market_type": "futures", "rule_success": true, "format_success": true, "duration_ms": 0.0}, {"request_id": 22, "exchange": "gate", "symbol": "ETH-USDT", "market_type": "spot", "rule_success": true, "format_success": true, "duration_ms": 0.0}, {"request_id": 23, "exchange": "okx", "symbol": "SOL-USDT", "market_type": "futures", "rule_success": true, "format_success": true, "duration_ms": 0.0}, {"request_id": 24, "exchange": "bybit", "symbol": "RESOLV-USDT", "market_type": "spot", "rule_success": true, "format_success": true, "duration_ms": 0.0}, {"request_id": 25, "exchange": "gate", "symbol": "BTC-USDT", "market_type": "futures", "rule_success": true, "format_success": true, "duration_ms": 1.0}, {"request_id": 26, "exchange": "okx", "symbol": "ETH-USDT", "market_type": "spot", "rule_success": true, "format_success": true, "duration_ms": 0.0}, {"request_id": 27, "exchange": "bybit", "symbol": "SOL-USDT", "market_type": "futures", "rule_success": true, "format_success": true, "duration_ms": 0.0}, {"request_id": 28, "exchange": "gate", "symbol": "RESOLV-USDT", "market_type": "spot", "rule_success": true, "format_success": true, "duration_ms": 0.0}, {"request_id": 29, "exchange": "okx", "symbol": "BTC-USDT", "market_type": "futures", "rule_success": true, "format_success": true, "duration_ms": 0.0}, {"request_id": 30, "exchange": "bybit", "symbol": "ETH-USDT", "market_type": "spot", "rule_success": true, "format_success": true, "duration_ms": 0.0}, {"request_id": 31, "exchange": "gate", "symbol": "SOL-USDT", "market_type": "futures", "rule_success": true, "format_success": true, "duration_ms": 1.0}, {"request_id": 32, "exchange": "okx", "symbol": "RESOLV-USDT", "market_type": "spot", "rule_success": true, "format_success": true, "duration_ms": 0.0}, {"request_id": 33, "exchange": "bybit", "symbol": "BTC-USDT", "market_type": "futures", "rule_success": true, "format_success": true, "duration_ms": 0.0}, {"request_id": 34, "exchange": "gate", "symbol": "ETH-USDT", "market_type": "spot", "rule_success": true, "format_success": true, "duration_ms": 0.0}, {"request_id": 35, "exchange": "okx", "symbol": "SOL-USDT", "market_type": "futures", "rule_success": true, "format_success": true, "duration_ms": 0.0}, {"request_id": 36, "exchange": "bybit", "symbol": "RESOLV-USDT", "market_type": "spot", "rule_success": true, "format_success": true, "duration_ms": 0.0}, {"request_id": 37, "exchange": "gate", "symbol": "BTC-USDT", "market_type": "futures", "rule_success": true, "format_success": true, "duration_ms": 0.0}, {"request_id": 38, "exchange": "okx", "symbol": "ETH-USDT", "market_type": "spot", "rule_success": true, "format_success": true, "duration_ms": 1.0}, {"request_id": 39, "exchange": "bybit", "symbol": "SOL-USDT", "market_type": "futures", "rule_success": true, "format_success": true, "duration_ms": 0.0}, {"request_id": 40, "exchange": "gate", "symbol": "RESOLV-USDT", "market_type": "spot", "rule_success": true, "format_success": true, "duration_ms": 0.0}, {"request_id": 41, "exchange": "okx", "symbol": "BTC-USDT", "market_type": "futures", "rule_success": true, "format_success": true, "duration_ms": 0.0}, {"request_id": 42, "exchange": "bybit", "symbol": "ETH-USDT", "market_type": "spot", "rule_success": true, "format_success": true, "duration_ms": 0.0}, {"request_id": 43, "exchange": "gate", "symbol": "SOL-USDT", "market_type": "futures", "rule_success": true, "format_success": true, "duration_ms": 0.0}, {"request_id": 44, "exchange": "okx", "symbol": "RESOLV-USDT", "market_type": "spot", "rule_success": true, "format_success": true, "duration_ms": 1.0}, {"request_id": 45, "exchange": "bybit", "symbol": "BTC-USDT", "market_type": "futures", "rule_success": true, "format_success": true, "duration_ms": 0.0}, {"request_id": 46, "exchange": "gate", "symbol": "ETH-USDT", "market_type": "spot", "rule_success": true, "format_success": true, "duration_ms": 0.0}, {"request_id": 47, "exchange": "okx", "symbol": "SOL-USDT", "market_type": "futures", "rule_success": true, "format_success": true, "duration_ms": 0.0}, {"request_id": 48, "exchange": "bybit", "symbol": "RESOLV-USDT", "market_type": "spot", "rule_success": true, "format_success": true, "duration_ms": 0.0}, {"request_id": 49, "exchange": "gate", "symbol": "BTC-USDT", "market_type": "futures", "rule_success": true, "format_success": true, "duration_ms": 0.0}, {"request_id": 50, "exchange": "okx", "symbol": "ETH-USDT", "market_type": "spot", "rule_success": true, "format_success": true, "duration_ms": 0.0}, {"request_id": 51, "exchange": "bybit", "symbol": "SOL-USDT", "market_type": "futures", "rule_success": true, "format_success": true, "duration_ms": 0.0}, {"request_id": 52, "exchange": "gate", "symbol": "RESOLV-USDT", "market_type": "spot", "rule_success": true, "format_success": true, "duration_ms": 0.0}, {"request_id": 53, "exchange": "okx", "symbol": "BTC-USDT", "market_type": "futures", "rule_success": true, "format_success": true, "duration_ms": 0.0}, {"request_id": 54, "exchange": "bybit", "symbol": "ETH-USDT", "market_type": "spot", "rule_success": true, "format_success": true, "duration_ms": 0.0}, {"request_id": 55, "exchange": "gate", "symbol": "SOL-USDT", "market_type": "futures", "rule_success": true, "format_success": true, "duration_ms": 1.0}, {"request_id": 56, "exchange": "okx", "symbol": "RESOLV-USDT", "market_type": "spot", "rule_success": true, "format_success": true, "duration_ms": 0.0}, {"request_id": 57, "exchange": "bybit", "symbol": "BTC-USDT", "market_type": "futures", "rule_success": true, "format_success": true, "duration_ms": 0.0}, {"request_id": 58, "exchange": "gate", "symbol": "ETH-USDT", "market_type": "spot", "rule_success": true, "format_success": true, "duration_ms": 0.0}, {"request_id": 59, "exchange": "okx", "symbol": "SOL-USDT", "market_type": "futures", "rule_success": true, "format_success": true, "duration_ms": 0.0}, {"request_id": 60, "exchange": "bybit", "symbol": "RESOLV-USDT", "market_type": "spot", "rule_success": true, "format_success": true, "duration_ms": 0.0}, {"request_id": 61, "exchange": "gate", "symbol": "BTC-USDT", "market_type": "futures", "rule_success": true, "format_success": true, "duration_ms": 1.0}, {"request_id": 62, "exchange": "okx", "symbol": "ETH-USDT", "market_type": "spot", "rule_success": true, "format_success": true, "duration_ms": 0.0}, {"request_id": 63, "exchange": "bybit", "symbol": "SOL-USDT", "market_type": "futures", "rule_success": true, "format_success": true, "duration_ms": 0.0}, {"request_id": 64, "exchange": "gate", "symbol": "RESOLV-USDT", "market_type": "spot", "rule_success": true, "format_success": true, "duration_ms": 0.0}, {"request_id": 65, "exchange": "okx", "symbol": "BTC-USDT", "market_type": "futures", "rule_success": true, "format_success": true, "duration_ms": 0.0}, {"request_id": 66, "exchange": "bybit", "symbol": "ETH-USDT", "market_type": "spot", "rule_success": true, "format_success": true, "duration_ms": 0.0}, {"request_id": 67, "exchange": "gate", "symbol": "SOL-USDT", "market_type": "futures", "rule_success": true, "format_success": true, "duration_ms": 1.0}, {"request_id": 68, "exchange": "okx", "symbol": "RESOLV-USDT", "market_type": "spot", "rule_success": true, "format_success": true, "duration_ms": 0.0}, {"request_id": 69, "exchange": "bybit", "symbol": "BTC-USDT", "market_type": "futures", "rule_success": true, "format_success": true, "duration_ms": 0.0}, {"request_id": 70, "exchange": "gate", "symbol": "ETH-USDT", "market_type": "spot", "rule_success": true, "format_success": true, "duration_ms": 0.0}, {"request_id": 71, "exchange": "okx", "symbol": "SOL-USDT", "market_type": "futures", "rule_success": true, "format_success": true, "duration_ms": 0.0}, {"request_id": 72, "exchange": "bybit", "symbol": "RESOLV-USDT", "market_type": "spot", "rule_success": true, "format_success": true, "duration_ms": 0.0}, {"request_id": 73, "exchange": "gate", "symbol": "BTC-USDT", "market_type": "futures", "rule_success": true, "format_success": true, "duration_ms": 1.0}, {"request_id": 74, "exchange": "okx", "symbol": "ETH-USDT", "market_type": "spot", "rule_success": true, "format_success": true, "duration_ms": 0.0}, {"request_id": 75, "exchange": "bybit", "symbol": "SOL-USDT", "market_type": "futures", "rule_success": true, "format_success": true, "duration_ms": 0.0}, {"request_id": 76, "exchange": "gate", "symbol": "RESOLV-USDT", "market_type": "spot", "rule_success": true, "format_success": true, "duration_ms": 0.0}, {"request_id": 77, "exchange": "okx", "symbol": "BTC-USDT", "market_type": "futures", "rule_success": true, "format_success": true, "duration_ms": 0.0}, {"request_id": 78, "exchange": "bybit", "symbol": "ETH-USDT", "market_type": "spot", "rule_success": true, "format_success": true, "duration_ms": 1.0}, {"request_id": 79, "exchange": "gate", "symbol": "SOL-USDT", "market_type": "futures", "rule_success": true, "format_success": true, "duration_ms": 0.0}, {"request_id": 80, "exchange": "okx", "symbol": "RESOLV-USDT", "market_type": "spot", "rule_success": true, "format_success": true, "duration_ms": 0.0}, {"request_id": 81, "exchange": "bybit", "symbol": "BTC-USDT", "market_type": "futures", "rule_success": true, "format_success": true, "duration_ms": 0.0}, {"request_id": 82, "exchange": "gate", "symbol": "ETH-USDT", "market_type": "spot", "rule_success": true, "format_success": true, "duration_ms": 0.0}, {"request_id": 83, "exchange": "okx", "symbol": "SOL-USDT", "market_type": "futures", "rule_success": true, "format_success": true, "duration_ms": 1.0}, {"request_id": 84, "exchange": "bybit", "symbol": "RESOLV-USDT", "market_type": "spot", "rule_success": true, "format_success": true, "duration_ms": 0.0}, {"request_id": 85, "exchange": "gate", "symbol": "BTC-USDT", "market_type": "futures", "rule_success": true, "format_success": true, "duration_ms": 0.0}, {"request_id": 86, "exchange": "okx", "symbol": "ETH-USDT", "market_type": "spot", "rule_success": true, "format_success": true, "duration_ms": 0.0}, {"request_id": 87, "exchange": "bybit", "symbol": "SOL-USDT", "market_type": "futures", "rule_success": true, "format_success": true, "duration_ms": 1.0}, {"request_id": 88, "exchange": "gate", "symbol": "RESOLV-USDT", "market_type": "spot", "rule_success": true, "format_success": true, "duration_ms": 0.2}, {"request_id": 89, "exchange": "okx", "symbol": "BTC-USDT", "market_type": "futures", "rule_success": true, "format_success": true, "duration_ms": 0.0}, {"request_id": 90, "exchange": "bybit", "symbol": "ETH-USDT", "market_type": "spot", "rule_success": true, "format_success": true, "duration_ms": 0.51}, {"request_id": 91, "exchange": "gate", "symbol": "SOL-USDT", "market_type": "futures", "rule_success": true, "format_success": true, "duration_ms": 0.0}, {"request_id": 92, "exchange": "okx", "symbol": "RESOLV-USDT", "market_type": "spot", "rule_success": true, "format_success": true, "duration_ms": 0.0}, {"request_id": 93, "exchange": "bybit", "symbol": "BTC-USDT", "market_type": "futures", "rule_success": true, "format_success": true, "duration_ms": 0.0}, {"request_id": 94, "exchange": "gate", "symbol": "ETH-USDT", "market_type": "spot", "rule_success": true, "format_success": true, "duration_ms": 0.0}, {"request_id": 95, "exchange": "okx", "symbol": "SOL-USDT", "market_type": "futures", "rule_success": true, "format_success": true, "duration_ms": 1.01}, {"request_id": 96, "exchange": "bybit", "symbol": "RESOLV-USDT", "market_type": "spot", "rule_success": true, "format_success": true, "duration_ms": 0.0}, {"request_id": 97, "exchange": "gate", "symbol": "BTC-USDT", "market_type": "futures", "rule_success": true, "format_success": true, "duration_ms": 0.0}, {"request_id": 98, "exchange": "okx", "symbol": "ETH-USDT", "market_type": "spot", "rule_success": true, "format_success": true, "duration_ms": 0.0}, {"request_id": 99, "exchange": "bybit", "symbol": "SOL-USDT", "market_type": "futures", "rule_success": true, "format_success": true, "duration_ms": 0.0}], "concurrent_success_rate": 100.0, "average_duration_ms": 0.29, "total_requests": 100, "successful_requests": 100}}, {"name": "极限数值测试", "status": "passed", "duration_ms": 0.99, "timestamp": "2025-07-31T18:49:06.100721", "details": {"extreme_value_results": {"1e-06": {"input": 1e-06, "output": "0", "success": true}, "0.999999": {"input": 0.999999, "output": "0.9", "success": true}, "1.000001": {"input": 1.000001, "output": "1", "success": true}, "999999.999999": {"input": 999999.999999, "output": "999999.9", "success": true}, "173.01038": {"input": 173.01038, "output": "173", "success": true}, "0.1": {"input": 0.1, "output": "0.1", "success": true}, "inf": {"input": Infinity, "error": "cannot convert float infinity to integer", "exception_handled": true}, "negative": {"input": -1.0, "output": "-1", "handled": true}}}}, {"name": "性能基准测试", "status": "failed", "duration_ms": 160.2, "timestamp": "2025-07-31T18:49:06.264918", "details": {"performance_metrics": {"total_operations": 2000, "total_duration_seconds": 0.16, "operations_per_second": 12484.46, "avg_operation_time_ms": 0.08, "cache_hit_rate": 0, "cache_hits": 0, "cache_total": 0}, "error": "缓存命中率低于基准: 0.00% < 95.0%"}}], "success_rate": 66.67}}, "critical_issues": [], "warnings": [], "final_status": "failed", "overall_success_rate": 77.78}