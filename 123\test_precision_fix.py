#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 精度修复验证测试
验证_get_precision_from_exchange_api_sync方法的修复效果
"""

import sys
import os
import asyncio
import logging
from typing import Dict, Any

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入核心模块
from core.trading_rules_preloader import get_trading_rules_preloader
from config.settings import get_config

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MockExchange:
    """模拟交易所用于测试"""
    def __init__(self, exchange_name: str):
        self.exchange_name = exchange_name
        self._class_name = f"{exchange_name.capitalize()}Exchange"
    
    def __class__(self):
        class MockClass:
            def __name__(self):
                return self._class_name
        return MockClass()

def test_precision_retrieval():
    """测试精度获取功能"""
    logger.info("🚀 开始测试精度获取修复...")
    
    # 获取预加载器实例
    preloader = get_trading_rules_preloader()
    
    # 测试用例
    test_cases = [
        {"exchange": "bybit", "symbol": "SPK-USDT", "market_type": "spot"},
        {"exchange": "bybit", "symbol": "ICNT-USDT", "market_type": "futures"},
        {"exchange": "gate", "symbol": "SPK-USDT", "market_type": "spot"},
        {"exchange": "okx", "symbol": "SPK-USDT", "market_type": "futures"},
    ]
    
    results = []
    
    for test_case in test_cases:
        try:
            logger.info(f"📊 测试: {test_case['exchange']} {test_case['symbol']} {test_case['market_type']}")
            
            # 创建模拟交易所实例
            mock_exchange = MockExchange(test_case['exchange'])
            
            # 调用修复后的方法
            precision_info = preloader._get_precision_from_exchange_api_sync(
                mock_exchange, 
                test_case['symbol'], 
                test_case['market_type']
            )
            
            if precision_info:
                logger.info(f"✅ 获取成功:")
                logger.info(f"   步长: {precision_info.get('step_size')}")
                logger.info(f"   最小数量: {precision_info.get('min_amount')}")
                logger.info(f"   精度: {precision_info.get('amount_precision')}")
                logger.info(f"   来源: {precision_info.get('source')}")
                
                results.append({
                    "test_case": test_case,
                    "success": True,
                    "precision_info": precision_info
                })
            else:
                logger.warning(f"⚠️ 获取失败")
                results.append({
                    "test_case": test_case,
                    "success": False,
                    "precision_info": None
                })
                
        except Exception as e:
            logger.error(f"❌ 测试异常: {e}")
            results.append({
                "test_case": test_case,
                "success": False,
                "error": str(e)
            })
    
    # 输出测试结果统计
    success_count = sum(1 for r in results if r.get("success", False))
    total_count = len(results)
    
    logger.info(f"\n🎯 测试结果统计:")
    logger.info(f"   总测试数: {total_count}")
    logger.info(f"   成功数: {success_count}")
    logger.info(f"   失败数: {total_count - success_count}")
    logger.info(f"   成功率: {success_count/total_count*100:.1f}%")
    
    return results

def test_trading_rule_creation():
    """测试交易规则创建"""
    logger.info("\n🔧 测试交易规则创建...")
    
    preloader = get_trading_rules_preloader()
    
    test_symbols = ["SPK-USDT", "ICNT-USDT"]
    test_exchanges = ["bybit", "gate", "okx"]
    test_market_types = ["spot", "futures"]
    
    success_count = 0
    total_count = 0
    
    for symbol in test_symbols:
        for exchange in test_exchanges:
            for market_type in test_market_types:
                total_count += 1
                try:
                    # 尝试获取交易规则
                    rule = preloader.get_trading_rule(exchange, symbol, market_type)
                    
                    if rule:
                        logger.info(f"✅ {exchange} {symbol} {market_type}: 步长={rule.qty_step}")
                        success_count += 1
                        
                        # 测试格式化功能
                        test_amount = 123.456789
                        formatted = preloader.format_amount_unified(test_amount, exchange, symbol, market_type)
                        logger.info(f"   格式化测试: {test_amount} → {formatted}")
                        
                    else:
                        logger.warning(f"⚠️ {exchange} {symbol} {market_type}: 规则获取失败")
                        
                except Exception as e:
                    logger.error(f"❌ {exchange} {symbol} {market_type}: {e}")
    
    logger.info(f"\n📊 交易规则测试统计:")
    logger.info(f"   总测试数: {total_count}")
    logger.info(f"   成功数: {success_count}")
    logger.info(f"   成功率: {success_count/total_count*100:.1f}%")

def test_bybit_trailing_zero_fix():
    """测试Bybit尾随零修复"""
    logger.info("\n🔧 测试Bybit尾随零修复...")
    
    preloader = get_trading_rules_preloader()
    
    test_cases = [
        {"value": "123.000000", "step_size": 0.001, "expected": "123"},
        {"value": "123.456000", "step_size": 0.001, "expected": "123.456"},
        {"value": "123.100000", "step_size": 0.1, "expected": "123.1"},
        {"value": "100.000000", "step_size": 1.0, "expected": "100"},
    ]
    
    for test_case in test_cases:
        try:
            result = preloader._apply_bybit_trailing_zero_fix(
                test_case["value"], 
                test_case["step_size"]
            )
            
            if result == test_case["expected"]:
                logger.info(f"✅ {test_case['value']} → {result} (预期: {test_case['expected']})")
            else:
                logger.warning(f"⚠️ {test_case['value']} → {result} (预期: {test_case['expected']})")
                
        except Exception as e:
            logger.error(f"❌ 尾随零修复测试失败: {e}")

if __name__ == "__main__":
    logger.info("🚀 开始精度修复验证测试...")
    
    try:
        # 测试1: 精度获取
        test_precision_retrieval()
        
        # 测试2: 交易规则创建
        test_trading_rule_creation()
        
        # 测试3: Bybit尾随零修复
        test_bybit_trailing_zero_fix()
        
        logger.info("\n✅ 所有测试完成！")
        
    except Exception as e:
        logger.error(f"❌ 测试执行失败: {e}")
        sys.exit(1)