# UniLendRecord

Interest Record
## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**currency** | **str** | Currency name | [optional] [readonly] 
**amount** | **str** | current amount | [optional] [readonly] 
**last_wallet_amount** | **str** | Last wallet amount | [optional] [readonly] 
**last_lent_amount** | **str** | Last lent amount | [optional] [readonly] 
**last_frozen_amount** | **str** | Last frozen amount | [optional] [readonly] 
**type** | **str** | Record type: lend - lend, redeem - redeem | [optional] [readonly] 
**create_time** | **int** | Created time | [optional] [readonly] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


